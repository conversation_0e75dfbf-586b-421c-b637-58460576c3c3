import { useState, useEffect, useCallback, useRef } from 'react';

// 动画状态枚举
export enum SplashAnimationPhase {
  LOADING = 'loading',
  LOADED = 'loaded',
  HIDDEN = 'hidden'
}

// 动画状态接口
export interface SplashAnimationState {
  phase: SplashAnimationPhase;
  progress: number;
  isVisible: boolean;
  hasError: boolean;
}

// 加载任务接口
export interface LoadingTask {
  id: string;
  name: string;
  loader: () => Promise<any>;
  weight: number; // 该任务在数据加载中的权重
}

// Hook配置接口
export interface SplashAnimationConfig {
  duration?: number;
  showTime?: number;
  progressStep?: number;
  autoHide?: boolean;
  maxTimeout?: number;
  // 新增动态加载配置
  loadingTasks?: LoadingTask[];
  animationWeight?: number; // 动画在总进度中的权重（0-1）
  dataWeight?: number;      // 数据加载在总进度中的权重（0-1）
  minDuration?: number;     // 最小显示时间
  enableDynamicLoading?: boolean; // 是否启用动态加载
  onPhaseChange?: (phase: SplashAnimationPhase) => void;
  onProgressChange?: (progress: number) => void;
  onComplete?: () => void;
  onError?: (error: Error) => void;
  onTimeout?: () => void;
}

// 默认配置
const DEFAULT_CONFIG: Required<SplashAnimationConfig> = {
  duration: 3000,
  showTime: 1500,
  progressStep: 2,
  autoHide: true,
  maxTimeout: 12000,
  // 新增默认配置
  loadingTasks: [],
  animationWeight: 0.3,
  dataWeight: 0.7,
  minDuration: 1500,
  enableDynamicLoading: false,
  onPhaseChange: () => {},
  onProgressChange: () => {},
  onComplete: () => {},
  onError: () => {},
  onTimeout: () => {}
};

/**
 * 开屏动画Hook
 * 控制开屏动画的显示时机、管理动画状态、提供动画完成回调
 * 支持动态加载模式和传统固定时间模式
 */
export const useSplashAnimation = (userConfig?: SplashAnimationConfig) => {
  const config = { ...DEFAULT_CONFIG, ...userConfig };
  const {
    duration,
    showTime,
    progressStep,
    autoHide,
    maxTimeout,
    loadingTasks,
    animationWeight,
    dataWeight,
    minDuration,
    enableDynamicLoading,
    onPhaseChange,
    onProgressChange,
    onComplete,
    onError,
    onTimeout
  } = config;

  // 状态管理
  const [animationState, setAnimationState] = useState<SplashAnimationState>({
    phase: SplashAnimationPhase.LOADING,
    progress: 0,
    isVisible: true,
    hasError: false
  });

  // 动态加载状态
  const [dataProgress, setDataProgress] = useState(0);
  const [loadingStatus, setLoadingStatus] = useState<Record<string, boolean>>({});
  const [startTime] = useState(Date.now());

  // 引用管理
  const progressIntervalRef = useRef<NodeJS.Timeout | null>(null);
  const hideTimerRef = useRef<NodeJS.Timeout | null>(null);
  const timeoutTimerRef = useRef<NodeJS.Timeout | null>(null);
  const isMountedRef = useRef(true);
  const completedTasksRef = useRef<Set<string>>(new Set());
  const animationFrameRef = useRef<number | null>(null);

  // 关键修复：使用 ref 保存回调函数，避免依赖变化
  const callbacksRef = useRef({
    onPhaseChange,
    onProgressChange,
    onComplete,
    onError,
    onTimeout
  });

  // 更新回调函数引用
  useEffect(() => {
    callbacksRef.current = {
      onPhaseChange,
      onProgressChange,
      onComplete,
      onError,
      onTimeout
    };
  });

  // 安全的状态更新函数
  const safeSetState = useCallback((updater: (prev: SplashAnimationState) => SplashAnimationState) => {
    if (isMountedRef.current) {
      setAnimationState(updater);
    }
  }, []);

  // 清理定时器函数
  const clearTimers = useCallback(() => {
    if (progressIntervalRef.current) {
      clearInterval(progressIntervalRef.current);
      progressIntervalRef.current = null;
    }
    if (hideTimerRef.current) {
      clearTimeout(hideTimerRef.current);
      hideTimerRef.current = null;
    }
    if (timeoutTimerRef.current) {
      clearTimeout(timeoutTimerRef.current);
      timeoutTimerRef.current = null;
    }
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
      animationFrameRef.current = null;
    }
  }, []);

  // 手动隐藏动画
  const hide = useCallback(() => {
    try {
      clearTimers();
      safeSetState(prev => {
        // 只有在未隐藏状态下才执行隐藏操作
        if (prev.phase !== SplashAnimationPhase.HIDDEN) {
          const newState = {
            ...prev,
            phase: SplashAnimationPhase.HIDDEN,
            isVisible: false
          };
          
          // 触发回调
          callbacksRef.current.onPhaseChange(newState.phase);
          callbacksRef.current.onComplete();
          
          return newState;
        }
        return prev;
      });
    } catch (error) {
      console.error('Error hiding splash animation:', error);
      callbacksRef.current.onError(error as Error);
    }
  }, [clearTimers, safeSetState]);

  // 数据加载逻辑
  const startDataLoading = useCallback(() => {
    if (!enableDynamicLoading || loadingTasks.length === 0) return;

    // 立即开始数据加载，而不是等到80%
    loadingTasks.forEach(task => {
      task.loader()
        .then(() => {
          if (isMountedRef.current) {
            completedTasksRef.current.add(task.id);
            
            // 更新加载状态
            setLoadingStatus(prev => ({
              ...prev,
              [task.id]: true
            }));
            
            // 计算数据进度
            const totalWeight = loadingTasks.reduce((sum, t) => sum + t.weight, 0);
            const completedWeight = loadingTasks
              .filter(t => completedTasksRef.current.has(t.id))
              .reduce((sum, t) => sum + t.weight, 0);
            
            const newDataProgress = totalWeight > 0 ? (completedWeight / totalWeight) * 100 : 0;
            setDataProgress(newDataProgress);
          }
        })
        .catch(error => {
          console.error(`Loading task ${task.id} failed:`, error);
          if (isMountedRef.current) {
            completedTasksRef.current.add(task.id);
          }
        });
    });
  }, [enableDynamicLoading, loadingTasks]);

  // 重置动画状态
  const reset = useCallback(() => {
    try {
      clearTimers();
      completedTasksRef.current.clear();
      setDataProgress(0);
      setLoadingStatus({});
      
      safeSetState(() => ({
        phase: SplashAnimationPhase.LOADING,
        progress: 0,
        isVisible: true,
        hasError: false
      }));
    } catch (error) {
      console.error('Error resetting splash animation:', error);
      callbacksRef.current.onError(error as Error);
    }
  }, [clearTimers, safeSetState]);

  // 设置错误状态
  const setError = useCallback((error: Error) => {
    clearTimers();
    safeSetState(prev => ({
      ...prev,
      hasError: true,
      isVisible: false
    }));
    callbacksRef.current.onError(error);
  }, [clearTimers, safeSetState]);

  // 强制超时处理
  const forceTimeout = useCallback(() => {
    console.warn(`Splash animation timed out after ${maxTimeout}ms`);
    callbacksRef.current.onTimeout();
    hide();
  }, [maxTimeout, hide]);

  // 主要动画逻辑
  useEffect(() => {
    if (!animationState.isVisible || animationState.hasError) {
      return;
    }

    try {
      // 设置最大超时保护
      timeoutTimerRef.current = setTimeout(() => {
        if (isMountedRef.current) {
          forceTimeout();
        }
      }, maxTimeout);

      if (enableDynamicLoading) {
        // 动态加载模式 - 立即开始数据加载
        startDataLoading();
        
        const animationLoop = () => {
          if (!isMountedRef.current) return;
          
          const elapsed = Date.now() - startTime;
          const animationProgress = Math.min(100, (elapsed / duration) * 100);
          const totalProgress = Math.min(100, animationProgress * animationWeight + dataProgress * dataWeight);
          
          safeSetState(prev => {
            const newPhase = totalProgress >= 100 ? SplashAnimationPhase.LOADED : prev.phase;
            
            if (totalProgress !== prev.progress || newPhase !== prev.phase) {
              try {
                if (totalProgress !== prev.progress) {
                  callbacksRef.current.onProgressChange(totalProgress);
                }
                if (newPhase !== prev.phase) {
                  callbacksRef.current.onPhaseChange(newPhase);
                }
              } catch (callbackError) {
                console.error('Error in animation callbacks:', callbackError);
              }
            }
            
            return {
              ...prev,
              progress: totalProgress,
              phase: newPhase
            };
          });
          
          // 检查完成条件
          const isDataComplete = completedTasksRef.current.size === loadingTasks.length;
          const isMinTimeReached = elapsed >= minDuration;
          
          if ((isDataComplete && isMinTimeReached) || elapsed >= maxTimeout) {
            if (autoHide) {
              setTimeout(() => {
                if (isMountedRef.current) {
                  hide();
                }
              }, showTime);
            }
          } else {
            animationFrameRef.current = requestAnimationFrame(animationLoop);
          }
        };
        
        animationFrameRef.current = requestAnimationFrame(animationLoop);
      } else {
        // 原有的固定时间模式
        const progressUpdateInterval = Math.max(duration / (100 / progressStep), 16);
        
        progressIntervalRef.current = setInterval(() => {
          if (!isMountedRef.current) return;
          
          safeSetState(prev => {
            const newProgress = Math.min(prev.progress + progressStep, 100);
            const newPhase = newProgress === 100 ? SplashAnimationPhase.LOADED : prev.phase;
            
            if (newProgress !== prev.progress || newPhase !== prev.phase) {
              try {
                if (newProgress !== prev.progress) {
                  callbacksRef.current.onProgressChange(newProgress);
                }
                if (newPhase !== prev.phase) {
                  callbacksRef.current.onPhaseChange(newPhase);
                }
              } catch (callbackError) {
                console.error('Error in animation callbacks:', callbackError);
              }
            }
            
            return {
              ...prev,
              progress: newProgress,
              phase: newPhase
            };
          });
        }, progressUpdateInterval);

        // 自动隐藏逻辑
        if (autoHide) {
          const totalDuration = duration + showTime;
          hideTimerRef.current = setTimeout(() => {
            if (isMountedRef.current) {
              hide();
            }
          }, totalDuration);
        }
      }

    } catch (error) {
      console.error('Error in splash animation:', error);
      callbacksRef.current.onError(error as Error);
    }

    // 清理函数
    return () => {
      clearTimers();
    };
  }, [
    animationState.isVisible,
    animationState.hasError,
    enableDynamicLoading,
    startDataLoading,
    duration,
    progressStep,
    autoHide,
    showTime,
    maxTimeout,
    minDuration,
    animationWeight,
    dataWeight,
    dataProgress,
    startTime,
    loadingTasks.length,
    forceTimeout,
    hide,
    safeSetState,
    clearTimers
  ]);

  // 组件卸载时清理
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      clearTimers();
    };
  }, [clearTimers]);

  return {
    ...animationState,
    dataProgress,
    loadingStatus,
    config,
    hide,
    reset,
    setError,
    isHidden: animationState.phase === SplashAnimationPhase.HIDDEN
  };
};