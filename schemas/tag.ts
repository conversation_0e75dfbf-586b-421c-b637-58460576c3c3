import { z } from 'zod';
import { UuidSchema, DateTimeSchema, NonEmptyStringSchema } from './common';

// 兼容本地和远程的ID Schema
const FlexibleIdSchema = z.string().min(1, 'ID不能为空');

/**
 * 标签相关的 Schema 定义
 */

// 标签读取 Schema
export const TagReadSchema = z.object({
  id: FlexibleIdSchema,
  name: NonEmptyStringSchema,
  created_at: DateTimeSchema,
  updated_at: DateTimeSchema,
});

// 标签创建 Schema
export const TagCreateSchema = z.object({
  name: NonEmptyStringSchema,
});

// 标签更新 Schema
export const TagUpdateSchema = z.object({
  name: NonEmptyStringSchema.optional(),
});

// 标签列表响应 Schema
export const TagListResponseSchema = z.array(TagReadSchema);

/**
 * 标签相关类型定义
 */
export type TagRead = z.infer<typeof TagReadSchema>;
export type TagCreate = z.infer<typeof TagCreateSchema>;
export type TagUpdate = z.infer<typeof TagUpdateSchema>;
export type TagListResponse = z.infer<typeof TagListResponseSchema>;

/**
 * 标签相关验证函数
 */

// 验证标签读取数据
export function validateTagRead(data: unknown): TagRead {
  return TagReadSchema.parse(data);
}

// 验证标签列表数据
export function validateTagList(data: unknown): TagListResponse {
  return TagListResponseSchema.parse(data);
}

// 验证标签名称
export function validateTagName(name: string): string {
  return NonEmptyStringSchema.parse(name);
} 