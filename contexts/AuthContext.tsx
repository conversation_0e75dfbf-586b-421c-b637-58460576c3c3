import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { IS_ELECTRON } from '../constants';

interface AuthContextType {
  isAuthenticated: boolean;
  login: (token: string) => void;
  logout: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  // Electron模式下直接设置为已认证状态，Web模式检查localStorage
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(
    IS_ELECTRON ? true : !!localStorage.getItem('access_token')
  );
  const navigate = useNavigate();
  const location = useLocation(); // To track location changes for re-check

  useEffect(() => {
    if (IS_ELECTRON) {
      // Electron模式：自动登录，设置本地用户信息
      console.log('✅ Electron模式：自动登录激活');
      setIsAuthenticated(true);
      // 设置本地用户token，确保其他依赖token的代码正常工作
      localStorage.setItem('access_token', 'electron-local-token');
      return; // 跳过Web模式的认证检查逻辑
    }

    // Web模式：保持原有认证检查逻辑
    const checkAuthStatus = () => {
      setIsAuthenticated(!!localStorage.getItem('access_token'));
    };

    checkAuthStatus(); // Check on initial mount and location change

    window.addEventListener('storage', checkAuthStatus); // Listen for direct localStorage changes from other tabs/windows

    // Custom event listener for in-app token changes (e.g. if not using context's login/logout everywhere)
    // This is a fallback, prefer using context's login/logout.
    const handleTokenChange = () => checkAuthStatus();
    window.addEventListener('tokenChanged', handleTokenChange);

    return () => {
      window.removeEventListener('storage', checkAuthStatus);
      window.removeEventListener('tokenChanged', handleTokenChange);
    };
  }, [location.pathname]); // Re-check on path change as well

  const login = (token: string) => {
    if (IS_ELECTRON) {
      // Electron模式：已经自动登录，忽略额外的登录请求
      console.log('📍 Electron模式：忽略手动登录，已处于登录状态');
      return;
    }
    
    // Web模式：正常登录流程
    localStorage.setItem('access_token', token);
    setIsAuthenticated(true);
    window.dispatchEvent(new Event('tokenChanged')); // Notify other parts if needed
    // navigate('/'); // Or to a specific post-login page
  };

  const logout = () => {
    if (IS_ELECTRON) {
      // Electron模式：禁止登出，保持登录状态
      console.log('📍 Electron模式：禁止登出操作，保持本地认证状态');
      return;
    }
    
    // Web模式：正常登出流程
    localStorage.removeItem('access_token');
    setIsAuthenticated(false);
    window.dispatchEvent(new Event('tokenChanged')); // Notify other parts if needed
    navigate('/');
  };

  return (
    <AuthContext.Provider value={{ isAuthenticated, login, logout }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
