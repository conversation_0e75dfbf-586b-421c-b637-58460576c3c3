<!DOCTYPE html>
<html lang="en" class="scrollbar-thin">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>Pokedex 鸟类图鉴</title>

  <!-- PWA Manifest -->
  <link rel="manifest" href="/manifest.json">

  <!-- Theme Color -->
  <meta name="theme-color" content="#3B82F6">

  <!-- Standard PWA Meta Tag -->
  <meta name="mobile-web-app-capable" content="yes">
  
  <!-- Apple PWA Meta Tags - 针对iOS优化 -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <meta name="apple-mobile-web-app-title" content="Pokedex IM">
  <meta name="apple-touch-fullscreen" content="yes">
  
  <!-- Apple Touch Icons - 多尺寸支持 -->
  <link rel="apple-touch-icon" sizes="144x144" href="/icons/icon-144x144.png">
  <link rel="apple-touch-icon" sizes="192x192" href="/icons/icon-192x192.png">
  <link rel="apple-touch-icon" sizes="512x512" href="/icons/icon-512x512.png">
  
  <!-- Apple启动画面配置 -->
  <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
  <link rel="apple-touch-startup-image" href="/icons/icon-512x512.png">
  
  <!-- iOS Safari特定优化 -->
  <meta name="format-detection" content="telephone=no">
  <meta name="msapplication-tap-highlight" content="no">


  
  <!-- 开屏动画优化配置 -->
  <meta name="splash-screen-support" content="true">
  <meta name="application-ready" content="false">
  
  <style>
    /* IBM Plex Mono - 等宽字体 */
    @font-face {
      font-family: 'IBM Plex Mono';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url('/src/assets/fonts/ibm-plex-mono/IBMPlexMono-Regular.ttf') format('truetype');
    }
    @font-face {
      font-family: 'IBM Plex Mono';
      font-style: normal;
      font-weight: 500;
      font-display: swap;
      src: url('/src/assets/fonts/ibm-plex-mono/IBMPlexMono-Medium.ttf') format('truetype');
    }
    @font-face {
      font-family: 'IBM Plex Mono';
      font-style: normal;
      font-weight: 600;
      font-display: swap;
      src: url('/src/assets/fonts/ibm-plex-mono/IBMPlexMono-SemiBold.ttf') format('truetype');
    }
    @font-face {
      font-family: 'IBM Plex Mono';
      font-style: normal;
      font-weight: 700;
      font-display: swap;
      src: url('/src/assets/fonts/ibm-plex-mono/IBMPlexMono-Bold.ttf') format('truetype');
    }

    /* Inter - 无衬线字体 */
    @font-face {
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-display: swap;
      src: url('/src/assets/fonts/inter/Inter-Regular.ttf') format('truetype');
    }
    @font-face {
      font-family: 'Inter';
      font-style: normal;
      font-weight: 500;
      font-display: swap;
      src: url('/src/assets/fonts/inter/Inter-Medium.ttf') format('truetype');
    }
    @font-face {
      font-family: 'Inter';
      font-style: normal;
      font-weight: 600;
      font-display: swap;
      src: url('/src/assets/fonts/inter/Inter-SemiBold.ttf') format('truetype');
    }
    @font-face {
      font-family: 'Inter';
      font-style: normal;
      font-weight: 700;
      font-display: swap;
      src: url('/src/assets/fonts/inter/Inter-Bold.ttf') format('truetype');
    }

    /* Custom scrollbar for webkit browsers */
    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 10px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }

    .scrollbar-thin::-webkit-scrollbar {
        width: 6px;
        height: 6px;
    }
    .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: #a0aec0;
        border-radius: 3px;
    }
    html.dark .scrollbar-thin::-webkit-scrollbar-track {
        background: #2d3748;
    }
    html.dark .scrollbar-thin::-webkit-scrollbar-thumb {
        background-color: #718096;
    }

    body {
      font-family: 'IBM Plex Mono', 'Inter', 'Noto Serif SC', 'Noto Sans SC', monospace, sans-serif;
      transition: background-color 0.3s ease-in-out, color 0.3s ease-in-out;
      overflow-x: hidden;
    }

    /* Keyframes for subtle animations */
    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }
    @keyframes fadeInUp {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    .animate-fadeIn {
      animation: fadeIn 0.5s ease-in-out;
    }
    .animate-fadeInUp {
      animation: fadeInUp 0.5s ease-in-out;
    }

    .modal-transition {
      transition-timing-function: cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    /* Hide browser's default clear button for search inputs */
    input[type="search"]::-webkit-search-decoration,
    input[type="search"]::-webkit-search-cancel-button,
    input[type="search"]::-webkit-search-results-button,
    input[type="search"]::-webkit-search-results-decoration {
      -webkit-appearance: none;
      display: none;
    }
    input[type="search"] {
      -moz-appearance: textfield;
    }

    /* ECharts Tooltip Styles */
    .echarts-tooltip {
        background: rgba(255, 255, 255, 0.97) !important;
        border: 1px solid #FFD700 !important;
        border-radius: 8px !important;
        padding: 18px 25px !important;
        box-shadow: 0 6px 25px rgba(255, 215, 0, 0.2) !important;
        color: #333d48 !important;
        font-size: 14px !important;
        line-height: 1.8;
        transition: all 0.3s ease-out;
        font-family: 'IBM Plex Mono', 'Noto Serif SC', 'Noto Sans SC', monospace, serif;
    }
    .echarts-tooltip strong {
        color: #B8860B !important;
        font-weight: 700 !important;
        font-size: 1.1em;
        display: block;
        margin-bottom: 8px;
    }
    .echarts-tooltip span {
        color: #4a5568 !important;
        font-weight: 500;
    }

    /* Shimmer animation for skeletons */
    @keyframes shimmer {
      0% { background-position: -1000px 0; }
      100% { background-position: 1000px 0; }
    }
    .shimmer-gradient {
      animation: shimmer 2s infinite linear;
      background: linear-gradient(to right, transparent 0%, rgba(255,255,255,0.2) 50%, transparent 100%);
      background-size: 1000px 100%;
    }
    html.dark .shimmer-gradient {
      background: linear-gradient(to right, transparent 0%, rgba(255,255,255,0.05) 50%, transparent 100%);
      background-size: 1000px 100%;
    }
    
    .shimmer-gradient-galaxy {
      animation: shimmer 2.2s infinite linear;
      background: linear-gradient(to right, transparent 0%, rgba(56, 189, 248, 0.15) 50%, transparent 100%);
      background-size: 1000px 100%;
    }
    html.dark .shimmer-gradient-galaxy {
      background: linear-gradient(to right, transparent 0%, rgba(34, 211, 238, 0.1) 50%, transparent 100%);
      background-size: 1000px 100%;
    }
    
    .shimmer-gradient-arcade {
      animation: shimmer 1.8s infinite steps(10, end);
      background: linear-gradient(to right, 
        transparent 0%, rgba(255,255,0,0.2) 20%,
        transparent 20%, transparent 40%, rgba(239,68,68,0.2) 60%,
        transparent 60%, transparent 80%, rgba(59,130,246,0.2) 100%
      );
      background-size: 1200px 100%;
    }
    html.dark .shimmer-gradient-arcade {
       animation: shimmer 1.8s infinite steps(10, end);
      background: linear-gradient(to right, 
        transparent 0%, rgba(250,204,21,0.15) 20%, 
        transparent 20%, transparent 40%, rgba(220,38,38,0.15) 60%, 
        transparent 60%, transparent 80%, rgba(37,99,235,0.15) 100%
      );
      background-size: 1200px 100%;
    }
    
    /* Masonry styles */
    .my-masonry-grid {
      display: flex;
      margin-left: -12px;
      width: auto;
    }
    .my-masonry-grid_column {
      padding-left: 12px;
      background-clip: padding-box;
    }
    .my-masonry-grid_column > div {
      margin-bottom: 12px;
    }
  </style>
  

</head>
<body class="antialiased">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>