/// <reference types="vite/client" />

declare module "*.css" {
  const content: string;
  export default content;
}

interface Window {
  electronAPI: {
    isElectron: boolean;
    platform: string;
    testDatabase?: () => Promise<any>;
    getDatabaseStats?: () => Promise<any>;
    getCategories?: (skip?: number, limit?: number) => Promise<any>;
    createCategory?: (categoryData: any) => Promise<any>;
    updateCategory?: (categoryId: string, categoryData: any) => Promise<any>;
    deleteCategory?: (categoryId: string) => Promise<void>;
    getCategoryById?: (categoryId: string) => Promise<any>;
    getCategoryWithImages?: (categoryId: string) => Promise<any>;
    
    // 图片相关方法
    uploadImage?: (categoryId: string, fileBuffer: any, originalFilename: string, mimeType: string) => Promise<any>;
    getImageById?: (imageId: string) => Promise<any>;
    updateImage?: (imageId: string, imageData: any) => Promise<any>;
    deleteImage?: (imageId: string) => Promise<any>;
    deleteImages?: (imageIds: string[]) => Promise<any>;
    validateDeleteConditions?: (imageId: string) => Promise<any>;
    showDeleteConfirmation?: (options: any) => Promise<any>;
    showDeleteProgress?: (options: any) => Promise<any>;
    getImagePath?: (filename: string) => Promise<string>;
    getThumbnailPath?: (filename: string) => Promise<string>;
    
    // 标签相关方法
    getAllTags?: () => Promise<any>;
    createTag?: (tagData: any) => Promise<any>;
    updateTag?: (tagId: string, tagData: any) => Promise<any>;
    deleteTag?: (tagId: string) => Promise<void>;
    getTagById?: (tagId: string) => Promise<any>;
    searchTags?: (query: string) => Promise<any>;
    addTagToImage?: (imageId: string, tagId: string) => Promise<void>;
    removeTagFromImage?: (imageId: string, tagId: string) => Promise<void>;
    getTagsForImage?: (imageId: string) => Promise<any>;
    searchImagesByTags?: (tagNames: string[]) => Promise<string[]>;
    
    // 事件监听方法
    onMenuAction?: (callback: (action: string, data?: any) => void) => void;
    onAppReady?: (callback: () => void) => void;
    removeAllListeners?: (channel: string) => void;
  };
} 