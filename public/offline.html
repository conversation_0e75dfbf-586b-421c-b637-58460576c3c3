<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - Pokedex Image Manager</title>
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
      background-color: #F1F5F9; /* slate-100 */
      color: #1E293B; /* slate-800 */
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      margin: 0;
      padding: 20px;
      text-align: center;
      box-sizing: border-box;
    }
    .container {
      background-color: #FFFFFF; /* white */
      padding: 30px 40px;
      border-radius: 12px;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    }
    h1 {
      color: #3B82F6; /* blue-600 */
      font-size: 24px;
      margin-bottom: 10px;
    }
    p {
      font-size: 16px;
      line-height: 1.6;
      margin-bottom: 20px;
    }
    a {
      display: inline-block;
      padding: 10px 20px;
      background-color: #3B82F6; /* blue-600 */
      color: #FFFFFF;
      text-decoration: none;
      border-radius: 8px;
      font-weight: 500;
      transition: background-color 0.2s ease-in-out;
    }
    a:hover {
      background-color: #2563EB; /* blue-700 */
    }
    /* Dark mode styles (optional, if you want to respect system preference here) */
    @media (prefers-color-scheme: dark) {
      body {
        background-color: #0F172A; /* slate-950 */
        color: #E2E8F0; /* slate-200 */
      }
      .container {
        background-color: #1E293B; /* slate-800/90 */
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.2), 0 2px 4px -1px rgba(0, 0, 0, 0.1);
      }
      h1 {
        color: #60A5FA; /* blue-400 */
      }
      a {
        background-color: #60A5FA; /* blue-400 */
        color: #0F172A; /* slate-950 for text on light blue button */
      }
      a:hover {
        background-color: #3B82F6; /* blue-600 */
      }
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>You are Offline</h1>
    <p>It seems you're not connected to the internet. Some features might be limited, but you can still access previously viewed content.</p>
    <p>Please check your connection and try again.</p>
    <a href="/">Try to Reload Home</a>
  </div>
</body>
</html>
