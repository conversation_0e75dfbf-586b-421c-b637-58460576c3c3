#!/usr/bin/env node

/**
 * 构建验证脚本
 * 验证Electron构建产物的完整性，特别是数据文件的可访问性
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const exists = promisify(fs.exists);
const readdir = promisify(fs.readdir);
const stat = promisify(fs.stat);

// 颜色输出工具
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 验证配置
const VERIFICATION_CONFIG = {
  // 构建输出目录
  buildDir: path.resolve(__dirname, '..', 'dist-electron'),
  
  // 关键目录
  directories: {
    main: 'main',
    preload: 'preload', 
    renderer: 'renderer'
  },
  
  // 必需的数据文件
  dataFiles: [
    'analytics_summary.json',
    'bird_sightings.json', 
    'region_bird_stats.json',
    'social_stats.json',
    'top_birds.json'
  ],
  
  // 其他重要文件
  importantFiles: [
    'china.json', // 中国地图数据
    'index.html', // 主页面
    'manifest.json' // PWA配置
  ],
  
  // 主进程文件
  mainFiles: [
    'main.js',
    'oss-config-window.html'
  ],
  
  // 预加载脚本文件
  preloadFiles: [
    'preload.js'
  ]
};

/**
 * 验证目录是否存在
 */
async function verifyDirectory(dirPath, name) {
  if (await exists(dirPath)) {
    log('green', `✅ ${name} 目录存在: ${dirPath}`);
    return true;
  } else {
    log('red', `❌ ${name} 目录不存在: ${dirPath}`);
    return false;
  }
}

/**
 * 验证文件是否存在
 */
async function verifyFile(filePath, name) {
  if (await exists(filePath)) {
    const stats = await stat(filePath);
    log('green', `✅ ${name} 文件存在: ${filePath} (${stats.size} bytes)`);
    return true;
  } else {
    log('red', `❌ ${name} 文件不存在: ${filePath}`);
    return false;
  }
}

/**
 * 验证JSON文件格式
 */
async function verifyJsonFile(filePath, name) {
  try {
    if (!(await exists(filePath))) {
      log('red', `❌ ${name} 文件不存在: ${filePath}`);
      return false;
    }
    
    const content = fs.readFileSync(filePath, 'utf-8');
    JSON.parse(content);
    
    const stats = await stat(filePath);
    log('green', `✅ ${name} JSON文件有效: ${filePath} (${stats.size} bytes)`);
    return true;
  } catch (error) {
    log('red', `❌ ${name} JSON文件无效: ${filePath} - ${error.message}`);
    return false;
  }
}

/**
 * 列出目录内容（用于调试）
 */
async function listDirectory(dirPath, name, maxDepth = 2, currentDepth = 0) {
  if (currentDepth >= maxDepth || !(await exists(dirPath))) {
    return;
  }
  
  try {
    const items = await readdir(dirPath);
    const indent = '  '.repeat(currentDepth);
    
    if (currentDepth === 0) {
      log('cyan', `\n📁 ${name} 目录内容:`);
    }
    
    for (const item of items) {
      const itemPath = path.join(dirPath, item);
      const stats = await stat(itemPath);
      
      if (stats.isDirectory()) {
        log('blue', `${indent}📁 ${item}/`);
        await listDirectory(itemPath, `${name}/${item}`, maxDepth, currentDepth + 1);
      } else {
        const size = stats.size > 1024 ? `${(stats.size / 1024).toFixed(1)}KB` : `${stats.size}B`;
        log('white', `${indent}📄 ${item} (${size})`);
      }
    }
  } catch (error) {
    log('red', `❌ 无法读取目录 ${dirPath}: ${error.message}`);
  }
}

/**
 * 主验证函数
 */
async function main() {
  log('bright', '\n🔍 开始验证Electron构建产物...\n');
  
  let allPassed = true;
  
  // 1. 验证主要目录
  log('cyan', '📁 验证主要目录结构...');
  const mainDirPath = path.join(VERIFICATION_CONFIG.buildDir, VERIFICATION_CONFIG.directories.main);
  const preloadDirPath = path.join(VERIFICATION_CONFIG.buildDir, VERIFICATION_CONFIG.directories.preload);
  const rendererDirPath = path.join(VERIFICATION_CONFIG.buildDir, VERIFICATION_CONFIG.directories.renderer);
  
  allPassed &= await verifyDirectory(VERIFICATION_CONFIG.buildDir, '构建输出根目录');
  allPassed &= await verifyDirectory(mainDirPath, '主进程目录');
  allPassed &= await verifyDirectory(preloadDirPath, '预加载脚本目录');
  allPassed &= await verifyDirectory(rendererDirPath, '渲染进程目录');
  
  // 2. 验证主进程文件
  log('cyan', '\n📄 验证主进程文件...');
  for (const file of VERIFICATION_CONFIG.mainFiles) {
    const filePath = path.join(mainDirPath, file);
    allPassed &= await verifyFile(filePath, `主进程文件 ${file}`);
  }
  
  // 3. 验证预加载脚本文件
  log('cyan', '\n📄 验证预加载脚本文件...');
  for (const file of VERIFICATION_CONFIG.preloadFiles) {
    const filePath = path.join(preloadDirPath, file);
    allPassed &= await verifyFile(filePath, `预加载脚本 ${file}`);
  }
  
  // 4. 验证渲染进程基础文件
  log('cyan', '\n📄 验证渲染进程基础文件...');
  for (const file of VERIFICATION_CONFIG.importantFiles) {
    const filePath = path.join(rendererDirPath, file);
    allPassed &= await verifyFile(filePath, `渲染进程文件 ${file}`);
  }
  
  // 5. 验证数据文件目录和文件
  log('cyan', '\n📄 验证数据文件...');
  const dataDir = path.join(rendererDirPath, 'data');
  allPassed &= await verifyDirectory(dataDir, '数据目录');
  
  if (await exists(dataDir)) {
    for (const file of VERIFICATION_CONFIG.dataFiles) {
      const filePath = path.join(dataDir, file);
      allPassed &= await verifyJsonFile(filePath, `数据文件 ${file}`);
    }
  }
  
  // 6. 特殊验证：china.json文件（可能在根目录或data目录）
  log('cyan', '\n📄 验证中国地图数据...');
  const chinaJsonPaths = [
    path.join(rendererDirPath, 'china.json'),
    path.join(dataDir, 'china.json')
  ];
  
  let chinaJsonFound = false;
  for (const chinaPath of chinaJsonPaths) {
    if (await exists(chinaPath)) {
      allPassed &= await verifyJsonFile(chinaPath, '中国地图数据');
      chinaJsonFound = true;
      break;
    }
  }
  
  if (!chinaJsonFound) {
    log('red', '❌ 中国地图数据文件未找到');
    allPassed = false;
  }
  
  // 7. 检查关键静态资源
  log('cyan', '\n📄 验证静态资源...');
  const assetsPath = path.join(rendererDirPath, 'assets');
  if (await exists(assetsPath)) {
    log('green', `✅ assets目录存在: ${assetsPath}`);
    const assetItems = await readdir(assetsPath);
    log('blue', `📊 assets目录包含 ${assetItems.length} 个文件`);
  } else {
    log('yellow', `⚠️  assets目录不存在，可能影响样式加载`);
  }
  
  // 8. 生成详细的目录结构报告（调试用）
  if (process.argv.includes('--verbose') || process.argv.includes('-v')) {
    log('cyan', '\n📋 详细目录结构:');
    await listDirectory(VERIFICATION_CONFIG.buildDir, '构建输出');
  }
  
  // 9. 输出验证结果
  log('cyan', '\n📊 验证结果汇总:');
  if (allPassed) {
    log('green', '🎉 所有验证项目通过！构建产物完整。');
    process.exit(0);
  } else {
    log('red', '💥 验证失败！部分文件缺失或损坏。');
    log('yellow', '💡 建议：');
    log('yellow', '  1. 重新运行构建命令：npm run electron:build');
    log('yellow', '  2. 检查构建日志是否有错误信息');
    log('yellow', '  3. 确保public/data/目录下的数据文件完整');
    log('yellow', '  4. 运行 node scripts/verify-build.js --verbose 查看详细信息');
    process.exit(1);
  }
}

// 错误处理
process.on('unhandledRejection', (reason, promise) => {
  log('red', `❌ 未处理的Promise拒绝: ${reason}`);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  log('red', `❌ 未捕获的异常: ${error.message}`);
  process.exit(1);
});

// 运行主函数
main().catch(error => {
  log('red', `❌ 验证过程出错: ${error.message}`);
  process.exit(1);
});