<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>OSS配置</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 500px;
            margin: 0 auto;
            background-color: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            font-size: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }
        
        input[type="text"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        input[type="text"]:focus, input[type="password"]:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
        }
        
        .required {
            color: #e74c3c;
        }
        
        .optional {
            color: #888;
            font-size: 12px;
        }
        
        .buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 30px;
        }
        
        button {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        
        .btn-primary {
            background-color: #4CAF50;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #45a049;
        }
        
        .btn-secondary {
            background-color: #2196F3;
            color: white;
        }
        
        .btn-secondary:hover {
            background-color: #1976D2;
        }
        
        .btn-cancel {
            background-color: #f44336;
            color: white;
        }
        
        .btn-cancel:hover {
            background-color: #d32f2f;
        }
        
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            text-align: center;
            font-size: 14px;
        }
        
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .help-text {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
        }

        .backup-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin-bottom: 8px;
            background-color: white;
            border: 1px solid #e0e0e0;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
        }

        .backup-item:hover {
            background-color: #f0f0f0;
        }

        .backup-item:last-child {
            margin-bottom: 0;
        }

        .backup-info {
            flex: 1;
        }

        .backup-name {
            font-weight: 500;
            color: #333;
            font-size: 13px;
        }

        .backup-time {
            font-size: 11px;
            color: #666;
            margin-top: 2px;
        }

        .backup-size {
            font-size: 11px;
            color: #888;
        }

        .backup-actions {
            display: flex;
            gap: 4px;
        }

        .backup-action {
            padding: 4px 8px;
            color: white;
            border: none;
            border-radius: 3px;
            font-size: 11px;
            cursor: pointer;
        }

        .backup-action.delete {
            background-color: #f44336;
        }

        .backup-action.delete:hover {
            background-color: #d32f2f;
        }

        .backup-action.rename {
            background-color: #4CAF50;
        }

        .backup-action.rename:hover {
            background-color: #45a049;
        }

        .backup-action.restore {
            background-color: #2196F3;
        }

        .backup-action.restore:hover {
            background-color: #1976D2;
        }

        .sync-status-enabled {
            color: #4CAF50;
            font-weight: 500;
        }

        .sync-status-disabled {
            color: #f44336;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>存储配置</h1>
        
        <!-- 存储类型选择 -->
        <div class="form-group">
            <label>存储类型 <span class="required">*</span></label>
            <div style="display: flex; gap: 20px; margin-top: 10px;">
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="radio" name="storageType" value="local" id="localStorage" style="margin-right: 8px;">
                    本地存储
                </label>
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="radio" name="storageType" value="oss" id="ossStorage" style="margin-right: 8px;">
                    OSS存储
                </label>
            </div>
            <div class="help-text">选择图片存储方式</div>
        </div>

        <!-- OSS配置区域 -->
        <div id="ossConfigSection" style="display: none;">
            <h2 style="color: #666; font-size: 18px; margin: 30px 0 20px 0;">OSS配置</h2>
        
            <div class="form-group">
                <label for="endpoint">服务端点 <span class="required">*</span></label>
                <input type="text" id="endpoint" name="endpoint" placeholder="https://oss-cn-hangzhou.aliyuncs.com" required>
                <div class="help-text">OSS服务的端点地址，例如：https://oss-cn-hangzhou.aliyuncs.com</div>
            </div>
            
            <div class="form-group">
                <label for="region">区域 <span class="required">*</span></label>
                <input type="text" id="region" name="region" placeholder="cn-hangzhou" required>
                <div class="help-text">OSS服务的区域，例如：cn-hangzhou</div>
            </div>
            
            <div class="form-group">
                <label for="accessKeyId">访问密钥ID <span class="required">*</span></label>
                <input type="text" id="accessKeyId" name="accessKeyId" placeholder="LTAI..." required>
                <div class="help-text">阿里云Access Key ID</div>
            </div>
            
            <div class="form-group">
                <label for="secretAccessKey">访问密钥Secret <span class="required">*</span></label>
                <input type="password" id="secretAccessKey" name="secretAccessKey" placeholder="密钥..." required>
                <div class="help-text">阿里云Access Key Secret</div>
            </div>
            
            <div class="form-group">
                <label for="bucket">存储桶名称 <span class="required">*</span></label>
                <input type="text" id="bucket" name="bucket" placeholder="my-bucket" required>
                <div class="help-text">OSS存储桶名称</div>
            </div>
            
            <div class="form-group">
                <label for="pathPrefix">路径前缀 <span class="optional">(可选)</span></label>
                <input type="text" id="pathPrefix" name="pathPrefix" placeholder="pokedex">
                <div class="help-text">文件存储的路径前缀，可以为空</div>
            </div>
        </div>

        <!-- 数据库同步区域 -->
        <div id="databaseSyncSection" style="display: none;">
            <h2 style="color: #666; font-size: 18px; margin: 30px 0 20px 0; border-top: 1px solid #eee; padding-top: 20px;">数据库同步</h2>
            
            <!-- 同步状态显示 -->
            <div class="form-group">
                <label>同步状态</label>
                <div id="syncStatusDisplay" style="padding: 10px; background-color: #f8f9fa; border-radius: 4px; font-size: 14px;">
                    <div id="syncStatusText">正在加载...</div>
                    <div id="lastBackupTime" style="font-size: 12px; color: #666; margin-top: 5px;"></div>
                    <div id="lastRestoreTime" style="font-size: 12px; color: #666;"></div>
                </div>
            </div>

            <!-- 启用数据库同步 -->
            <div class="form-group">
                <label style="display: flex; align-items: center; font-weight: normal;">
                    <input type="checkbox" id="enableDatabaseSync" style="margin-right: 8px;">
                    启用数据库同步功能
                </label>
                <div class="help-text">启用后可以将数据库备份到OSS并从OSS恢复数据库</div>
            </div>

            <!-- 备份操作区域 -->
            <div id="backupSection" style="display: none;">
                <div class="form-group">
                    <label>数据库备份</label>
                    <div style="display: flex; gap: 10px; align-items: center;">
                        <button type="button" id="backupBtn" class="btn-secondary" style="margin: 0;">备份到云端</button>
                        <button type="button" id="refreshBackupsBtn" class="btn-secondary" style="margin: 0;">刷新列表</button>
                    </div>
                    <div class="help-text">将当前数据库备份到OSS云存储</div>
                </div>

                <!-- 备份列表 -->
                <div class="form-group">
                    <label>可用备份</label>
                    <div id="backupsList" style="max-height: 200px; overflow-y: auto; border: 1px solid #ddd; border-radius: 4px; padding: 10px; background-color: #fafafa;">
                        <div id="backupsLoading" style="text-align: center; color: #666; padding: 20px;">正在加载备份列表...</div>
                        <div id="backupsEmpty" style="text-align: center; color: #666; padding: 20px; display: none;">暂无可用备份</div>
                        <div id="backupsContent"></div>
                    </div>
                    <div class="help-text">点击备份项进行恢复操作</div>
                </div>
            </div>
        </div>

        <!-- 按钮区域 -->
        <div class="buttons">
            <button type="button" id="testBtn" class="btn-secondary" style="display: none;">测试连接</button>
            <button type="button" id="saveStorageBtn" class="btn-primary">保存设置</button>
            <button type="button" id="cancelBtn" class="btn-cancel">取消</button>
        </div>
            
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        // 全局变量
        let currentDatabaseSyncSettings = null;
        let currentBackups = [];
        let backupProgressListeners = [];
        let restoreProgressListeners = [];

        // 加载当前配置
        document.addEventListener('DOMContentLoaded', async () => {
            try {
                const result = await ipcRenderer.invoke('get-oss-config');
                if (result.success) {
                    // 设置存储类型
                    const storageType = result.storageType || 'local';
                    if (storageType === 'local') {
                        document.getElementById('localStorage').checked = true;
                    } else {
                        document.getElementById('ossStorage').checked = true;
                    }
                    
                    // 根据存储类型显示/隐藏OSS配置区域
                    toggleOSSConfigSection();
                    
                    // 加载OSS配置
                    if (result.config) {
                        const config = result.config;
                        document.getElementById('endpoint').value = config.endpoint || '';
                        document.getElementById('region').value = config.region || '';
                        document.getElementById('accessKeyId').value = config.accessKeyId || '';
                        document.getElementById('secretAccessKey').value = config.secretAccessKey || '';
                        document.getElementById('bucket').value = config.bucket || '';
                        document.getElementById('pathPrefix').value = config.pathPrefix || '';
                    }
                }

                // 加载数据库同步设置
                await loadDatabaseSyncSettings();

                // 设置进度监听器
                setupProgressListeners();
            } catch (error) {
                console.error('加载配置失败:', error);
            }
        });
        
        // 切换OSS配置区域显示/隐藏
        function toggleOSSConfigSection() {
            const ossStorage = document.getElementById('ossStorage');
            const ossConfigSection = document.getElementById('ossConfigSection');
            const databaseSyncSection = document.getElementById('databaseSyncSection');
            const testBtn = document.getElementById('testBtn');
            const saveBtn = document.getElementById('saveStorageBtn');
            
            if (ossStorage.checked) {
                ossConfigSection.style.display = 'block';
                databaseSyncSection.style.display = 'block';
                testBtn.style.display = 'inline-block';
                saveBtn.textContent = '保存OSS设置';
                
                // 加载数据库同步设置和备份列表
                loadDatabaseSyncSettings();
            } else {
                ossConfigSection.style.display = 'none';
                databaseSyncSection.style.display = 'none';
                testBtn.style.display = 'none';
                saveBtn.textContent = '保存设置';
            }
        }
        
        // 监听存储类型变化
        document.getElementById('localStorage').addEventListener('change', toggleOSSConfigSection);
        document.getElementById('ossStorage').addEventListener('change', toggleOSSConfigSection);
        
        // 显示状态信息
        function showStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
        }
        
        // 获取表单数据
        function getFormData() {
            return {
                endpoint: document.getElementById('endpoint').value.trim(),
                region: document.getElementById('region').value.trim(),
                accessKeyId: document.getElementById('accessKeyId').value.trim(),
                secretAccessKey: document.getElementById('secretAccessKey').value.trim(),
                bucket: document.getElementById('bucket').value.trim(),
                pathPrefix: document.getElementById('pathPrefix').value.trim()
            };
        }
        
        // 测试连接
        document.getElementById('testBtn').addEventListener('click', async () => {
            const config = getFormData();
            
            // 验证必填字段
            if (!config.endpoint || !config.region || !config.accessKeyId || !config.secretAccessKey || !config.bucket) {
                showStatus('请填写所有必填字段', 'error');
                return;
            }
            
            showStatus('正在测试连接...', 'info');
            
            try {
                const result = await ipcRenderer.invoke('test-oss-connection', config);
                if (result.success) {
                    showStatus('连接测试成功！', 'success');
                } else {
                    showStatus(`连接测试失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`连接测试失败: ${error.message}`, 'error');
            }
        });
        
        // 保存设置
        document.getElementById('saveStorageBtn').addEventListener('click', async () => {
            const storageType = document.getElementById('localStorage').checked ? 'local' : 'oss';
            
            // 如果选择OSS存储，需要验证OSS配置
            if (storageType === 'oss') {
                const config = getFormData();
                
                // 验证必填字段
                if (!config.endpoint || !config.region || !config.accessKeyId || !config.secretAccessKey || !config.bucket) {
                    showStatus('使用OSS存储需要填写所有必填字段', 'error');
                    return;
                }
                
                showStatus('正在保存OSS配置...', 'info');
                
                try {
                    // 先保存OSS配置
                    const ossResult = await ipcRenderer.invoke('update-oss-config', config);
                    if (!ossResult.success) {
                        showStatus(`保存OSS配置失败: ${ossResult.message}`, 'error');
                        return;
                    }
                } catch (error) {
                    showStatus(`保存OSS配置失败: ${error.message}`, 'error');
                    return;
                }
            }
            
            // 切换存储类型
            const statusMessage = storageType === 'local' ? '正在切换到本地存储...' : '正在切换到OSS存储...';
            showStatus(statusMessage, 'info');
            
            try {
                const result = await ipcRenderer.invoke('switch-storage-type', storageType);
                if (result.success) {
                    const successMessage = storageType === 'local' ? '已切换到本地存储！' : '已切换到OSS存储！';
                    showStatus(successMessage, 'success');
                    
                    // 刷新主窗口
                    try {
                        showStatus('正在刷新主窗口...', 'info');
                        const refreshResult = await ipcRenderer.invoke('refresh-main-window');
                        if (refreshResult.success) {
                            showStatus('配置已保存，主窗口已刷新！', 'success');
                        } else {
                            console.warn('刷新主窗口失败:', refreshResult.message);
                            showStatus('配置已保存！', 'success');
                        }
                    } catch (refreshError) {
                        console.warn('刷新主窗口失败:', refreshError);
                        showStatus('配置已保存！', 'success');
                    }
                    
                    setTimeout(() => {
                        window.close();
                    }, 1500);
                } else {
                    showStatus(`切换存储类型失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`切换存储类型失败: ${error.message}`, 'error');
            }
        });
        
        // 取消
        document.getElementById('cancelBtn').addEventListener('click', () => {
            window.close();
        });

        // ============ 数据库同步相关函数 ============

        // 加载数据库同步设置
        async function loadDatabaseSyncSettings() {
            try {
                const result = await ipcRenderer.invoke('get-database-sync-settings');
                if (result.success) {
                    currentDatabaseSyncSettings = result.data;
                    updateSyncStatusDisplay();
                    
                    // 设置复选框状态
                    document.getElementById('enableDatabaseSync').checked = result.data.enabled;
                    toggleBackupSection();
                    
                    // 如果启用了同步，加载备份列表
                    if (result.data.enabled && result.data.canSync) {
                        loadBackupsList();
                    }
                }
            } catch (error) {
                console.error('加载数据库同步设置失败:', error);
                document.getElementById('syncStatusText').textContent = '加载设置失败';
            }
        }

        // 更新同步状态显示
        function updateSyncStatusDisplay() {
            const syncStatusText = document.getElementById('syncStatusText');
            const lastBackupTime = document.getElementById('lastBackupTime');
            const lastRestoreTime = document.getElementById('lastRestoreTime');
            
            if (!currentDatabaseSyncSettings) {
                syncStatusText.textContent = '未知状态';
                return;
            }
            
            const settings = currentDatabaseSyncSettings;
            
            if (settings.enabled && settings.canSync) {
                syncStatusText.textContent = '✅ 数据库同步已启用';
                syncStatusText.className = 'sync-status-enabled';
            } else if (settings.enabled) {
                syncStatusText.textContent = '⚠️ 数据库同步已启用（需要有效的OSS配置）';
                syncStatusText.className = 'sync-status-disabled';
            } else {
                syncStatusText.textContent = '❌ 数据库同步未启用';
                syncStatusText.className = 'sync-status-disabled';
            }
            
            if (settings.lastBackupTime) {
                const backupDate = new Date(settings.lastBackupTime);
                lastBackupTime.textContent = `最后备份时间: ${formatDateTime(backupDate)}`;
            } else {
                lastBackupTime.textContent = '最后备份时间: 从未备份';
            }
            
            if (settings.lastRestoreTime) {
                const restoreDate = new Date(settings.lastRestoreTime);
                lastRestoreTime.textContent = `最后恢复时间: ${formatDateTime(restoreDate)}`;
            } else {
                lastRestoreTime.textContent = '最后恢复时间: 从未恢复';
            }
        }

        // 格式化日期时间
        function formatDateTime(date) {
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        }

        // 切换备份操作区域显示/隐藏
        function toggleBackupSection() {
            const enableSync = document.getElementById('enableDatabaseSync').checked;
            const backupSection = document.getElementById('backupSection');
            
            if (enableSync) {
                backupSection.style.display = 'block';
            } else {
                backupSection.style.display = 'none';
            }
        }

        // 监听数据库同步启用状态变化
        document.getElementById('enableDatabaseSync').addEventListener('change', async (e) => {
            const enabled = e.target.checked;
            
            try {
                showStatus('正在更新数据库同步设置...', 'info');
                const result = await ipcRenderer.invoke('update-database-sync-settings', { enableDatabaseSync: enabled });
                
                if (result.success) {
                    currentDatabaseSyncSettings = result.data;
                    updateSyncStatusDisplay();
                    toggleBackupSection();
                    
                    if (enabled && result.data.canSync) {
                        loadBackupsList();
                        showStatus('数据库同步已启用', 'success');
                    } else if (enabled) {
                        showStatus('数据库同步已启用，但需要有效的OSS配置', 'info');
                    } else {
                        showStatus('数据库同步已禁用', 'info');
                    }
                } else {
                    showStatus(`更新设置失败: ${result.message}`, 'error');
                    e.target.checked = !enabled; // 恢复状态
                }
            } catch (error) {
                showStatus(`更新设置失败: ${error.message}`, 'error');
                e.target.checked = !enabled; // 恢复状态
            }
        });

        // 加载备份列表
        async function loadBackupsList() {
            const backupsContent = document.getElementById('backupsContent');
            const backupsLoading = document.getElementById('backupsLoading');
            const backupsEmpty = document.getElementById('backupsEmpty');
            
            // 显示加载状态
            backupsLoading.style.display = 'block';
            backupsEmpty.style.display = 'none';
            backupsContent.innerHTML = '';
            
            try {
                const result = await ipcRenderer.invoke('list-database-backups');
                
                if (result.success) {
                    currentBackups = result.backups || [];
                    
                    if (currentBackups.length === 0) {
                        backupsLoading.style.display = 'none';
                        backupsEmpty.style.display = 'block';
                    } else {
                        backupsLoading.style.display = 'none';
                        renderBackupsList();
                    }
                } else {
                    backupsLoading.style.display = 'none';
                    backupsContent.innerHTML = `<div style="text-align: center; color: #f44336; padding: 20px;">加载失败: ${result.message}</div>`;
                }
            } catch (error) {
                backupsLoading.style.display = 'none';
                backupsContent.innerHTML = `<div style="text-align: center; color: #f44336; padding: 20px;">加载失败: ${error.message}</div>`;
            }
        }

        // 渲染备份列表
        function renderBackupsList() {
            const backupsContent = document.getElementById('backupsContent');
            
            backupsContent.innerHTML = currentBackups.map(backup => {
                const backupDate = new Date(backup.lastModified);
                const sizeText = backup.size > 0 ? formatFileSize(backup.size) : '未知大小';
                
                return `
                    <div class="backup-item" data-backup-name="${backup.name}">
                        <div class="backup-info">
                            <div class="backup-name">${backup.name}</div>
                            <div class="backup-time">${formatDateTime(backupDate)}</div>
                        </div>
                        <div class="backup-size">${sizeText}</div>
                        <div class="backup-actions">
                            <button class="backup-action rename" data-backup-name="${backup.name}">重命名</button>
                            <button class="backup-action restore" data-backup-name="${backup.name}">恢复</button>
                            <button class="backup-action delete" data-backup-name="${backup.name}">删除</button>
                        </div>
                    </div>
                `;
            }).join('');

            // 为新渲染的按钮添加事件监听器
            addBackupActionListeners();
        }

        // 添加备份操作按钮的事件监听器
        function addBackupActionListeners() {
            // 重命名按钮
            const renameButtons = document.querySelectorAll('.backup-action.rename');
            
            renameButtons.forEach((button) => {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const backupName = e.target.getAttribute('data-backup-name');
                    renameBackup(backupName);
                });
            });

            // 恢复按钮
            const restoreButtons = document.querySelectorAll('.backup-action.restore');
            
            restoreButtons.forEach((button) => {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const backupName = e.target.getAttribute('data-backup-name');
                    restoreBackup(backupName);
                });
            });

            // 删除按钮
            const deleteButtons = document.querySelectorAll('.backup-action.delete');
            
            deleteButtons.forEach((button) => {
                button.addEventListener('click', (e) => {
                    e.stopPropagation();
                    const backupName = e.target.getAttribute('data-backup-name');
                    deleteBackup(backupName);
                });
            });
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 设置进度监听器
        function setupProgressListeners() {
            // 备份进度监听
            if (window.electronAPI && window.electronAPI.onBackupProgress) {
                window.electronAPI.onBackupProgress((progressData) => {
                    const { progress, message } = progressData;
                    showStatus(`${message} (${progress}%)`, 'info');
                });
            }

            // 恢复进度监听
            if (window.electronAPI && window.electronAPI.onRestoreProgress) {
                window.electronAPI.onRestoreProgress((progressData) => {
                    const { progress, message } = progressData;
                    showStatus(`${message} (${progress}%)`, 'info');
                });
            }
        }

        // 备份数据库
        document.getElementById('backupBtn').addEventListener('click', async () => {
            try {
                showStatus('开始备份数据库到云端...', 'info');
                
                // 禁用备份按钮防止重复点击
                const backupBtn = document.getElementById('backupBtn');
                backupBtn.disabled = true;
                backupBtn.textContent = '备份中...';
                
                const result = await ipcRenderer.invoke('backup-database-to-oss');
                
                if (result.success) {
                    showStatus('数据库备份成功！', 'success');
                    
                    // 更新同步状态和备份列表
                    await loadDatabaseSyncSettings();
                    if (currentDatabaseSyncSettings?.canSync) {
                        loadBackupsList();
                    }
                } else {
                    showStatus(`备份失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`备份失败: ${error.message}`, 'error');
            } finally {
                // 恢复备份按钮状态
                const backupBtn = document.getElementById('backupBtn');
                backupBtn.disabled = false;
                backupBtn.textContent = '备份到云端';
            }
        });

        // 刷新备份列表
        document.getElementById('refreshBackupsBtn').addEventListener('click', () => {
            loadBackupsList();
        });

        // 删除备份
        async function deleteBackup(backupName) {
            const confirmed = confirm(`确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`);
            
            if (!confirmed) {
                return;
            }
            
            try {
                showStatus('正在删除备份...', 'info');
                
                const result = await ipcRenderer.invoke('delete-database-backup', backupName);
                
                if (result.success) {
                    showStatus('备份删除成功！', 'success');
                    
                    // 刷新备份列表
                    loadBackupsList();
                } else {
                    showStatus(`删除失败: ${result.message}`, 'error');
                }
            } catch (error) {
                showStatus(`删除失败: ${error.message}`, 'error');
            }
        }

        // 显示自定义输入对话框
        function showInputDialog(title, defaultValue, callback) {
            // 创建模态对话框
            const dialog = document.createElement('div');
            dialog.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.5);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 10000;
            `;
            
            const dialogBox = document.createElement('div');
            dialogBox.style.cssText = `
                background-color: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                min-width: 300px;
                max-width: 500px;
            `;
            
            dialogBox.innerHTML = `
                <h3 style="margin: 0 0 15px 0; color: #333;">${title}</h3>
                <input type="text" id="inputDialogValue" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; margin-bottom: 15px;" value="${defaultValue}" />
                <div style="display: flex; gap: 10px; justify-content: flex-end;">
                    <button id="inputDialogCancel" style="padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">取消</button>
                    <button id="inputDialogOk" style="padding: 8px 16px; border: none; background: #4CAF50; color: white; border-radius: 4px; cursor: pointer;">确定</button>
                </div>
            `;
            
            dialog.appendChild(dialogBox);
            document.body.appendChild(dialog);
            
            const input = document.getElementById('inputDialogValue');
            const okBtn = document.getElementById('inputDialogOk');
            const cancelBtn = document.getElementById('inputDialogCancel');
            
            // 聚焦并选中输入框
            input.focus();
            input.select();
            
            // 事件处理
            const cleanup = () => {
                document.body.removeChild(dialog);
            };
            
            okBtn.addEventListener('click', () => {
                const value = input.value.trim();
                cleanup();
                callback(value);
            });
            
            cancelBtn.addEventListener('click', () => {
                cleanup();
                callback(null);
            });
            
            // 按Enter确定，按Esc取消
            input.addEventListener('keydown', (e) => {
                if (e.key === 'Enter') {
                    const value = input.value.trim();
                    cleanup();
                    callback(value);
                } else if (e.key === 'Escape') {
                    cleanup();
                    callback(null);
                }
            });
        }

        // 重命名备份
        async function renameBackup(backupName) {
            // 提取当前的备份名称前缀（去掉backup-前缀和.db后缀）
            let currentPrefix = backupName.replace(/^backup-/, '').replace(/\.db$/, '');
            
            // 显示自定义输入对话框
            showInputDialog('重命名备份', currentPrefix, async (newPrefix) => {
                if (!newPrefix) {
                    return;
                }
                
                try {
                    // 构造新的备份名称
                    let newBackupName = newPrefix;
                    
                    // 确保文件名以 backup- 开头
                    if (!newBackupName.startsWith('backup-')) {
                        newBackupName = 'backup-' + newBackupName;
                    }
                    
                    // 确保文件名以 .db 结尾
                    if (!newBackupName.endsWith('.db')) {
                        newBackupName += '.db';
                    }
                    
                    if (newBackupName === backupName) {
                        showStatus('新名称与原名称相同', 'info');
                        return;
                    }
                    
                    showStatus('正在重命名备份...', 'info');
                    
                    const result = await ipcRenderer.invoke('rename-database-backup', backupName, newBackupName);
                    
                    if (result.success) {
                        showStatus('备份重命名成功！', 'success');
                        
                        // 刷新备份列表
                        loadBackupsList();
                    } else {
                        showStatus(`重命名失败: ${result.message}`, 'error');
                    }
                } catch (error) {
                    showStatus(`重命名失败: ${error.message}`, 'error');
                }
            });
        }

        // 恢复备份
        async function restoreBackup(backupName) {
            const confirmed = confirm(`确定要恢复备份 "${backupName}" 吗？\n\n这将替换当前的数据库，此操作不可撤销。`);
            
            if (!confirmed) {
                return;
            }
            
            try {
                showStatus('开始从云端恢复数据库...', 'info');
                
                // 禁用所有恢复按钮防止重复点击
                const restoreButtons = document.querySelectorAll('.backup-action');
                restoreButtons.forEach(btn => {
                    btn.disabled = true;
                    btn.textContent = '恢复中...';
                });
                
                const result = await ipcRenderer.invoke('restore-database-from-oss', backupName);
                
                if (result.success) {
                    showStatus('数据库恢复成功！页面将在3秒后关闭。', 'success');
                    
                    // 更新同步状态
                    await loadDatabaseSyncSettings();
                    
                    // 刷新主窗口
                    try {
                        const refreshResult = await ipcRenderer.invoke('refresh-main-window');
                        if (!refreshResult.success) {
                            console.warn('刷新主窗口失败:', refreshResult.message);
                        }
                    } catch (refreshError) {
                        console.warn('刷新主窗口失败:', refreshError);
                    }
                    
                    setTimeout(() => {
                        window.close();
                    }, 3000);
                } else {
                    showStatus(`恢复失败: ${result.message}`, 'error');
                    // 恢复按钮状态
                    restoreButtons.forEach(btn => {
                        btn.disabled = false;
                        btn.textContent = '恢复';
                    });
                }
            } catch (error) {
                showStatus(`恢复失败: ${error.message}`, 'error');
                // 恢复按钮状态
                const restoreButtons = document.querySelectorAll('.backup-action');
                restoreButtons.forEach(btn => {
                    btn.disabled = false;
                    btn.textContent = '恢复';
                });
            }
        }
    </script>
</body>
</html>