import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import type { TagRead, TagCreate } from '../../schemas/tag';

export class TagService {
  constructor(private dbManager: DatabaseManager) {}
  
  async getAllTags(): Promise<TagRead[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const tags = db.prepare('SELECT * FROM tags ORDER BY created_at DESC').all() as TagRead[];
    
    return tags;
  }
  
  async createTag(tagData: TagCreate): Promise<TagRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 检查标签是否已存在
    const existingTag = db.prepare('SELECT * FROM tags WHERE LOWER(name) = LOWER(?)').get(tagData.name) as TagRead | undefined;
    
    if (existingTag) {
      return existingTag;
    }
    
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const tag: TagRead = {
      id,
      name: tagData.name,
      created_at: now,
      updated_at: now
    };
    
    db.prepare('INSERT INTO tags (id, name, created_at, updated_at) VALUES (?, ?, ?, ?)')
      .run(id, tagData.name, now, now);
    
    return tag;
  }
  
  async updateTag(tagId: string, tagData: Partial<TagCreate>): Promise<TagRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const existingTag = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
    if (!existingTag) {
      throw new Error(`标签不存在: ${tagId}`);
    }
    
    // 检查新名称是否与其他标签冲突
    if (tagData.name && tagData.name !== existingTag.name) {
      const nameConflict = db.prepare('SELECT * FROM tags WHERE id != ? AND LOWER(name) = LOWER(?)')
        .get(tagId, tagData.name) as TagRead | undefined;
      
      if (nameConflict) {
        throw new Error(`标签名称已存在: ${tagData.name}`);
      }
    }
    
    const now = new Date().toISOString();
    const updatedTag: TagRead = {
      ...existingTag,
      ...tagData,
      updated_at: now
    };
    
    db.prepare('UPDATE tags SET name = ?, updated_at = ? WHERE id = ?')
      .run(updatedTag.name, now, tagId);
    
    return updatedTag;
  }
  
  async deleteTag(tagId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const tag = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
    if (!tag) {
      throw new Error(`标签不存在: ${tagId}`);
    }
    
    // 删除标签（外键约束会自动删除image_tags关联）
    db.prepare('DELETE FROM tags WHERE id = ?').run(tagId);
    
  }
  
  async getTagById(tagId: string): Promise<TagRead | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const tag = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
    
    if (!tag) {
      return null;
    }
    
    return tag;
  }
  
  async searchTags(query: string): Promise<TagRead[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const normalizedQuery = query.toLowerCase().trim();
    
    if (!normalizedQuery) {
      return this.getAllTags();
    }
    
    // 使用SQL进行搜索和排序
    const sql = `
      SELECT *,
        CASE 
          WHEN LOWER(name) = ? THEN 0
          WHEN LOWER(name) LIKE ? || '%' THEN 1
          ELSE 2
        END as match_score
      FROM tags
      WHERE LOWER(name) LIKE '%' || ? || '%'
      ORDER BY match_score, name
    `;
    
    const tags = db.prepare(sql).all(normalizedQuery, normalizedQuery, normalizedQuery) as TagRead[];
    
    return tags;
  }
  
  // 为图片添加标签
  async addTagToImage(imageId: string, tagId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 检查图片是否存在
    const imageExists = db.prepare('SELECT id FROM images WHERE id = ?').get(imageId);
    if (!imageExists) {
      throw new Error(`图片不存在: ${imageId}`);
    }
    
    // 检查标签是否存在
    const tagExists = db.prepare('SELECT * FROM tags WHERE id = ?').get(tagId) as TagRead | undefined;
    if (!tagExists) {
      throw new Error(`标签不存在: ${tagId}`);
    }
    
    // 检查关联是否已存在
    const existingRelation = db.prepare('SELECT * FROM image_tags WHERE image_id = ? AND tag_id = ?')
      .get(imageId, tagId);
    
    if (existingRelation) {
      return;
    }
    
    // 创建新的关联
    db.prepare('INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)')
      .run(imageId, tagId);
    
  }
  
  // 从图片移除标签
  async removeTagFromImage(imageId: string, tagId: string): Promise<void> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const result = db.prepare('DELETE FROM image_tags WHERE image_id = ? AND tag_id = ?')
      .run(imageId, tagId);
    
    if (result.changes > 0) {
    } else {
    }
  }
  
  // 获取图片的所有标签
  async getTagsForImage(imageId: string): Promise<TagRead[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = `
      SELECT t.*
      FROM tags t
      JOIN image_tags it ON t.id = it.tag_id
      WHERE it.image_id = ?
    `;
    
    const tags = db.prepare(query).all(imageId) as TagRead[];
    
    return tags;
  }
  
  // 根据标签搜索图片
  async searchImagesByTags(tagNames: string[]): Promise<string[]> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    if (tagNames.length === 0) {
      return [];
    }
    
    // 构建查询条件
    const placeholders = tagNames.map(() => '?').join(',');
    const query = `
      SELECT DISTINCT it.image_id
      FROM image_tags it
      JOIN tags t ON it.tag_id = t.id
      WHERE LOWER(t.name) IN (${placeholders})
    `;
    
    const normalizedNames = tagNames.map(name => name.toLowerCase());
    const rows = db.prepare(query).all(...normalizedNames) as { image_id: string }[];
    const imageIds = rows.map(row => row.image_id);
    
    return imageIds;
  }
}