import { DatabaseManager } from '../database';
import Database from 'better-sqlite3';
import { v4 as uuidv4 } from 'uuid';
import type { CategoryCreate, CategoryRead, CategoryUpdate, CategoryListResponse, CategoryReadWithImages } from '../../schemas/category';
import type { BatchDeleteResult } from './ImageService';

export class CategoryService {
  private imageService?: any; // 延迟注入，避免循环依赖

  constructor(private dbManager: DatabaseManager) {}

  /**
   * 设置ImageService实例（用于删除分类时处理图片文件）
   */
  setImageService(imageService: any): void {
    this.imageService = imageService;
  }
  
  async getCategories(skip = 0, limit = 100): Promise<CategoryListResponse> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 首先显示当前使用的数据库信息
    
    const query = `
      SELECT id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at
      FROM categories
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const categories = db.prepare(query).all(limit, skip) as CategoryRead[];
      id: cat.id,
      name: cat.name,
      description: cat.description,
      created_at: cat.created_at
    })));
    return categories;
  }
  
  async createCategory(categoryData: CategoryCreate): Promise<CategoryRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    const id = uuidv4();
    const now = new Date().toISOString();
    
    const insert = db.prepare(`
      INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    const category: CategoryRead = {
      id,
      name: categoryData.name,
      description: categoryData.description,
      thumbnail_path: null,
      thumbnail_url: null,
      created_at: now,
      updated_at: now
    };
    
    insert.run(id, categoryData.name, categoryData.description || null, null, null, now, now);
    
    return category;
  }
  
  async updateCategory(categoryId: string, categoryData: CategoryUpdate): Promise<CategoryRead> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    // 构建动态更新查询，只更新提供的字段
    const updateFields: string[] = [];
    const updateValues: any[] = [];
    
    if (categoryData.name !== undefined) {
      updateFields.push('name = ?');
      updateValues.push(categoryData.name);
    }
    
    if (categoryData.description !== undefined) {
      updateFields.push('description = ?');
      updateValues.push(categoryData.description);
    }
    
    if (updateFields.length === 0) {
      throw new Error('没有提供需要更新的字段');
    }
    
    updateFields.push('updated_at = ?');
    const now = new Date().toISOString();
    updateValues.push(now);
    updateValues.push(categoryId);
    
    const updateQuery = `
      UPDATE categories 
      SET ${updateFields.join(', ')}
      WHERE id = ?
    `;
    
    const update = db.prepare(updateQuery);
    const result = update.run(...updateValues);
    
    if (result.changes === 0) {
      throw new Error(`分类不存在: ${categoryId}`);
    }
    
    const getCategory = db.prepare(`
      SELECT * FROM categories WHERE id = ?
    `);
    
    const updatedCategory = getCategory.get(categoryId) as CategoryRead;
    return updatedCategory;
  }
  
  async deleteCategory(categoryId: string): Promise<{
    success: boolean;
    message: string;
    details?: {
      categoryDeleted: boolean;
      imagesDeleted: BatchDeleteResult;
    };
  }> {
    const db = this.dbManager.getDatabase() as Database.Database;


    // 1. 验证分类是否存在
    const category = await this.getCategoryById(categoryId);
    if (!category) {
      const error = `分类不存在: ${categoryId}`;
      console.error(`❌ ${error}`);
      return {
        success: false,
        message: error
      };
    }


    // 2. 获取分类下的所有图片
    const images = db.prepare('SELECT id FROM images WHERE category_id = ?').all(categoryId) as any[];
    const imageIds = images.map(img => img.id);


    let imagesDeleteResult: BatchDeleteResult = {
      totalCount: 0,
      successCount: 0,
      failedCount: 0,
      results: [],
      errors: []
    };

    // 3. 删除图片文件（如果有ImageService且有图片）
    if (this.imageService && imageIds.length > 0) {
      try {
        imagesDeleteResult = await this.imageService.deleteImages(imageIds);

        // 如果图片删除失败，记录警告但继续删除分类
        if (imagesDeleteResult.failedCount > 0) {
        }
      } catch (error) {
        console.error('❌ 批量删除图片失败:', error);
        // 图片删除失败不应该阻止分类删除，记录错误并继续
        imagesDeleteResult.errors.push(`图片删除失败: ${error instanceof Error ? error.message : String(error)}`);
      }
    } else if (imageIds.length > 0) {
    }

    // 4. 删除分类和数据库中的图片记录
    let categoryDeleted = false;
    try {
      const deleteCategoryTransaction = db.transaction(() => {
        // 删除分类（外键约束会自动删除相关图片记录）
        const deleteCategory = db.prepare('DELETE FROM categories WHERE id = ?');
        const result = deleteCategory.run(categoryId);

        if (result.changes === 0) {
          throw new Error(`分类删除失败: 数据库中没有找到分类 ${categoryId}`);
        }

      });

      deleteCategoryTransaction();
      categoryDeleted = true;
    } catch (error) {
      const errorMsg = `分类删除失败: ${error instanceof Error ? error.message : String(error)}`;
      console.error(`❌ ${errorMsg}`);
      return {
        success: false,
        message: errorMsg,
        details: {
          categoryDeleted: false,
          imagesDeleted: imagesDeleteResult
        }
      };
    }

    // 5. 生成最终结果
    const success = categoryDeleted && (imagesDeleteResult.totalCount === 0 || imagesDeleteResult.failedCount === 0);
    const message = success
      ? `分类 "${category.name}" 删除成功 (包含 ${imagesDeleteResult.successCount} 张图片)`
      : `分类 "${category.name}" 删除完成，但有 ${imagesDeleteResult.failedCount} 张图片删除失败`;


    return {
      success,
      message,
      details: {
        categoryDeleted,
        imagesDeleted: imagesDeleteResult
      }
    };
  }
  
  async getCategoryById(categoryId: string): Promise<CategoryRead | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const query = db.prepare('SELECT * FROM categories WHERE id = ?');
    const category = query.get(categoryId) as CategoryRead | undefined;
    
    if (!category) {
      return null;
    }
    
    return category;
  }
  
  async getCategoryWithImages(categoryId: string): Promise<CategoryReadWithImages | null> {
    const db = this.dbManager.getDatabase() as Database.Database;
    
    const categoryQuery = db.prepare('SELECT * FROM categories WHERE id = ?');
    const category = categoryQuery.get(categoryId) as CategoryRead | undefined;
    
    if (!category) {
      return null;
    }
    
    const imagesQuery = db.prepare(`
      SELECT * FROM images 
      WHERE category_id = ?
      ORDER BY created_at DESC
    `);
    
    const images = imagesQuery.all(categoryId) as any[];
    
    // 确保图片格式正确
    const processedImages = images.map(image => ({
      ...image,
      tags: image.tags || [],
      exif_info: image.exif_info ? JSON.parse(image.exif_info) : null,
      file_metadata: image.file_metadata ? JSON.parse(image.file_metadata) : null
    }));
    
    
    return {
      ...category,
      images: processedImages
    };
  }
}