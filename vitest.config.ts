import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()], // React插件支持前端测试
  test: {
    globals: true,                 // 启用全局测试API (describe, it, expect)
    testTimeout: 10000,            // 10秒测试超时
    retry: 2,                      // 重试次数

    // 根据文件路径自动选择测试环境（使用更简单的方式）
    environmentMatchGlobs: [
      // 前端测试使用jsdom环境
      ['__tests__/**/{components,contexts,hooks,services,utils}/**/*.test.{ts,tsx}', 'jsdom'],
      ['__tests__/**/{schemas,animations}/**/*.test.ts', 'jsdom'],
      ['__tests__/environment.test.ts', 'jsdom'],
      // Electron后端测试使用node环境
      ['__tests__/**/electron/**/*.test.ts', 'node'],
      ['__tests__/**/database/**/*.test.ts', 'node'],
      ['__tests__/**/filesystem/**/*.test.ts', 'node']
    ],

    // 默认环境为jsdom（适合大部分前端测试）
    environment: 'jsdom',

    // 测试设置文件（会根据环境自动加载相应设置）
    setupFiles: ['__tests__/setup/test-setup.ts'],

    // 包含的测试文件
    include: ['__tests__/**/*.test.{ts,tsx}'],

    // 环境配置
    environmentOptions: {
      jsdom: {
        resources: 'usable',
        url: 'http://localhost:3000'
      }
    },

    // 全局配置
    exclude: ['node_modules/', 'dist/', 'dist-electron/'],

    // 覆盖率配置
    coverage: {
      provider: 'v8',
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'dist-electron/',
        '**/*.test.ts',
        '**/__tests__/**'
      ]
    },

    // 进程池配置
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true  // 避免数据库竞争
      }
    }
  }
})