import { defineConfig } from 'vitest/config'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()], // 添加React插件以支持前端测试
  test: {
    environment: 'node',           // 默认使用Node.js环境（适合Electron后端）
    globals: true,                 // 启用全局测试API (describe, it, expect)
    setupFiles: ['__tests__/setup/test-setup.ts'], // 测试设置文件
    coverage: {
      provider: 'v8',              // 使用V8覆盖率引擎
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'dist-electron/',
        '**/*.test.ts',
        '**/__tests__/**'
      ]
    },
    testTimeout: 10000,            // 10秒测试超时
    include: ['__tests__/**/*.test.{ts,tsx}'],
    exclude: ['node_modules/', 'dist/', 'dist-electron/'],
    // 禁用并发执行以避免数据库竞争
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    // 增加重试次数以处理偶发性失败
    retry: 2,
    // 环境配置
    environmentOptions: {
      // jsdom配置（用于前端测试）
      jsdom: {
        resources: 'usable',
        url: 'http://localhost/'
      }
    },
    // 根据文件名选择测试环境
    environmentMatchGlobs: [
      // 前端测试使用jsdom环境
      ['**/__tests__/{services,utils,contexts,hooks}/*.test.ts', 'jsdom'],
      ['**/__tests__/components/**/*.test.tsx', 'jsdom'],
      // 后端测试使用node环境
      ['**/__tests__/electron/**/*.test.ts', 'node']
    ]
  }
})