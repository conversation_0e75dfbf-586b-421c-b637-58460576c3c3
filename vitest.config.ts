import { defineConfig } from 'vitest/config'

export default defineConfig({
  test: {
    environment: 'node',           // Node.js环境（适合Electron后端）
    globals: true,                 // 启用全局测试API (describe, it, expect)
    setupFiles: ['__tests__/setup/test-setup.ts'], // 测试设置文件
    coverage: {
      provider: 'v8',              // 使用V8覆盖率引擎
      reporter: ['text', 'json', 'html'],
      exclude: [
        'node_modules/',
        'dist/',
        'dist-electron/',
        '**/*.test.ts',
        '**/__tests__/**'
      ]
    },
    testTimeout: 10000,            // 10秒测试超时
    include: ['__tests__/**/*.test.ts'],
    exclude: ['node_modules/', 'dist/', 'dist-electron/'],
    // 禁用并发执行以避免数据库竞争
    pool: 'forks',
    poolOptions: {
      forks: {
        singleFork: true
      }
    },
    // 增加重试次数以处理偶发性失败
    retry: 2
  }
})