{"openapi": "3.1.0", "info": {"title": "图鉴式图片管理工具", "description": "一个使用FastAPI和Vue构建的图鉴式图片管理和展示工具", "version": "1.0.0"}, "paths": {"/api/categories/": {"post": {"tags": ["Categories", "类别管理"], "summary": "创建新类别", "description": "创建一个新的图片类别。\n\n- **name**: 类别名称，必须唯一。\n- **description**: 类别的可选描述。", "operationId": "create_category_api_categories__post", "security": [{"OAuth2PasswordBearer": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryCreate"}}}}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "get": {"tags": ["Categories", "类别管理"], "summary": "获取所有类别列表", "description": "检索所有图片类别，支持分页。", "operationId": "read_categories_api_categories__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CategoryRead"}, "title": "Response Read Categories Api Categories  Get"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/categories/{category_id}/": {"get": {"tags": ["Categories", "类别管理"], "summary": "获取特定类别及其图片", "description": "根据ID获取一个特定类别及其包含的所有图片的元数据。", "operationId": "read_category_with_images_api_categories__category_id___get", "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Category Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryReadWithImages"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "patch": {"tags": ["Categories", "类别管理"], "summary": "更新特定类别信息", "description": "更新指定ID的类别信息。\n如果提供了名称，会检查新名称是否与现有其他类别冲突。", "operationId": "update_category_api_categories__category_id___patch", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Category Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CategoryRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Categories", "类别管理"], "summary": "删除特定类别", "description": "删除指定ID的类别。\n\n重要提示:\n此操作会级联删除类别下的所有图片记录及其对应的物理文件。\n在执行删除前，会首先检查类别是否存在。", "operationId": "delete_category_api_categories__category_id___delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Category Id"}}], "responses": {"204": {"description": "Successful Response"}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/categories/{category_id}/images/": {"get": {"tags": ["Categories", "类别管理"], "summary": "Get Images In Category", "description": "获取指定类别下的所有图片 (支持分页)。", "operationId": "get_images_in_category_api_categories__category_id__images__get", "parameters": [{"name": "category_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Category Id"}}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageRead"}, "title": "Response Get Images In Category Api Categories  Category Id  Images  Get"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/images/upload/": {"post": {"tags": ["Images", "图片管理"], "summary": "上传新图片", "description": "上传新图片，并关联到类别和标签。\n- **file**: 必须是图片文件。\n- **title**: 图片标题。\n- **description**: 图片描述 (可选)。\n- **category_id**: 图片所属的类别ID。\n- **tags**: 逗号分隔的标签字符串 (例如 \"风景,旅行\") (可选)。", "operationId": "upload_image_api_images_upload__post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_image_api_images_upload__post"}}}, "required": true}, "responses": {"201": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/api/images/by-tags/": {"get": {"tags": ["Images", "图片管理"], "summary": "根据标签名称搜索图片", "description": "根据一个或多个标签的名称搜索图片。\n\n- **tag_names**: 一个或多个标签名称。\n- **match_all**: 如果为 `true`，则只返回包含所有指定标签的图片 (AND查询)。\n                 如果为 `false` (默认)，则返回包含任何一个指定标签的图片 (OR查询)。", "operationId": "search_images_by_tags_api_images_by_tags__get", "parameters": [{"name": "tag", "in": "query", "required": true, "schema": {"type": "array", "items": {"type": "string"}, "description": "要搜索的标签名称列表 (例如: tag_names=夏天&tag_names=风景)", "title": "Tag"}, "description": "要搜索的标签名称列表 (例如: tag_names=夏天&tag_names=风景)"}, {"name": "match_all", "in": "query", "required": false, "schema": {"type": "boolean", "description": "是否要求匹配所有提供的标签 (AND逻辑)", "default": false, "title": "Match All"}, "description": "是否要求匹配所有提供的标签 (AND逻辑)"}, {"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "description": "跳过的记录数", "default": 0, "title": "<PERSON><PERSON>"}, "description": "跳过的记录数"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 200, "minimum": 1, "description": "返回的最大记录数", "default": 100, "title": "Limit"}, "description": "返回的最大记录数"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageRead"}, "title": "Response Search Images By Tags Api Images By Tags  Get"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/images/{image_id}/": {"get": {"tags": ["Images", "图片管理"], "summary": "Read Image", "description": "根据ID获取指定图片的元数据。", "operationId": "read_image_api_images__image_id___get", "parameters": [{"name": "image_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Image Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["Images", "图片管理"], "summary": "Update Image Metadata", "description": "更新指定图片的元数据，如描述、标签或所属类别。\n可以附带指定是否将此图片设置为其所属类别的缩略图。", "operationId": "update_image_metadata_api_images__image_id___put", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "image_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Image Id"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageUpdate"}}}}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "delete": {"tags": ["Images", "图片管理"], "summary": "删除图片", "description": "从数据库中删除一张图片及其相关文件。\n如果图片不存在，则不执行任何操作并返回204。", "operationId": "delete_image_api_images__image_id___delete", "security": [{"OAuth2PasswordBearer": []}], "parameters": [{"name": "image_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Image Id"}}], "responses": {"204": {"description": "Successful Response"}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/images/": {"get": {"tags": ["Images", "图片管理"], "summary": "Get All Images", "description": "获取所有图片的列表 (支持分页)。", "operationId": "get_all_images_api_images__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ImageRead"}, "title": "Response Get All Images Api Images  Get"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/images/{image_id}": {"get": {"tags": ["Images", "图片管理"], "summary": "Get Image By Id", "description": "根据ID获取单个图片对象的详细信息。", "operationId": "get_image_by_id_api_images__image_id__get", "parameters": [{"name": "image_id", "in": "path", "required": true, "schema": {"type": "string", "format": "uuid", "title": "Image Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ImageRead"}}}}, "404": {"description": "未找到"}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/suggestions": {"get": {"tags": ["Species Information", "Species Information"], "summary": "Get Species Suggestions Endpoint", "description": "获取物种中文名搜索建议列表。\n\n后端将使用查询词 `q` 同时对物种的中文名、中文名全拼、\n以及中文名拼音首字母进行前缀匹配搜索。", "operationId": "get_species_suggestions_endpoint_api_suggestions_get", "parameters": [{"name": "q", "in": "query", "required": true, "schema": {"type": "string", "minLength": 1, "description": "搜索词 (可为中文、全拼或拼音首字母)", "title": "Q"}, "description": "搜索词 (可为中文、全拼或拼音首字母)"}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 20, "exclusiveMinimum": 0, "description": "返回建议结果的最大数量", "default": 10, "title": "Limit"}, "description": "返回建议结果的最大数量"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}, "title": "Response Get Species Suggestions Endpoint Api Suggestions Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/details/{chinese_name}": {"get": {"tags": ["Species Information", "Species Information"], "summary": "Get Species Details Endpoint", "description": "根据物种的精确中文名获取其完整的详细信息。", "operationId": "get_species_details_endpoint_api_details__chinese_name__get", "parameters": [{"name": "chinese_name", "in": "path", "required": true, "schema": {"type": "string", "description": "要查询物种的精确中文名 (需URL编码若含特殊字符)", "title": "Chinese Name"}, "description": "要查询物种的精确中文名 (需URL编码若含特殊字符)"}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SpeciesRead"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/tags/": {"get": {"tags": ["Tags", "标签管理"], "summary": "Get All Tags", "description": "获取所有标签的列表 (支持分页)。", "operationId": "get_all_tags_api_tags__get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TagRead"}, "title": "Response Get All Tags Api Tags  Get"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/send-verification": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Send Verification Code", "description": "Send verification code to user's email", "operationId": "send_verification_code_api_send_verification_post", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string", "title": "Email"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/verify": {"post": {"tags": ["<PERSON><PERSON>"], "summary": "Verify Code And Get Token", "description": "Verify code and return JWT token if valid", "operationId": "verify_code_and_get_token_api_verify_post", "parameters": [{"name": "email", "in": "query", "required": true, "schema": {"type": "string", "title": "Email"}}, {"name": "code", "in": "query", "required": true, "schema": {"type": "string", "title": "Code"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/protected-test": {"get": {"tags": ["<PERSON><PERSON>"], "summary": "Protected Test", "description": "Test endpoint for protected routes", "operationId": "protected_test_api_protected_test_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}, "security": [{"OAuth2PasswordBearer": []}]}}, "/": {"get": {"summary": "Root", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_upload_image_api_images_upload__post": {"properties": {"file": {"type": "string", "format": "binary", "title": "File", "description": "要上传的图片文件"}, "category_id": {"type": "string", "format": "uuid", "title": "Category Id", "description": "图片所属的类别ID"}, "title": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Title", "description": "图片的可选标题"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "图片的可选描述"}, "tags": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tags"}, "set_as_category_thumbnail": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Set As Category Thumbnail", "description": "是否将此图片设置为类别的缩略图", "default": false}}, "type": "object", "required": ["file", "category_id"], "title": "Body_upload_image_api_images_upload__post"}, "CategoryCreate": {"properties": {"name": {"type": "string", "maxLength": 50, "title": "Name", "description": "类别名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Description", "description": "类别描述"}}, "type": "object", "required": ["name"], "title": "CategoryCreate", "description": "创建类别时使用的模型 (主要用于JSON体，文件通常由API端点参数处理)\n根据文档，前端可能发送包含 name 和 description 的JSON，thumbnail文件作为独立部分。"}, "CategoryRead": {"properties": {"name": {"type": "string", "maxLength": 50, "title": "Name", "description": "类别名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Description", "description": "类别描述"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "thumbnail_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Thumbnail Path"}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true}}, "type": "object", "required": ["name", "id", "created_at", "updated_at", "thumbnail_path", "thumbnail_url"], "title": "CategoryRead", "description": "读取类别信息时使用的模型 (不包含图片列表)"}, "CategoryReadWithImages": {"properties": {"name": {"type": "string", "maxLength": 50, "title": "Name", "description": "类别名称"}, "description": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Description", "description": "类别描述"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}, "thumbnail_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Thumbnail Path"}, "images": {"items": {"$ref": "#/components/schemas/ImageRead"}, "type": "array", "title": "Images", "default": []}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true}}, "type": "object", "required": ["name", "id", "created_at", "updated_at", "thumbnail_path", "thumbnail_url"], "title": "CategoryReadWithImages", "description": "读取类别信息及其关联图片列表时使用的模型"}, "CategoryUpdate": {"properties": {"name": {"anyOf": [{"type": "string", "maxLength": 50}, {"type": "null"}], "title": "Name"}, "description": {"anyOf": [{"type": "string", "maxLength": 300}, {"type": "null"}], "title": "Description"}}, "type": "object", "title": "CategoryUpdate", "description": "更新类别时使用的模型"}, "ExifData": {"properties": {"make": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Make", "description": "相机制造商"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "相机型号"}, "lens_make": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lens Make", "description": "镜头制造商"}, "bits_per_sample": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Bits Per Sample", "description": "每像素位数"}, "date_time_original": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Date Time Original", "description": "原始拍摄日期时间"}, "exposure_time": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Exposure Time", "description": "曝光时间"}, "f_number": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "F Number", "description": "F值"}, "exposure_program": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Exposure Program", "description": "曝光程序"}, "iso_speed_rating": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Iso Speed Rating", "description": "ISO速度"}, "focal_length": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Focal Length", "description": "焦距"}, "lens_specification": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lens Specification", "description": "镜头规格"}, "lens_model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Lens Model", "description": "镜头型号"}, "exposure_mode": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Exposure Mode", "description": "曝光模式"}, "cfa_pattern": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Cfa Pattern", "description": "CFA模式"}, "color_space": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Color Space", "description": "色彩空间"}, "white_balance": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "White Balance", "description": "白平衡"}}, "type": "object", "title": "ExifData", "description": "图片的详细EXIF元数据"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ImageRead": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Title", "description": "图片标题"}, "original_filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Original Filename", "description": "用户上传时的原始文件名"}, "stored_filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Stored Filename", "description": "服务器存储的UUID文件名"}, "relative_file_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Relative File Path", "description": "相对于图片存储根目录的路径"}, "relative_thumbnail_path": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Relative Thumbnail Path", "description": "相对于缩略图存储根目录的路径"}, "mime_type": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Mime Type", "description": "如 image/jpeg"}, "size_bytes": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "<PERSON>ze Bytes", "description": "文件大小"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "图片描述"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "category_id": {"type": "string", "format": "uuid", "title": "Category Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"anyOf": [{"type": "string", "format": "date-time"}, {"type": "null"}], "title": "Updated At"}, "file_metadata": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "File Metadata"}, "exif_info": {"anyOf": [{"$ref": "#/components/schemas/ExifData"}, {"type": "null"}]}, "tags": {"items": {"$ref": "#/components/schemas/TagRead"}, "type": "array", "title": "Tags"}, "image_url": {"type": "string", "title": "Image Url", "readOnly": true}, "thumbnail_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON><PERSON>", "readOnly": true}}, "type": "object", "required": ["id", "category_id", "created_at", "image_url", "thumbnail_url"], "title": "ImageRead", "description": "读取图片信息时使用的模型"}, "ImageUpdate": {"properties": {"title": {"anyOf": [{"type": "string", "maxLength": 255}, {"type": "null"}], "title": "Title", "description": "新的图片标题"}, "description": {"anyOf": [{"type": "string", "maxLength": 500}, {"type": "null"}], "title": "Description", "description": "新的图片描述"}, "tags": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Tags", "description": "逗号分隔的标签字符串 (例如 \"风景,旅行\")"}, "category_id": {"anyOf": [{"type": "string", "format": "uuid"}, {"type": "null"}], "title": "Category Id", "description": "新的类别ID"}, "set_as_category_thumbnail": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Set As Category Thumbnail", "description": "是否将此图片设置为其所属类别的缩略图", "default": false}}, "type": "object", "title": "ImageUpdate", "description": "更新图片元数据时使用的模型"}, "SpeciesRead": {"properties": {"order_details": {"type": "string", "title": "Order Details", "description": "目信息 (例如: 雀形目Passeriformes)"}, "family_details": {"type": "string", "title": "Family Details", "description": "科信息 (例如: 裸鼻雀科 Thraupidae)"}, "genus_details": {"type": "string", "title": "Genus Details", "description": "属信息 (例如: 印加雀属Incaspiza)"}, "name_chinese": {"type": "string", "title": "Name Chinese", "description": "中文种名"}, "name_english": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name English", "description": "英文种名"}, "name_latin": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name Latin", "description": "学名 (拉丁文学名)"}, "href": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "<PERSON><PERSON><PERSON>", "description": "相关链接"}, "pinyin_full": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pinyin Full", "description": "中文名全拼 (小写)"}, "pinyin_initials": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Pinyin Initials", "description": "中文名拼音首字母 (小写)"}, "id": {"type": "integer", "title": "Id"}}, "type": "object", "required": ["order_details", "family_details", "genus_details", "name_chinese", "id"], "title": "SpeciesRead", "description": "用于从API读取/返回物种信息的数据模型 (API输出)"}, "TagRead": {"properties": {"name": {"type": "string", "maxLength": 100, "title": "Name", "description": "标签名称"}, "id": {"type": "string", "format": "uuid", "title": "Id"}, "created_at": {"type": "string", "format": "date-time", "title": "Created At"}, "updated_at": {"type": "string", "format": "date-time", "title": "Updated At"}}, "type": "object", "required": ["name", "id", "created_at", "updated_at"], "title": "TagRead", "description": "读取标签信息时使用的模型"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}, "securitySchemes": {"OAuth2PasswordBearer": {"type": "oauth2", "flows": {"password": {"scopes": {}, "tokenUrl": "api/auth/verify"}}}}}}