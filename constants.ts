export const API_BASE_URL = '/'; // Replace with your actual API base URL

export const IMAGE_BASE_URL = 'http://39.107.88.124:8000'; // Replace with your actual API base URL

// DEFAULT_CATEGORY_THUMBNAIL and DEFAULT_IMAGE_THUMBNAIL have been removed.
// The application will now use internal placeholders (SVG icons on skeleton backgrounds)
// when actual images are not available or fail to load.

export const MAX_CATEGORIES_TO_LOAD_IMAGES_FROM = 2000; // Increased for client-side pagination

// 运行时环境检测函数
export function isElectronEnvironment(): boolean {
  return typeof window !== 'undefined' && window.electronAPI?.isElectron === true;
}

export function getPlatform(): string {
  return typeof window !== 'undefined' ? window.electronAPI?.platform || 'web' : 'web';
}

// PWA功能检测函数
export function isPWAEnvironment(): boolean {
  return !isElectronEnvironment() && typeof window !== 'undefined' && 'serviceWorker' in navigator;
}

// 导出函数用于动态检测（替代静态常量）
export const IS_ELECTRON = isElectronEnvironment();
export const IS_PWA_ENVIRONMENT = isPWAEnvironment();
export const PLATFORM = getPlatform();

console.log('运行环境:', isElectronEnvironment() ? 'Electron' : 'Web');
console.log('PWA环境:', isPWAEnvironment() ? '支持' : '不支持');
console.log('平台:', getPlatform());
