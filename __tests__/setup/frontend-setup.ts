/**
 * 前端测试环境设置
 * 配置React Testing Library、jsdom环境和全局mocks
 */

import { afterEach, beforeAll, afterAll, vi } from 'vitest';
import { cleanup } from '@testing-library/react';
import '@testing-library/jest-dom';

// 全局前端测试设置
beforeAll(() => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  
  // Mock window对象的一些属性
  Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: vi.fn().mockImplementation(query => ({
      matches: false,
      media: query,
      onchange: null,
      addListener: vi.fn(), // deprecated
      removeListener: vi.fn(), // deprecated
      addEventListener: vi.fn(),
      removeEventListener: vi.fn(),
      dispatchEvent: vi.fn(),
    })),
  });

  // Mock localStorage
  const localStorageMock = {
    getItem: vi.fn(),
    setItem: vi.fn(),
    removeItem: vi.fn(),
    clear: vi.fn(),
  };
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock
  });

  // Mock sessionStorage
  Object.defineProperty(window, 'sessionStorage', {
    value: localStorageMock
  });

  // Mock URL.createObjectURL
  Object.defineProperty(URL, 'createObjectURL', {
    writable: true,
    value: vi.fn(() => 'mocked-object-url'),
  });

  // Mock URL.revokeObjectURL
  Object.defineProperty(URL, 'revokeObjectURL', {
    writable: true,
    value: vi.fn(),
  });

  // Mock IntersectionObserver
  global.IntersectionObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // Mock ResizeObserver
  global.ResizeObserver = vi.fn().mockImplementation(() => ({
    observe: vi.fn(),
    unobserve: vi.fn(),
    disconnect: vi.fn(),
  }));

  // 静默console输出以减少测试噪音
  console.log = vi.fn();
  console.warn = vi.fn();
  console.error = vi.fn();
});

afterAll(() => {
  // 恢复console
  vi.restoreAllMocks();
});

// 每个测试后清理
afterEach(() => {
  cleanup();
  vi.clearAllMocks();
});

// 全局测试工具函数
declare global {
  var frontendTestUtils: {
    delay: (ms: number) => Promise<void>;
    waitForNextTick: () => Promise<void>;
    mockLocalStorage: (items?: Record<string, string>) => void;
    clearLocalStorage: () => void;
  };
}

globalThis.frontendTestUtils = {
  // 延迟工具函数
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // 等待下一个事件循环
  waitForNextTick: () => new Promise(resolve => process.nextTick(resolve)),
  
  // Mock localStorage数据
  mockLocalStorage: (items: Record<string, string> = {}) => {
    const mockGetItem = vi.fn((key: string) => items[key] || null);
    const mockSetItem = vi.fn((key: string, value: string) => {
      items[key] = value;
    });
    const mockRemoveItem = vi.fn((key: string) => {
      delete items[key];
    });
    const mockClear = vi.fn(() => {
      Object.keys(items).forEach(key => delete items[key]);
    });

    Object.defineProperty(window, 'localStorage', {
      value: {
        getItem: mockGetItem,
        setItem: mockSetItem,
        removeItem: mockRemoveItem,
        clear: mockClear,
      }
    });
  },
  
  // 清理localStorage mock
  clearLocalStorage: () => {
    vi.clearAllMocks();
  }
};
