/**
 * Electron测试环境设置
 * 用于配置Electron主进程和后端相关的测试环境
 */

import { vi } from 'vitest';
import path from 'path';
import { fileURLToPath } from 'url';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.IS_ELECTRON = 'true';

// Mock Electron相关模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn((name: string) => {
      const testDataPath = path.join(process.cwd(), '__tests__', 'fixtures', 'data');
      switch (name) {
        case 'userData':
          return path.join(testDataPath, 'userData');
        case 'documents':
          return path.join(testDataPath, 'documents');
        case 'pictures':
          return path.join(testDataPath, 'pictures');
        default:
          return testDataPath;
      }
    }),
    getName: vi.fn(() => 'pokedex-test'),
    getVersion: vi.fn(() => '1.0.0-test'),
    isReady: vi.fn(() => Promise.resolve()),
    whenReady: vi.fn(() => Promise.resolve()),
    quit: vi.fn(),
    on: vi.fn(),
    once: vi.fn(),
    removeListener: vi.fn(),
  },
  
  BrowserWindow: vi.fn().mockImplementation(() => ({
    loadFile: vi.fn(),
    loadURL: vi.fn(),
    webContents: {
      send: vi.fn(),
      on: vi.fn(),
      once: vi.fn(),
    },
    on: vi.fn(),
    once: vi.fn(),
    show: vi.fn(),
    hide: vi.fn(),
    close: vi.fn(),
    destroy: vi.fn(),
    isDestroyed: vi.fn(() => false),
  })),
  
  ipcMain: {
    handle: vi.fn(),
    on: vi.fn(),
    once: vi.fn(),
    removeHandler: vi.fn(),
    removeAllListeners: vi.fn(),
  },
  
  dialog: {
    showOpenDialog: vi.fn(),
    showSaveDialog: vi.fn(),
    showMessageBox: vi.fn(),
    showErrorBox: vi.fn(),
  },
  
  shell: {
    openExternal: vi.fn(),
    openPath: vi.fn(),
    showItemInFolder: vi.fn(),
  },
  
  Menu: {
    buildFromTemplate: vi.fn(),
    setApplicationMenu: vi.fn(),
  },
  
  Tray: vi.fn().mockImplementation(() => ({
    setToolTip: vi.fn(),
    setContextMenu: vi.fn(),
    on: vi.fn(),
    destroy: vi.fn(),
  })),
  
  nativeTheme: {
    shouldUseDarkColors: false,
    on: vi.fn(),
    removeListener: vi.fn(),
  },
}));

// Mock Node.js文件系统模块
vi.mock('fs', async () => {
  const actual = await vi.importActual('fs');
  return {
    ...actual,
    promises: {
      ...actual.promises,
      readFile: vi.fn(),
      writeFile: vi.fn(),
      mkdir: vi.fn(),
      readdir: vi.fn(),
      stat: vi.fn(),
      access: vi.fn(),
      unlink: vi.fn(),
      rmdir: vi.fn(),
    },
  };
});

// Mock path模块的一些方法
vi.mock('path', async () => {
  const actual = await vi.importActual('path');
  return {
    ...actual,
    join: vi.fn((...args) => args.join('/')),
    resolve: vi.fn((...args) => '/' + args.join('/')),
    dirname: vi.fn((p) => p.split('/').slice(0, -1).join('/')),
    basename: vi.fn((p) => p.split('/').pop()),
    extname: vi.fn((p) => {
      const parts = p.split('.');
      return parts.length > 1 ? '.' + parts.pop() : '';
    }),
  };
});

// Mock SQLite数据库
vi.mock('sqlite3', () => ({
  Database: vi.fn().mockImplementation(() => ({
    run: vi.fn((sql, params, callback) => {
      if (callback) callback(null);
    }),
    get: vi.fn((sql, params, callback) => {
      if (callback) callback(null, {});
    }),
    all: vi.fn((sql, params, callback) => {
      if (callback) callback(null, []);
    }),
    close: vi.fn((callback) => {
      if (callback) callback(null);
    }),
    serialize: vi.fn((callback) => {
      if (callback) callback();
    }),
    parallelize: vi.fn((callback) => {
      if (callback) callback();
    }),
  })),
  OPEN_READWRITE: 1,
  OPEN_CREATE: 2,
  OPEN_READONLY: 4,
}));

// Mock 图片处理库
vi.mock('sharp', () => ({
  default: vi.fn(() => ({
    resize: vi.fn().mockReturnThis(),
    jpeg: vi.fn().mockReturnThis(),
    png: vi.fn().mockReturnThis(),
    webp: vi.fn().mockReturnThis(),
    toBuffer: vi.fn(() => Promise.resolve(Buffer.from('mock-image-data'))),
    toFile: vi.fn(() => Promise.resolve()),
    metadata: vi.fn(() => Promise.resolve({
      width: 800,
      height: 600,
      format: 'jpeg',
      size: 102400,
    })),
  })),
}));

// Mock EXIF数据提取
vi.mock('exifr', () => ({
  parse: vi.fn(() => Promise.resolve({
    Make: 'Test Camera',
    Model: 'Test Model',
    DateTime: '2025-01-01 12:00:00',
    GPS: {
      latitude: 39.9042,
      longitude: 116.4074,
    },
  })),
}));

// 全局测试工具函数
global.createMockFile = (name: string, content: string = 'mock content') => ({
  name,
  path: `/mock/path/${name}`,
  size: content.length,
  type: 'text/plain',
  lastModified: Date.now(),
  content: Buffer.from(content),
});

global.createMockImageFile = (name: string = 'test.jpg') => ({
  name,
  path: `/mock/images/${name}`,
  size: 102400,
  type: 'image/jpeg',
  lastModified: Date.now(),
  content: Buffer.from('mock-image-data'),
});

// 清理函数
global.cleanupElectronMocks = () => {
  vi.clearAllMocks();
};

console.log('Electron测试环境设置完成');
