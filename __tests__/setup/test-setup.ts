import { beforeEach, afterEach, beforeAll, afterAll, vi } from 'vitest';

// 检测当前测试环境
const isElectronTest = process.env.VITEST_ENVIRONMENT === 'node' ||
                      process.argv.some(arg => arg.includes('electron')) ||
                      process.env.IS_ELECTRON === 'true';

const isFrontendTest = process.env.VITEST_ENVIRONMENT === 'jsdom' ||
                       !isElectronTest;

// 全局测试设置
beforeAll(async () => {
  // 设置基础测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.VITE_API_BASE_URL = 'http://localhost:8000';

  // 静默console.log输出以减少测试噪音
  console.log = vi.fn();
  console.warn = vi.fn();
  console.error = vi.fn();

  // 根据环境加载相应的设置
  if (isElectronTest) {
    try {
      const { setupElectronMocks } = await import('../electron/helpers/electron-mocks');
      setupElectronMocks();
      console.log('Electron test environment loaded');
    } catch (error) {
      console.warn('Failed to load Electron test helpers:', error);
    }
  } else if (isFrontendTest) {
    // 为前端测试设置基础的DOM mocks
    // Mock window.matchMedia
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: vi.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: vi.fn(), // deprecated
        removeListener: vi.fn(), // deprecated
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      })),
    });

    // Mock localStorage
    const localStorageMock = {
      getItem: vi.fn(),
      setItem: vi.fn(),
      removeItem: vi.fn(),
      clear: vi.fn(),
    };
    Object.defineProperty(window, 'localStorage', {
      value: localStorageMock
    });

    // Mock sessionStorage
    Object.defineProperty(window, 'sessionStorage', {
      value: localStorageMock
    });
  }
});

afterAll(async () => {
  // 根据环境清理资源
  if (isElectronTest) {
    try {
      const { cleanupElectronMocks } = await import('../electron/helpers/electron-mocks');
      const { TestDatabaseManager } = await import('../electron/helpers/test-database');

      // 清理所有测试数据库
      TestDatabaseManager.getInstance().cleanupAll();

      // 清理Electron mocks
      cleanupElectronMocks();
    } catch (error) {
      console.warn('Failed to cleanup Electron test resources:', error);
    }
  }

  // 恢复所有mocks
  vi.restoreAllMocks();
});

// 每个测试前的清理
beforeEach(() => {
  // 清理所有mock的调用历史
  vi.clearAllMocks();
});

afterEach(() => {
  // 每个测试后的清理工作可以在这里添加
});

// 全局测试工具函数
declare global {
  var testUtils: {
    delay: (ms: number) => Promise<void>;
    expectAsync: <T>(fn: () => Promise<T>) => Promise<T>;
    mockFetch: (response: any, options?: { status?: number; ok?: boolean }) => void;
    restoreFetch: () => void;
    isElectronTest: boolean;
    isFrontendTest: boolean;
  };
}

globalThis.testUtils = {
  // 延迟工具函数
  delay: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),

  // 异步期望工具函数
  expectAsync: async <T>(fn: () => Promise<T>): Promise<T> => {
    try {
      return await fn();
    } catch (error) {
      throw error;
    }
  },

  // Mock fetch API
  mockFetch: (response: any, options: { status?: number; ok?: boolean } = {}) => {
    const { status = 200, ok = true } = options;
    global.fetch = vi.fn(() =>
      Promise.resolve({
        ok,
        status,
        json: () => Promise.resolve(response),
        text: () => Promise.resolve(JSON.stringify(response)),
        headers: new Headers(),
        redirected: false,
        statusText: ok ? 'OK' : 'Error',
        type: 'basic' as ResponseType,
        url: '',
        clone: vi.fn(),
        body: null,
        bodyUsed: false,
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0)),
        blob: () => Promise.resolve(new Blob()),
        formData: () => Promise.resolve(new FormData()),
      } as Response)
    );
  },

  // 恢复fetch
  restoreFetch: () => {
    vi.restoreAllMocks();
  },

  // 环境检测
  isElectronTest,
  isFrontendTest,
};