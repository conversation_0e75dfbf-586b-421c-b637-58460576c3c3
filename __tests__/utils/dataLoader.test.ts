/**
 * 数据加载工具测试套件
 * 测试跨平台数据文件加载功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import {
  getDataPath,
  getPlatformSpecificPath,
  loadDataWithRetry,
  loadJSONData,
  loadTextData,
  type DataLoadResult,
} from '../../utils/dataLoader';

// Mock fetch
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock window对象
const mockWindow = {
  electronAPI: undefined,
};

describe('数据加载工具测试套件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 重置环境变量
    process.env.NODE_ENV = 'test';
    // 重置window mock
    Object.defineProperty(global, 'window', {
      value: mockWindow,
      writable: true,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('getDataPath函数测试', () => {
    it('应该在Electron环境中返回相对路径', () => {
      // Mock Electron环境
      mockWindow.electronAPI = {};
      
      const path = getDataPath('test.json');
      
      expect(path).toBe('./data/test.json');
    });

    it('应该在开发环境中处理china.json特殊情况', () => {
      // Mock 非Electron环境
      mockWindow.electronAPI = undefined;
      process.env.NODE_ENV = 'development';
      
      const chinaPath = getDataPath('china.json');
      const otherPath = getDataPath('test.json');
      
      expect(chinaPath).toBe('/china.json');
      expect(otherPath).toBe('/data/test.json');
    });

    it('应该在生产环境中正确处理路径', () => {
      // Mock 非Electron环境
      mockWindow.electronAPI = undefined;
      process.env.NODE_ENV = 'production';
      
      const chinaPath = getDataPath('china.json');
      const otherPath = getDataPath('test.json');
      
      expect(chinaPath).toBe('/china.json');
      expect(otherPath).toBe('/data/test.json');
    });

    it('应该处理空文件名', () => {
      const path = getDataPath('');
      
      expect(path).toBe('/data/');
    });

    it('应该处理带路径分隔符的文件名', () => {
      const path = getDataPath('subfolder/test.json');
      
      expect(path).toBe('/data/subfolder/test.json');
    });
  });

  describe('getPlatformSpecificPath函数测试', () => {
    it('应该在Windows平台转换路径分隔符', () => {
      // Mock Windows平台
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        writable: true,
      });
      
      const path = getPlatformSpecificPath('/data/test.json');
      
      expect(path).toBe('\\data\\test.json');
    });

    it('应该在非Windows平台保持原路径', () => {
      // Mock Linux平台
      Object.defineProperty(process, 'platform', {
        value: 'linux',
        writable: true,
      });
      
      const path = getPlatformSpecificPath('/data/test.json');
      
      expect(path).toBe('/data/test.json');
    });

    it('应该处理复杂路径', () => {
      Object.defineProperty(process, 'platform', {
        value: 'win32',
        writable: true,
      });
      
      const path = getPlatformSpecificPath('/data/subfolder/test.json');
      
      expect(path).toBe('\\data\\subfolder\\test.json');
    });
  });

  describe('loadDataWithRetry函数测试', () => {
    it('应该成功加载数据', async () => {
      const mockData = { test: 'data' };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const result = await loadDataWithRetry('/data/test.json');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(mockFetch).toHaveBeenCalledWith('/data/test.json');
    });

    it('应该在失败时重试', async () => {
      const mockData = { test: 'data' };
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve(mockData),
        });

      const result = await loadDataWithRetry('/data/test.json', 3, 100);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(mockFetch).toHaveBeenCalledTimes(3);
    });

    it('应该在达到最大重试次数后失败', async () => {
      mockFetch.mockRejectedValue(new Error('Network error'));

      const result = await loadDataWithRetry('/data/test.json', 2, 100);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Network error');
      expect(mockFetch).toHaveBeenCalledTimes(2);
    });

    it('应该处理HTTP错误状态', async () => {
      mockFetch.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found',
      });

      const result = await loadDataWithRetry('/data/test.json');

      expect(result.success).toBe(false);
      expect(result.error).toContain('404');
    });

    it('应该处理JSON解析错误', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.reject(new Error('Invalid JSON')),
      });

      const result = await loadDataWithRetry('/data/test.json');

      expect(result.success).toBe(false);
      expect(result.error).toContain('Invalid JSON');
    });

    it('应该支持自定义重试延迟', async () => {
      const startTime = Date.now();
      mockFetch
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValue({
          ok: true,
          json: () => Promise.resolve({ test: 'data' }),
        });

      await loadDataWithRetry('/data/test.json', 2, 200);

      const endTime = Date.now();
      expect(endTime - startTime).toBeGreaterThanOrEqual(200);
    });
  });

  describe('loadJSONData函数测试', () => {
    it('应该成功加载JSON数据', async () => {
      const mockData = { name: 'test', value: 123 };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      const result = await loadJSONData<typeof mockData>('test.json');

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
    });

    it('应该处理JSON加载错误', async () => {
      mockFetch.mockRejectedValue(new Error('File not found'));

      const result = await loadJSONData('nonexistent.json');

      expect(result.success).toBe(false);
      expect(result.error).toContain('File not found');
    });

    it('应该验证JSON数据类型', async () => {
      const mockData = { name: 'test', value: 123 };
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(mockData),
      });

      interface TestData {
        name: string;
        value: number;
      }

      const result = await loadJSONData<TestData>('test.json');

      expect(result.success).toBe(true);
      expect(result.data?.name).toBe('test');
      expect(result.data?.value).toBe(123);
    });
  });

  describe('loadTextData函数测试', () => {
    it('应该成功加载文本数据', async () => {
      const mockText = 'This is test content';
      mockFetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve(mockText),
      });

      const result = await loadTextData('test.txt');

      expect(result.success).toBe(true);
      expect(result.data).toBe(mockText);
    });

    it('应该处理文本加载错误', async () => {
      mockFetch.mockRejectedValue(new Error('File not found'));

      const result = await loadTextData('nonexistent.txt');

      expect(result.success).toBe(false);
      expect(result.error).toContain('File not found');
    });

    it('应该处理空文本文件', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        text: () => Promise.resolve(''),
      });

      const result = await loadTextData('empty.txt');

      expect(result.success).toBe(true);
      expect(result.data).toBe('');
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理网络超时', async () => {
      mockFetch.mockImplementation(() => 
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request timeout')), 100);
        })
      );

      const result = await loadDataWithRetry('/data/test.json', 1, 50);

      expect(result.success).toBe(false);
      expect(result.error).toContain('Request timeout');
    });

    it('应该处理无效URL', async () => {
      const result = await loadDataWithRetry('invalid-url');

      expect(result.success).toBe(false);
    });

    it('应该处理空响应', async () => {
      mockFetch.mockResolvedValue({
        ok: true,
        json: () => Promise.resolve(null),
      });

      const result = await loadDataWithRetry('/data/test.json');

      expect(result.success).toBe(true);
      expect(result.data).toBeNull();
    });
  });
});
