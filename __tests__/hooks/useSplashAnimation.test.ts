/**
 * 开屏动画Hook测试套件
 * 测试开屏动画状态管理功能
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { renderHook, act, waitFor } from '@testing-library/react';
import {
  useSplashAnimation,
  SplashAnimationPhase,
  type LoadingTask,
  type SplashAnimationConfig,
} from '../../hooks/useSplashAnimation';

// Mock定时器
vi.useFakeTimers();

describe('开屏动画Hook测试套件', () => {
  beforeEach(() => {
    vi.clearAllTimers();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.runOnlyPendingTimers();
    vi.useRealTimers();
    vi.useFakeTimers();
  });

  describe('基础功能测试', () => {
    it('应该正确初始化默认状态', () => {
      const { result } = renderHook(() => useSplashAnimation());

      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.LOADING);
      expect(result.current.animationState.progress).toBe(0);
      expect(result.current.animationState.isVisible).toBe(true);
      expect(result.current.animationState.hasError).toBe(false);
    });

    it('应该提供控制方法', () => {
      const { result } = renderHook(() => useSplashAnimation());

      expect(typeof result.current.hide).toBe('function');
      expect(typeof result.current.reset).toBe('function');
      expect(typeof result.current.setError).toBe('function');
      expect(typeof result.current.forceTimeout).toBe('function');
    });

    it('应该支持自定义配置', () => {
      const config: SplashAnimationConfig = {
        duration: 5000,
        showTime: 2000,
        progressStep: 5,
        autoHide: false,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 验证初始状态
      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.LOADING);
      expect(result.current.animationState.isVisible).toBe(true);
    });
  });

  describe('动画状态切换测试', () => {
    it('应该正确处理手动隐藏', () => {
      const { result } = renderHook(() => useSplashAnimation());

      act(() => {
        result.current.hide();
      });

      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.HIDDEN);
      expect(result.current.animationState.isVisible).toBe(false);
    });

    it('应该正确处理重置操作', () => {
      const { result } = renderHook(() => useSplashAnimation());

      // 先隐藏
      act(() => {
        result.current.hide();
      });

      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.HIDDEN);

      // 然后重置
      act(() => {
        result.current.reset();
      });

      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.LOADING);
      expect(result.current.animationState.progress).toBe(0);
      expect(result.current.animationState.isVisible).toBe(true);
      expect(result.current.animationState.hasError).toBe(false);
    });

    it('应该正确处理错误状态', () => {
      const { result } = renderHook(() => useSplashAnimation());
      const testError = new Error('Test error');

      act(() => {
        result.current.setError(testError);
      });

      expect(result.current.animationState.hasError).toBe(true);
      expect(result.current.animationState.isVisible).toBe(false);
    });
  });

  describe('进度更新测试', () => {
    it('应该在固定时间模式下更新进度', async () => {
      const onProgressChange = vi.fn();
      const config: SplashAnimationConfig = {
        duration: 1000,
        progressStep: 10,
        onProgressChange,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 推进时间
      act(() => {
        vi.advanceTimersByTime(500); // 50%进度
      });

      await waitFor(() => {
        expect(result.current.animationState.progress).toBeGreaterThan(0);
      });

      expect(onProgressChange).toHaveBeenCalled();
    });

    it('应该在达到100%进度时切换到LOADED状态', async () => {
      const onPhaseChange = vi.fn();
      const config: SplashAnimationConfig = {
        duration: 1000,
        onPhaseChange,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 推进时间到完成
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      await waitFor(() => {
        expect(result.current.animationState.phase).toBe(SplashAnimationPhase.LOADED);
      });

      expect(onPhaseChange).toHaveBeenCalledWith(SplashAnimationPhase.LOADED);
    });
  });

  describe('动态加载模式测试', () => {
    it('应该正确处理加载任务', async () => {
      const mockTask1: LoadingTask = {
        id: 'task1',
        name: 'Task 1',
        loader: vi.fn().mockResolvedValue('result1'),
        weight: 0.5,
      };

      const mockTask2: LoadingTask = {
        id: 'task2',
        name: 'Task 2',
        loader: vi.fn().mockResolvedValue('result2'),
        weight: 0.5,
      };

      const config: SplashAnimationConfig = {
        enableDynamicLoading: true,
        loadingTasks: [mockTask1, mockTask2],
        animationWeight: 0.3,
        dataWeight: 0.7,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 等待加载任务完成
      await waitFor(() => {
        expect(mockTask1.loader).toHaveBeenCalled();
        expect(mockTask2.loader).toHaveBeenCalled();
      });
    });

    it('应该正确计算混合进度', async () => {
      const quickTask: LoadingTask = {
        id: 'quick',
        name: 'Quick Task',
        loader: vi.fn().mockResolvedValue('quick'),
        weight: 1,
      };

      const config: SplashAnimationConfig = {
        enableDynamicLoading: true,
        loadingTasks: [quickTask],
        animationWeight: 0.5,
        dataWeight: 0.5,
        duration: 1000,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 等待任务完成
      await waitFor(() => {
        expect(quickTask.loader).toHaveBeenCalled();
      });

      // 推进动画时间
      act(() => {
        vi.advanceTimersByTime(500); // 50%动画进度
      });

      await waitFor(() => {
        // 总进度应该是动画进度(50% * 0.5) + 数据进度(100% * 0.5) = 75%
        expect(result.current.animationState.progress).toBeGreaterThan(50);
      });
    });

    it('应该处理加载任务失败', async () => {
      const failingTask: LoadingTask = {
        id: 'failing',
        name: 'Failing Task',
        loader: vi.fn().mockRejectedValue(new Error('Task failed')),
        weight: 1,
      };

      const config: SplashAnimationConfig = {
        enableDynamicLoading: true,
        loadingTasks: [failingTask],
      };

      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      const { result } = renderHook(() => useSplashAnimation(config));

      await waitFor(() => {
        expect(failingTask.loader).toHaveBeenCalled();
      });

      // 验证错误被记录但不影响整体流程
      expect(consoleSpy).toHaveBeenCalledWith(
        expect.stringContaining('Loading task failing failed:'),
        expect.any(Error)
      );

      consoleSpy.mockRestore();
    });
  });

  describe('回调函数测试', () => {
    it('应该正确触发阶段变化回调', async () => {
      const onPhaseChange = vi.fn();
      const config: SplashAnimationConfig = {
        duration: 500,
        onPhaseChange,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 手动隐藏触发阶段变化
      act(() => {
        result.current.hide();
      });

      expect(onPhaseChange).toHaveBeenCalledWith(SplashAnimationPhase.HIDDEN);
    });

    it('应该正确触发完成回调', () => {
      const onComplete = vi.fn();
      const config: SplashAnimationConfig = {
        onComplete,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      act(() => {
        result.current.hide();
      });

      expect(onComplete).toHaveBeenCalled();
    });

    it('应该正确触发错误回调', () => {
      const onError = vi.fn();
      const config: SplashAnimationConfig = {
        onError,
      };

      const { result } = renderHook(() => useSplashAnimation(config));
      const testError = new Error('Test error');

      act(() => {
        result.current.setError(testError);
      });

      expect(onError).toHaveBeenCalledWith(testError);
    });

    it('应该正确触发超时回调', () => {
      const onTimeout = vi.fn();
      const config: SplashAnimationConfig = {
        maxTimeout: 1000,
        onTimeout,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      // 推进时间到超时
      act(() => {
        vi.advanceTimersByTime(1000);
      });

      expect(onTimeout).toHaveBeenCalled();
    });
  });

  describe('内存泄漏防护测试', () => {
    it('应该在组件卸载时清理定时器', () => {
      const clearTimeoutSpy = vi.spyOn(global, 'clearTimeout');
      const clearIntervalSpy = vi.spyOn(global, 'clearInterval');

      const { unmount } = renderHook(() => useSplashAnimation());

      unmount();

      // 验证定时器被清理（可能被调用多次）
      expect(clearTimeoutSpy).toHaveBeenCalled();
      expect(clearIntervalSpy).toHaveBeenCalled();

      clearTimeoutSpy.mockRestore();
      clearIntervalSpy.mockRestore();
    });

    it('应该防止在组件卸载后更新状态', () => {
      const { result, unmount } = renderHook(() => useSplashAnimation());

      unmount();

      // 尝试在卸载后调用方法，不应该抛出错误
      expect(() => {
        act(() => {
          result.current.hide();
        });
      }).not.toThrow();
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空的加载任务列表', () => {
      const config: SplashAnimationConfig = {
        enableDynamicLoading: true,
        loadingTasks: [],
      };

      expect(() => {
        renderHook(() => useSplashAnimation(config));
      }).not.toThrow();
    });

    it('应该处理极短的持续时间', async () => {
      const config: SplashAnimationConfig = {
        duration: 1,
        minDuration: 1,
      };

      const { result } = renderHook(() => useSplashAnimation(config));

      act(() => {
        vi.advanceTimersByTime(1);
      });

      await waitFor(() => {
        expect(result.current.animationState.progress).toBeGreaterThan(0);
      });
    });

    it('应该处理重复的隐藏调用', () => {
      const { result } = renderHook(() => useSplashAnimation());

      act(() => {
        result.current.hide();
        result.current.hide(); // 重复调用
      });

      expect(result.current.animationState.phase).toBe(SplashAnimationPhase.HIDDEN);
    });
  });
});
