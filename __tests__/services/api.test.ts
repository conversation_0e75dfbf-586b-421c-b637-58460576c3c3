/**
 * API服务测试套件
 * 测试所有API函数的功能、错误处理和多环境支持
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import axios from 'axios';
import {
  setupAPIMocks,
  cleanupAPIMocks,
  mockCategory,
  mockCategoryListResponse,
  mockImage,
  mockTag,
  mockSpecies,
  mockTokenResponse,
  createErrorMocks,
} from '../mocks/api-mocks';

// 导入要测试的API函数
import {
  // 认证相关
  sendVerificationCode,
  verifyCodeAndGetToken,
  // 分类相关
  getCategories,
  createCategory,
  updateCategory,
  deleteCategory,
  getCategoryWithImages,
  getCategoryByName,
  // 图片相关
  uploadImage,
  getImage,
  updateImageMetadata,
  deleteImage,
  deleteImages,
  searchImagesByTag,
  // 标签相关
  getAllTags,
  createTag,
  updateTag,
  deleteTag,
  searchTags,
  addTagToImage,
  removeTagFromImage,
  getTagsForImage,
  // 物种相关
  getSpeciesSuggestions,
  getSpeciesDetails,
} from '../../services/api';

describe('API服务测试套件', () => {
  let mocks: ReturnType<typeof setupAPIMocks>;

  beforeEach(() => {
    mocks = setupAPIMocks();
    // 重置IS_ELECTRON常量
    vi.doMock('../../constants', () => ({
      IS_ELECTRON: false,
      API_BASE_URL: 'http://localhost:8000',
    }));
  });

  afterEach(() => {
    cleanupAPIMocks();
    vi.clearAllMocks();
  });

  describe('认证相关API', () => {
    it('应该成功发送验证码', async () => {
      mocks.axiosMock.post.mockResolvedValue({ data: { success: true } });

      const result = await sendVerificationCode('<EMAIL>');

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/auth/send-verification-code/', {
        email: '<EMAIL>',
      });
      expect(result).toEqual({ success: true });
    });

    it('应该成功验证码并获取token', async () => {
      mocks.axiosMock.post.mockResolvedValue({ data: mockTokenResponse });

      const result = await verifyCodeAndGetToken('<EMAIL>', '123456');

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/auth/verify-code/', {
        email: '<EMAIL>',
        code: '123456',
      });
      expect(result).toEqual(mockTokenResponse);
    });

    it('应该处理验证错误', async () => {
      const errorMocks = createErrorMocks();
      mocks.axiosMock.post.mockRejectedValue(errorMocks.validationError);

      await expect(verifyCodeAndGetToken('', '123456')).rejects.toThrow();
    });
  });

  describe('分类相关API', () => {
    it('应该获取分类列表', async () => {
      mocks.axiosMock.get.mockResolvedValue({ data: mockCategoryListResponse });

      const result = await getCategories(0, 100);

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/categories/?skip=0&limit=100');
      expect(result).toEqual(mockCategoryListResponse);
    });

    it('应该创建新分类', async () => {
      const categoryData = { name: 'New Category', description: 'Description' };
      mocks.axiosMock.post.mockResolvedValue({ data: mockCategory });

      const result = await createCategory(categoryData);

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/categories/', categoryData);
      expect(result).toEqual(mockCategory);
    });

    it('应该更新分类', async () => {
      const updateData = { name: 'Updated Category' };
      mocks.axiosMock.patch.mockResolvedValue({ data: { ...mockCategory, ...updateData } });

      const result = await updateCategory('1', updateData);

      expect(mocks.axiosMock.patch).toHaveBeenCalledWith('/api/categories/1/', updateData);
      expect(result.name).toBe('Updated Category');
    });

    it('应该删除分类', async () => {
      mocks.axiosMock.delete.mockResolvedValue({ data: { success: true } });

      const result = await deleteCategory('1');

      expect(mocks.axiosMock.delete).toHaveBeenCalledWith('/api/categories/1/');
      expect(result).toEqual({ success: true });
    });

    it('应该根据名称获取分类详情', async () => {
      mocks.axiosMock.get.mockResolvedValue({ data: mockCategory });

      const result = await getCategoryByName('Test Category');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/categories/by-name/Test%20Category/');
      expect(result).toEqual(mockCategory);
    });

    it('应该获取分类及其图片', async () => {
      const mockCategoryWithImages = { ...mockCategory, images: [mockImage] };
      mocks.axiosMock.get.mockResolvedValue({ data: mockCategoryWithImages });

      const result = await getCategoryWithImages('1');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/categories/1/');
      expect(result).toEqual(mockCategoryWithImages);
    });
  });

  describe('图片相关API', () => {
    it('应该获取单个图片', async () => {
      mocks.axiosMock.get.mockResolvedValue({ data: mockImage });

      const result = await getImage('1');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/images/1/');
      expect(result).toEqual(mockImage);
    });

    it('应该上传图片', async () => {
      const file = new File(['test'], 'test.jpg', { type: 'image/jpeg' });
      const uploadData = { file, category_id: '1' };
      mocks.axiosMock.post.mockResolvedValue({ data: mockImage });

      const result = await uploadImage(uploadData);

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/images/upload/', expect.any(FormData), {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      expect(result).toEqual(mockImage);
    });

    it('应该更新图片元数据', async () => {
      const updateData = { description: 'Updated description' };
      mocks.axiosMock.patch.mockResolvedValue({ data: { ...mockImage, ...updateData } });

      const result = await updateImageMetadata('1', updateData);

      expect(mocks.axiosMock.patch).toHaveBeenCalledWith('/api/images/1/', updateData);
      expect(result.description).toBe('Updated description');
    });

    it('应该删除单个图片', async () => {
      mocks.axiosMock.delete.mockResolvedValue({ data: { success: true } });

      const result = await deleteImage('1');

      expect(mocks.axiosMock.delete).toHaveBeenCalledWith('/api/images/1/');
      expect(result).toEqual({ success: true });
    });

    it('应该批量删除图片', async () => {
      const imageIds = ['1', '2', '3'];
      const mockBatchResult = {
        totalCount: 3,
        successCount: 3,
        failedCount: 0,
        results: [],
        errors: [],
      };
      mocks.axiosMock.post.mockResolvedValue({ data: mockBatchResult });

      const result = await deleteImages(imageIds);

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/images/batch-delete/', {
        image_ids: imageIds,
      });
      expect(result).toEqual(mockBatchResult);
    });

    it('应该根据标签搜索图片', async () => {
      const mockSearchResult = [mockImage];
      mocks.axiosMock.get.mockResolvedValue({ data: mockSearchResult });

      const result = await searchImagesByTag('nature,landscape');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/images/search-by-tags/', {
        params: { tags: 'nature,landscape' },
      });
      expect(result).toEqual(mockSearchResult);
    });
  });

  describe('标签相关API', () => {
    it('应该获取所有标签', async () => {
      const mockTagList = [mockTag];
      mocks.axiosMock.get.mockResolvedValue({ data: mockTagList });

      const result = await getAllTags();

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/tags/');
      expect(result).toEqual(mockTagList);
    });

    it('应该创建新标签', async () => {
      const tagData = { name: 'New Tag', color: '#FF0000' };
      mocks.axiosMock.post.mockResolvedValue({ data: mockTag });

      const result = await createTag(tagData);

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/tags/', tagData);
      expect(result).toEqual(mockTag);
    });

    it('应该更新标签', async () => {
      const updateData = { name: 'Updated Tag' };
      mocks.axiosMock.patch.mockResolvedValue({ data: { ...mockTag, ...updateData } });

      const result = await updateTag('1', updateData);

      expect(mocks.axiosMock.patch).toHaveBeenCalledWith('/api/tags/1/', updateData);
      expect(result.name).toBe('Updated Tag');
    });

    it('应该删除标签', async () => {
      mocks.axiosMock.delete.mockResolvedValue({ data: undefined });

      await deleteTag('1');

      expect(mocks.axiosMock.delete).toHaveBeenCalledWith('/api/tags/1/');
    });

    it('应该搜索标签', async () => {
      const mockSearchResult = [mockTag];
      mocks.axiosMock.get.mockResolvedValue({ data: mockSearchResult });

      const result = await searchTags('test');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/tags/search/?q=test');
      expect(result).toEqual(mockSearchResult);
    });

    it('应该为图片添加标签', async () => {
      mocks.axiosMock.post.mockResolvedValue({ data: undefined });

      await addTagToImage('1', '1');

      expect(mocks.axiosMock.post).toHaveBeenCalledWith('/api/images/1/tags/1/');
    });

    it('应该从图片移除标签', async () => {
      mocks.axiosMock.delete.mockResolvedValue({ data: undefined });

      await removeTagFromImage('1', '1');

      expect(mocks.axiosMock.delete).toHaveBeenCalledWith('/api/images/1/tags/1/');
    });

    it('应该获取图片的标签', async () => {
      const mockImageTags = [mockTag];
      mocks.axiosMock.get.mockResolvedValue({ data: mockImageTags });

      const result = await getTagsForImage('1');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/images/1/tags/');
      expect(result).toEqual(mockImageTags);
    });
  });

  describe('物种相关API', () => {
    it('应该获取物种建议', async () => {
      const mockSuggestions = { suggestions: [mockSpecies], total: 1 };
      mocks.axiosMock.get.mockResolvedValue({ data: mockSuggestions });

      const result = await getSpeciesSuggestions('test', 10);

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/suggestions?q=test&limit=10');
      expect(result).toEqual(mockSuggestions);
    });

    it('应该获取物种详情', async () => {
      mocks.axiosMock.get.mockResolvedValue({ data: mockSpecies });

      const result = await getSpeciesDetails('测试物种');

      expect(mocks.axiosMock.get).toHaveBeenCalledWith('/api/details/%E6%B5%8B%E8%AF%95%E7%89%A9%E7%A7%8D');
      expect(result).toEqual(mockSpecies);
    });
  });

  describe('错误处理测试', () => {
    it('应该处理网络错误', async () => {
      const errorMocks = createErrorMocks();
      mocks.axiosMock.get.mockRejectedValue(errorMocks.networkError);

      await expect(getCategories()).rejects.toThrow('Network Error');
    });

    it('应该处理验证错误', async () => {
      const errorMocks = createErrorMocks();
      mocks.axiosMock.post.mockRejectedValue(errorMocks.validationError);

      await expect(createCategory({ name: '', description: '' })).rejects.toThrow();
    });

    it('应该处理服务器错误', async () => {
      const errorMocks = createErrorMocks();
      mocks.axiosMock.get.mockRejectedValue(errorMocks.serverError);

      await expect(getCategories()).rejects.toThrow();
    });

    it('应该处理超时错误', async () => {
      const timeoutError = { code: 'ECONNABORTED', message: 'timeout of 5000ms exceeded' };
      mocks.axiosMock.get.mockRejectedValue(timeoutError);

      await expect(getCategories()).rejects.toThrow();
    });
  });

  describe('Electron环境测试', () => {
    beforeEach(() => {
      // Mock IS_ELECTRON为true
      vi.doMock('../../constants', () => ({
        IS_ELECTRON: true,
        API_BASE_URL: 'http://localhost:8000',
      }));
    });

    it('应该在Electron环境中使用electronAPI进行认证', async () => {
      // 重新导入以获取更新的IS_ELECTRON值
      const { login: electronLogin } = await import('../../services/api');

      const result = await electronLogin('testuser', 'password');

      expect(mocks.electronAPIMock.auth.login).toHaveBeenCalledWith('testuser', 'password');
      expect(result).toEqual(mockTokenResponse);
    });

    it('应该在Electron环境中使用electronAPI进行分类操作', async () => {
      const { getCategories: electronGetCategories } = await import('../../services/api');

      const result = await electronGetCategories();

      expect(mocks.electronAPIMock.categories.getCategories).toHaveBeenCalled();
      expect(result).toEqual(mockCategoryListResponse);
    });

    it('应该在Electron环境中处理API错误', async () => {
      mocks.electronAPIMock.categories.getCategories.mockResolvedValue({
        success: false,
        error: 'Database connection failed',
      });

      const { getCategories: electronGetCategories } = await import('../../services/api');

      await expect(electronGetCategories()).rejects.toThrow('Database connection failed');
    });
  });

  describe('请求拦截器和响应拦截器', () => {
    it('应该在请求中添加认证头', async () => {
      // Mock localStorage中的token
      frontendTestUtils.mockLocalStorage({ access_token: 'test-token' });

      await getCategories();

      // 验证请求拦截器是否添加了Authorization头
      expect(mocks.axiosMock.interceptors.request.use).toHaveBeenCalled();
    });

    it('应该处理401未授权响应', async () => {
      const unauthorizedError = {
        response: {
          status: 401,
          data: { detail: 'Unauthorized' },
        },
      };
      mocks.axiosMock.get.mockRejectedValue(unauthorizedError);

      await expect(getCategories()).rejects.toThrow();
    });

    it('应该处理token刷新', async () => {
      // Mock token过期的响应
      const expiredTokenError = {
        response: {
          status: 401,
          data: { detail: 'Token expired' },
        },
      };

      // 第一次请求失败，第二次成功
      mocks.axiosMock.get
        .mockRejectedValueOnce(expiredTokenError)
        .mockResolvedValueOnce({ data: mockCategoryListResponse });

      // Mock refresh token请求
      mocks.axiosMock.post.mockResolvedValue({ data: mockTokenResponse });

      const result = await getCategories();

      expect(result).toEqual(mockCategoryListResponse);
    });
  });

  describe('异步操作和Promise处理', () => {
    it('应该正确处理并发请求', async () => {
      mocks.axiosMock.get.mockResolvedValue({ data: mockCategoryListResponse });

      const promises = [
        getCategories(),
        getCategories(),
        getCategories(),
      ];

      const results = await Promise.all(promises);

      expect(results).toHaveLength(3);
      results.forEach(result => {
        expect(result).toEqual(mockCategoryListResponse);
      });
    });

    it('应该正确处理Promise.allSettled', async () => {
      mocks.axiosMock.get
        .mockResolvedValueOnce({ data: mockCategoryListResponse })
        .mockRejectedValueOnce(new Error('Network error'))
        .mockResolvedValueOnce({ data: mockCategoryListResponse });

      const promises = [
        getCategories(),
        getCategories(),
        getCategories(),
      ];

      const results = await Promise.allSettled(promises);

      expect(results[0].status).toBe('fulfilled');
      expect(results[1].status).toBe('rejected');
      expect(results[2].status).toBe('fulfilled');
    });

    it('应该支持请求取消', async () => {
      const abortController = new AbortController();

      // 模拟取消请求
      setTimeout(() => abortController.abort(), 100);

      mocks.axiosMock.get.mockImplementation(() =>
        new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Request aborted')), 200);
        })
      );

      await expect(getCategories()).rejects.toThrow();
    });
  });
});
