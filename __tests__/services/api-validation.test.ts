/**
 * API验证工具测试套件
 * 测试API响应验证和错误处理功能
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import {
  safeParseApiResponse,
  formatApiErrorWithZod,
  formatApiError,
} from '../../services/api-validation';
import {
  CategoryReadSchema,
  CategoryListResponseSchema,
  ImageReadSchema,
  TagReadSchema,
  isValidationError,
  createApiError,
  zodErrorToApiError,
} from '../../schemas';
import type { HTTPValidationError, ApiError } from '../../schemas';

describe('API验证工具测试套件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('safeParseApiResponse函数测试', () => {
    it('应该成功验证有效的分类数据', () => {
      const validCategoryData = {
        id: '1',
        name: 'Test Category',
        description: 'Test Description',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        thumbnail_path: '/thumbnails/test.jpg',
        thumbnail_url: '/thumbnails/test.jpg',
      };

      const result = safeParseApiResponse(CategoryReadSchema, validCategoryData);

      expect(result).toEqual(validCategoryData);
    });

    it('应该拒绝无效的分类数据', () => {
      const invalidCategoryData = {
        id: '1',
        name: '', // 空名称应该无效
        description: 'Test Description',
        created_at: 'invalid-date', // 无效日期格式
        updated_at: '2025-01-01T00:00:00Z',
        thumbnail_path: '/thumbnails/test.jpg',
        thumbnail_url: '/thumbnails/test.jpg',
      };

      expect(() => {
        safeParseApiResponse(CategoryReadSchema, invalidCategoryData);
      }).toThrow();
    });

    it('应该成功验证分类列表响应', () => {
      const validListResponse = [
        {
          id: '1',
          name: 'Category 1',
          description: 'Description 1',
          created_at: '2025-01-01T00:00:00Z',
          updated_at: '2025-01-01T00:00:00Z',
          thumbnail_path: '/thumbnails/cat1.jpg',
          thumbnail_url: '/thumbnails/cat1.jpg',
        },
      ];

      const result = safeParseApiResponse(CategoryListResponseSchema, validListResponse);

      expect(result).toEqual(validListResponse);
    });

    it('应该处理缺少必需字段的数据', () => {
      const incompleteData = {
        id: '1',
        name: 'Test Category',
        // 缺少其他必需字段
      };

      expect(() => {
        safeParseApiResponse(CategoryReadSchema, incompleteData);
      }).toThrow();
    });

    it('应该验证图片数据', () => {
      const validImageData = {
        id: '1',
        category_id: '1',
        title: 'Test Image',
        original_filename: 'test.jpg',
        stored_filename: 'stored-test.jpg',
        relative_file_path: '/images/test.jpg',
        relative_thumbnail_path: '/thumbnails/test.jpg',
        mime_type: 'image/jpeg',
        size_bytes: 1024000,
        description: 'Test description',
        tags: [],
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        file_metadata: {},
        exif_info: null,
        image_url: '/images/test.jpg',
        thumbnail_url: '/thumbnails/test.jpg',
        set_as_category_thumbnail: false,
      };

      const result = safeParseApiResponse(ImageReadSchema, validImageData);

      expect(result).toEqual(validImageData);
    });

    it('应该验证标签数据', () => {
      const validTagData = {
        id: '1',
        name: 'Test Tag',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
      };

      const result = safeParseApiResponse(TagReadSchema, validTagData);

      expect(result).toEqual(validTagData);
    });
  });

  describe('handleApiError函数测试', () => {
    it('应该处理网络错误', () => {
      const networkError = new Error('Network Error');
      
      const result = handleApiError(networkError);

      expect(result.type).toBe('network');
      expect(result.message).toContain('Network Error');
      expect(result.details).toBeDefined();
    });

    it('应该处理HTTP错误响应', () => {
      const httpError = {
        response: {
          status: 404,
          statusText: 'Not Found',
          data: { detail: 'Resource not found' },
        },
      };

      const result = handleApiError(httpError);

      expect(result.type).toBe('http');
      expect(result.status).toBe(404);
      expect(result.message).toContain('Not Found');
    });

    it('应该处理验证错误', () => {
      const validationError = {
        response: {
          status: 422,
          data: {
            detail: [
              {
                loc: ['body', 'name'],
                msg: 'field required',
                type: 'value_error.missing',
              },
            ],
          },
        },
      };

      const result = handleApiError(validationError);

      expect(result.type).toBe('validation');
      expect(result.status).toBe(422);
      expect(result.validationErrors).toBeDefined();
      expect(result.validationErrors?.length).toBe(1);
    });

    it('应该处理服务器错误', () => {
      const serverError = {
        response: {
          status: 500,
          data: { detail: 'Internal Server Error' },
        },
      };

      const result = handleApiError(serverError);

      expect(result.type).toBe('server');
      expect(result.status).toBe(500);
      expect(result.message).toContain('Internal Server Error');
    });

    it('应该处理未知错误', () => {
      const unknownError = 'Unknown error';

      const result = handleApiError(unknownError);

      expect(result.type).toBe('unknown');
      expect(result.message).toContain('Unknown error');
    });
  });

  describe('transformValidationError函数测试', () => {
    it('应该转换验证错误详情', () => {
      const validationDetail: HTTPValidationError = {
        detail: [
          {
            loc: ['body', 'name'],
            msg: 'field required',
            type: 'value_error.missing',
          },
          {
            loc: ['body', 'email'],
            msg: 'invalid email format',
            type: 'value_error.email',
          },
        ],
      };

      const result = transformValidationError(validationDetail);

      expect(result).toHaveLength(2);
      expect(result[0]).toEqual({
        field: 'name',
        message: 'field required',
        type: 'value_error.missing',
        location: ['body', 'name'],
      });
      expect(result[1]).toEqual({
        field: 'email',
        message: 'invalid email format',
        type: 'value_error.email',
        location: ['body', 'email'],
      });
    });

    it('应该处理嵌套字段路径', () => {
      const validationDetail: HTTPValidationError = {
        detail: [
          {
            loc: ['body', 'user', 'profile', 'name'],
            msg: 'field required',
            type: 'value_error.missing',
          },
        ],
      };

      const result = transformValidationError(validationDetail);

      expect(result[0].field).toBe('profile.name');
      expect(result[0].location).toEqual(['body', 'user', 'profile', 'name']);
    });

    it('应该处理空验证错误', () => {
      const validationDetail: HTTPValidationError = {
        detail: [],
      };

      const result = transformValidationError(validationDetail);

      expect(result).toHaveLength(0);
    });
  });

  describe('isValidationError函数测试', () => {
    it('应该识别验证错误', () => {
      const validationError = {
        response: {
          status: 422,
          data: {
            detail: [
              {
                loc: ['body', 'name'],
                msg: 'field required',
                type: 'value_error.missing',
              },
            ],
          },
        },
      };

      expect(isValidationError(validationError)).toBe(true);
    });

    it('应该识别非验证错误', () => {
      const networkError = new Error('Network Error');
      const serverError = { response: { status: 500 } };

      expect(isValidationError(networkError)).toBe(false);
      expect(isValidationError(serverError)).toBe(false);
    });
  });

  describe('createApiError函数测试', () => {
    it('应该创建标准API错误', () => {
      const error = createApiError('test', 'Test error message', 400);

      expect(error.type).toBe('test');
      expect(error.message).toBe('Test error message');
      expect(error.status).toBe(400);
      expect(error.timestamp).toBeDefined();
    });

    it('应该创建带详情的API错误', () => {
      const details = { field: 'name', value: 'invalid' };
      const error = createApiError('validation', 'Validation failed', 422, details);

      expect(error.type).toBe('validation');
      expect(error.message).toBe('Validation failed');
      expect(error.status).toBe(422);
      expect(error.details).toEqual(details);
    });

    it('应该设置默认状态码', () => {
      const error = createApiError('unknown', 'Unknown error');

      expect(error.status).toBe(500);
    });
  });

  describe('边界情况和错误处理', () => {
    it('应该处理null数据验证', () => {
      const result = validateApiResponse(CategoryReadSchema, null);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('应该处理undefined数据验证', () => {
      const result = validateApiResponse(CategoryReadSchema, undefined);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
    });

    it('应该处理循环引用数据', () => {
      const circularData: any = { id: '1', name: 'Test' };
      circularData.self = circularData;

      const result = validateApiResponse(CategoryReadSchema, circularData);

      expect(result.success).toBe(false);
    });

    it('应该处理大型数据对象', () => {
      const largeData = {
        id: '1',
        name: 'A'.repeat(10000), // 很长的名称
        description: 'B'.repeat(50000), // 很长的描述
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z',
        image_count: 999999,
      };

      const result = validateApiResponse(CategoryReadSchema, largeData);

      // 根据schema的具体验证规则，这可能成功或失败
      expect(result.success).toBeDefined();
    });
  });
});
