/**
 * 主题Context测试套件
 * 测试主题状态管理功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, act } from '@testing-library/react';
import {
  ThemeProvider,
  useTheme,
  type ThemeName,
  themeSettings,
} from '../../contexts/ThemeContext';

// Mock localStorage
const mockLocalStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock matchMedia
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(),
    removeListener: vi.fn(),
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock document
Object.defineProperty(document, 'documentElement', {
  value: {
    classList: {
      add: vi.fn(),
      remove: vi.fn(),
    },
  },
});

Object.defineProperty(document, 'body', {
  value: {
    className: '',
    classList: {
      add: vi.fn(),
    },
  },
});

// 测试组件，用于测试useTheme hook
const TestComponent: React.FC = () => {
  const { themeName, theme, setThemeName, isDarkMode, toggleDarkMode } = useTheme();
  
  return (
    <div>
      <div data-testid="theme-name">{themeName}</div>
      <div data-testid="theme-display-name">{theme.name}</div>
      <div data-testid="dark-mode-status">
        {isDarkMode ? 'dark' : 'light'}
      </div>
      <button 
        data-testid="set-modern-btn" 
        onClick={() => setThemeName('modern')}
      >
        Set Modern
      </button>
      <button 
        data-testid="set-retro-btn" 
        onClick={() => setThemeName('retroTechDark')}
      >
        Set Retro
      </button>
      <button 
        data-testid="toggle-dark-btn" 
        onClick={toggleDarkMode}
      >
        Toggle Dark
      </button>
    </div>
  );
};

// 包装组件，提供ThemeProvider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    {children}
  </ThemeProvider>
);

describe('主题Context测试套件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue(null);
    // 重置document mock
    (document.documentElement.classList.add as any).mockClear();
    (document.documentElement.classList.remove as any).mockClear();
    (document.body.classList.add as any).mockClear();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('ThemeProvider初始化测试', () => {
    it('应该使用默认主题初始化', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('theme-name')).toHaveTextContent('retroTechDark');
      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Retro Tech Dark');
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
    });

    it('应该从localStorage加载保存的主题', () => {
      mockLocalStorage.getItem.mockReturnValue('modern');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('theme-name')).toHaveTextContent('modern');
      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Modern Clean Pro');
    });

    it('应该忽略无效的localStorage主题值', () => {
      mockLocalStorage.getItem.mockReturnValue('invalid-theme');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('theme-name')).toHaveTextContent('retroTechDark');
    });

    it('应该从系统偏好检测暗色模式', () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'appTheme') return 'modern';
        if (key === 'darkMode') return null;
        return null;
      });

      // Mock系统偏好为暗色模式
      (window.matchMedia as any).mockImplementation(query => ({
        matches: query === '(prefers-color-scheme: dark)',
        media: query,
        onchange: null,
        addListener: vi.fn(),
        removeListener: vi.fn(),
        addEventListener: vi.fn(),
        removeEventListener: vi.fn(),
        dispatchEvent: vi.fn(),
      }));

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
    });
  });

  describe('主题切换测试', () => {
    it('应该正确切换到modern主题', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      act(() => {
        screen.getByTestId('set-modern-btn').click();
      });

      expect(screen.getByTestId('theme-name')).toHaveTextContent('modern');
      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Modern Clean Pro');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('appTheme', 'modern');
    });

    it('应该正确切换到retroTechDark主题', () => {
      mockLocalStorage.getItem.mockReturnValue('modern');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      act(() => {
        screen.getByTestId('set-retro-btn').click();
      });

      expect(screen.getByTestId('theme-name')).toHaveTextContent('retroTechDark');
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('appTheme', 'retroTechDark');
    });

    it('应该在切换到retroTechDark时强制启用暗色模式', () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'appTheme') return 'modern';
        if (key === 'darkMode') return 'false';
        return null;
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始应该是亮色模式
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('light');

      act(() => {
        screen.getByTestId('set-retro-btn').click();
      });

      // 切换到retroTechDark后应该强制为暗色模式
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
    });
  });

  describe('暗色模式切换测试', () => {
    it('应该正确切换暗色模式', () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'appTheme') return 'modern';
        if (key === 'darkMode') return 'false';
        return null;
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('light');

      act(() => {
        screen.getByTestId('toggle-dark-btn').click();
      });

      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'true');
    });

    it('应该在retroTechDark主题下禁用暗色模式切换', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 默认是retroTechDark主题，应该是暗色模式
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');

      act(() => {
        screen.getByTestId('toggle-dark-btn').click();
      });

      // 应该保持暗色模式，不能切换
      expect(screen.getByTestId('dark-mode-status')).toHaveTextContent('dark');
    });
  });

  describe('DOM操作测试', () => {
    it('应该正确设置document.documentElement的class', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // retroTechDark主题应该添加dark class
      expect(document.documentElement.classList.add).toHaveBeenCalledWith('dark');
    });

    it('应该在切换到亮色模式时移除dark class', () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'appTheme') return 'modern';
        if (key === 'darkMode') return 'true';
        return null;
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      act(() => {
        screen.getByTestId('toggle-dark-btn').click();
      });

      expect(document.documentElement.classList.remove).toHaveBeenCalledWith('dark');
      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'false');
    });

    it('应该正确设置body的class', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(document.body.className).toBe('antialiased');
      expect(document.body.classList.add).toHaveBeenCalled();
    });
  });

  describe('localStorage持久化测试', () => {
    it('应该保存主题名称到localStorage', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      act(() => {
        screen.getByTestId('set-modern-btn').click();
      });

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('appTheme', 'modern');
    });

    it('应该保存暗色模式状态到localStorage', () => {
      mockLocalStorage.getItem.mockImplementation((key) => {
        if (key === 'appTheme') return 'modern';
        if (key === 'darkMode') return 'false';
        return null;
      });

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      act(() => {
        screen.getByTestId('toggle-dark-btn').click();
      });

      expect(mockLocalStorage.setItem).toHaveBeenCalledWith('darkMode', 'true');
    });
  });

  describe('主题样式测试', () => {
    it('应该返回正确的主题样式对象', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 验证主题样式对象存在且包含预期属性
      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Retro Tech Dark');
    });

    it('应该在主题切换时更新样式对象', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Retro Tech Dark');

      act(() => {
        screen.getByTestId('set-modern-btn').click();
      });

      expect(screen.getByTestId('theme-display-name')).toHaveTextContent('Modern Clean Pro');
    });
  });

  describe('useTheme hook测试', () => {
    it('应该在ThemeProvider外部使用时抛出错误', () => {
      // 使用console.error的mock来避免测试输出中的错误信息
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useTheme must be used within a ThemeProvider');

      consoleSpy.mockRestore();
    });

    it('应该正确返回主题状态和方法', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 验证组件能正常渲染，说明hook返回了正确的值
      expect(screen.getByTestId('theme-name')).toBeInTheDocument();
      expect(screen.getByTestId('theme-display-name')).toBeInTheDocument();
      expect(screen.getByTestId('dark-mode-status')).toBeInTheDocument();
      expect(screen.getByTestId('set-modern-btn')).toBeInTheDocument();
      expect(screen.getByTestId('set-retro-btn')).toBeInTheDocument();
      expect(screen.getByTestId('toggle-dark-btn')).toBeInTheDocument();
    });
  });

  describe('边界情况测试', () => {
    it('应该处理localStorage访问失败', () => {
      mockLocalStorage.getItem.mockImplementation(() => {
        throw new Error('localStorage not available');
      });

      expect(() => {
        render(
          <TestWrapper>
            <TestComponent />
          </TestWrapper>
        );
      }).not.toThrow();

      // 应该使用默认主题
      expect(screen.getByTestId('theme-name')).toHaveTextContent('retroTechDark');
    });

    it('应该处理无效的主题名称', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 尝试设置无效主题（通过直接调用不会发生，但测试健壮性）
      expect(screen.getByTestId('theme-name')).toHaveTextContent('retroTechDark');
    });
  });
});
