/**
 * 认证Context测试套件
 * 测试认证状态管理功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, act, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { AuthProvider, useAuth } from '../../contexts/AuthContext';

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
    useLocation: () => ({ pathname: '/' }),
  };
});

// Mock constants
vi.mock('../../constants', () => ({
  IS_ELECTRON: false, // 默认为Web模式
}));

// 测试组件，用于测试useAuth hook
const TestComponent: React.FC = () => {
  const { isAuthenticated, login, logout } = useAuth();
  
  return (
    <div>
      <div data-testid="auth-status">
        {isAuthenticated ? 'authenticated' : 'not-authenticated'}
      </div>
      <button data-testid="login-btn" onClick={() => login('test-token')}>
        Login
      </button>
      <button data-testid="logout-btn" onClick={() => logout()}>
        Logout
      </button>
    </div>
  );
};

// 包装组件，提供Router和AuthProvider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MemoryRouter>
    <AuthProvider>
      {children}
    </AuthProvider>
  </MemoryRouter>
);

describe('认证Context测试套件', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    localStorage.clear();
    // 重置IS_ELECTRON为false（Web模式）
    vi.doMock('../../constants', () => ({
      IS_ELECTRON: false,
    }));
  });

  afterEach(() => {
    localStorage.clear();
    vi.restoreAllMocks();
  });

  describe('Web模式测试', () => {
    it('应该在没有token时初始化为未认证状态', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
    });

    it('应该在有token时初始化为认证状态', () => {
      localStorage.setItem('access_token', 'existing-token');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
    });

    it('应该成功执行登录操作', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始状态应该是未认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');

      // 执行登录
      act(() => {
        screen.getByTestId('login-btn').click();
      });

      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });

      // 验证token已存储
      expect(localStorage.getItem('access_token')).toBe('test-token');
    });

    it('应该成功执行登出操作', async () => {
      localStorage.setItem('access_token', 'existing-token');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始状态应该是认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');

      // 执行登出
      act(() => {
        screen.getByTestId('logout-btn').click();
      });

      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
      });

      // 验证token已清除
      expect(localStorage.getItem('access_token')).toBeNull();
      // 验证导航到首页
      expect(mockNavigate).toHaveBeenCalledWith('/');
    });

    it('应该监听localStorage变化', async () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始状态应该是未认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');

      // 模拟其他标签页设置token
      act(() => {
        localStorage.setItem('access_token', 'external-token');
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'access_token',
          newValue: 'external-token',
        }));
      });

      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      });
    });

    it('应该监听tokenChanged自定义事件', async () => {
      localStorage.setItem('access_token', 'initial-token');

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始状态应该是认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');

      // 模拟外部清除token并触发自定义事件
      act(() => {
        localStorage.removeItem('access_token');
        window.dispatchEvent(new Event('tokenChanged'));
      });

      // 等待状态更新
      await waitFor(() => {
        expect(screen.getByTestId('auth-status')).toHaveTextContent('not-authenticated');
      });
    });
  });

  describe('Electron模式测试', () => {
    beforeEach(() => {
      // 设置为Electron模式
      vi.doMock('../../constants', () => ({
        IS_ELECTRON: true,
      }));
    });

    it('应该在Electron模式下自动认证', async () => {
      // 重新导入以获取更新的IS_ELECTRON值
      const { AuthProvider: ElectronAuthProvider } = await import('../../contexts/AuthContext');
      
      render(
        <MemoryRouter>
          <ElectronAuthProvider>
            <TestComponent />
          </ElectronAuthProvider>
        </MemoryRouter>
      );

      // 应该自动认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      // 应该设置本地token
      expect(localStorage.getItem('access_token')).toBe('electron-local-token');
    });

    it('应该在Electron模式下忽略手动登录', async () => {
      const { AuthProvider: ElectronAuthProvider } = await import('../../contexts/AuthContext');
      
      render(
        <MemoryRouter>
          <ElectronAuthProvider>
            <TestComponent />
          </ElectronAuthProvider>
        </MemoryRouter>
      );

      // 尝试手动登录
      act(() => {
        screen.getByTestId('login-btn').click();
      });

      // 状态应该保持认证，token应该保持不变
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(localStorage.getItem('access_token')).toBe('electron-local-token');
    });

    it('应该在Electron模式下禁止登出', async () => {
      const { AuthProvider: ElectronAuthProvider } = await import('../../contexts/AuthContext');
      
      render(
        <MemoryRouter>
          <ElectronAuthProvider>
            <TestComponent />
          </ElectronAuthProvider>
        </MemoryRouter>
      );

      // 尝试登出
      act(() => {
        screen.getByTestId('logout-btn').click();
      });

      // 状态应该保持认证
      expect(screen.getByTestId('auth-status')).toHaveTextContent('authenticated');
      expect(localStorage.getItem('access_token')).toBe('electron-local-token');
      // 不应该导航
      expect(mockNavigate).not.toHaveBeenCalled();
    });
  });

  describe('useAuth hook测试', () => {
    it('应该在AuthProvider外部使用时抛出错误', () => {
      // 使用console.error的mock来避免测试输出中的错误信息
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useAuth must be used within an AuthProvider');

      consoleSpy.mockRestore();
    });

    it('应该正确返回认证状态和方法', () => {
      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 验证组件能正常渲染，说明hook返回了正确的值
      expect(screen.getByTestId('auth-status')).toBeInTheDocument();
      expect(screen.getByTestId('login-btn')).toBeInTheDocument();
      expect(screen.getByTestId('logout-btn')).toBeInTheDocument();
    });
  });

  describe('事件清理测试', () => {
    it('应该在组件卸载时清理事件监听器', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      const { unmount } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 卸载组件
      unmount();

      // 验证事件监听器被清理
      expect(removeEventListenerSpy).toHaveBeenCalledWith('storage', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('tokenChanged', expect.any(Function));

      removeEventListenerSpy.mockRestore();
    });
  });
});
