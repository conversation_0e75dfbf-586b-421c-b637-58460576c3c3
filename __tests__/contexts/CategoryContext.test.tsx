/**
 * 分类Context测试套件
 * 测试分类状态管理功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { render, screen, waitFor } from '@testing-library/react';
import { CategoryProvider, useCategories } from '../../contexts/CategoryContext';
import * as apiService from '../../services/api';
import type { CategoryRead, ApiError } from '../../types';

// Mock API服务
vi.mock('../../services/api', () => ({
  getCategories: vi.fn(),
}));

// Mock constants
vi.mock('../../constants', () => ({
  MAX_CATEGORIES_TO_LOAD_IMAGES_FROM: 50,
}));

// 测试组件，用于测试useCategories hook
const TestComponent: React.FC = () => {
  const { categories, isLoading, error, fetchCategories } = useCategories();
  
  return (
    <div>
      <div data-testid="loading-status">
        {isLoading ? 'loading' : 'not-loading'}
      </div>
      <div data-testid="error-status">
        {error ? 'has-error' : 'no-error'}
      </div>
      <div data-testid="categories-count">
        {categories.length}
      </div>
      <div data-testid="categories-list">
        {categories.map(category => (
          <div key={category.id} data-testid={`category-${category.id}`}>
            {category.name}
          </div>
        ))}
      </div>
      <button data-testid="refetch-btn" onClick={fetchCategories}>
        Refetch
      </button>
    </div>
  );
};

// 包装组件，提供CategoryProvider
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <CategoryProvider>
    {children}
  </CategoryProvider>
);

describe('分类Context测试套件', () => {
  const mockGetCategories = vi.mocked(apiService.getCategories);

  const mockCategories: CategoryRead[] = [
    {
      id: '1',
      name: 'Category 1',
      description: 'Description 1',
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
      thumbnail_path: '/thumbnails/cat1.jpg',
      thumbnail_url: '/thumbnails/cat1.jpg',
    },
    {
      id: '2',
      name: 'Category 2',
      description: 'Description 2',
      created_at: '2025-01-02T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
      thumbnail_path: '/thumbnails/cat2.jpg',
      thumbnail_url: '/thumbnails/cat2.jpg',
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('CategoryProvider初始化测试', () => {
    it('应该正确初始化状态', () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 初始状态应该是加载中
      expect(screen.getByTestId('loading-status')).toHaveTextContent('loading');
      expect(screen.getByTestId('error-status')).toHaveTextContent('no-error');
      expect(screen.getByTestId('categories-count')).toHaveTextContent('0');
    });

    it('应该成功加载分类数据', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 等待数据加载完成
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('error-status')).toHaveTextContent('no-error');
      expect(screen.getByTestId('categories-count')).toHaveTextContent('2');
      expect(screen.getByTestId('category-1')).toHaveTextContent('Category 1');
      expect(screen.getByTestId('category-2')).toHaveTextContent('Category 2');
    });

    it('应该正确调用API并传递参数', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(mockGetCategories).toHaveBeenCalledWith(0, 50);
      });
    });
  });

  describe('错误处理测试', () => {
    it('应该正确处理API错误', async () => {
      const mockError: ApiError = {
        type: 'network',
        message: 'Network error',
        timestamp: new Date().toISOString(),
      };

      mockGetCategories.mockRejectedValue(mockError);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 等待错误状态更新
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('error-status')).toHaveTextContent('has-error');
      expect(screen.getByTestId('categories-count')).toHaveTextContent('0');
    });

    it('应该在错误时清空分类列表', async () => {
      // 先成功加载数据
      mockGetCategories.mockResolvedValueOnce(mockCategories);

      const { rerender } = render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('categories-count')).toHaveTextContent('2');
      });

      // 然后模拟错误
      const mockError: ApiError = {
        type: 'server',
        message: 'Server error',
        timestamp: new Date().toISOString(),
      };

      mockGetCategories.mockRejectedValue(mockError);

      // 触发重新获取
      screen.getByTestId('refetch-btn').click();

      await waitFor(() => {
        expect(screen.getByTestId('error-status')).toHaveTextContent('has-error');
      });

      expect(screen.getByTestId('categories-count')).toHaveTextContent('0');
    });
  });

  describe('数据重新获取测试', () => {
    it('应该支持手动重新获取数据', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 等待初始加载完成
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      expect(mockGetCategories).toHaveBeenCalledTimes(1);

      // 手动重新获取
      screen.getByTestId('refetch-btn').click();

      await waitFor(() => {
        expect(mockGetCategories).toHaveBeenCalledTimes(2);
      });
    });

    it('应该在重新获取时显示加载状态', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 等待初始加载完成
      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      // 手动重新获取
      screen.getByTestId('refetch-btn').click();

      // 应该显示加载状态
      expect(screen.getByTestId('loading-status')).toHaveTextContent('loading');

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });
    });

    it('应该在重新获取时清除之前的错误', async () => {
      // 先模拟错误
      const mockError: ApiError = {
        type: 'network',
        message: 'Network error',
        timestamp: new Date().toISOString(),
      };

      mockGetCategories.mockRejectedValueOnce(mockError);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-status')).toHaveTextContent('has-error');
      });

      // 然后成功获取数据
      mockGetCategories.mockResolvedValue(mockCategories);

      screen.getByTestId('refetch-btn').click();

      await waitFor(() => {
        expect(screen.getByTestId('error-status')).toHaveTextContent('no-error');
      });

      expect(screen.getByTestId('categories-count')).toHaveTextContent('2');
    });
  });

  describe('数据转换测试', () => {
    it('应该正确转换CategoryRead为CategoryReadWithImages', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      // 验证数据结构正确转换
      expect(screen.getByTestId('categories-count')).toHaveTextContent('2');
      expect(screen.getByTestId('category-1')).toHaveTextContent('Category 1');
      expect(screen.getByTestId('category-2')).toHaveTextContent('Category 2');
    });

    it('应该处理空的分类列表', async () => {
      mockGetCategories.mockResolvedValue([]);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('error-status')).toHaveTextContent('no-error');
      expect(screen.getByTestId('categories-count')).toHaveTextContent('0');
    });
  });

  describe('useCategories hook测试', () => {
    it('应该在CategoryProvider外部使用时抛出错误', () => {
      // 使用console.error的mock来避免测试输出中的错误信息
      const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {});

      expect(() => {
        render(<TestComponent />);
      }).toThrow('useCategories must be used within a CategoryProvider');

      consoleSpy.mockRestore();
    });

    it('应该正确返回分类状态和方法', async () => {
      mockGetCategories.mockResolvedValue(mockCategories);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 验证组件能正常渲染，说明hook返回了正确的值
      expect(screen.getByTestId('loading-status')).toBeInTheDocument();
      expect(screen.getByTestId('error-status')).toBeInTheDocument();
      expect(screen.getByTestId('categories-count')).toBeInTheDocument();
      expect(screen.getByTestId('categories-list')).toBeInTheDocument();
      expect(screen.getByTestId('refetch-btn')).toBeInTheDocument();

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });
    });
  });

  describe('并发请求处理测试', () => {
    it('应该正确处理快速连续的重新获取请求', async () => {
      let resolvePromise: (value: CategoryRead[]) => void;
      const promise = new Promise<CategoryRead[]>((resolve) => {
        resolvePromise = resolve;
      });

      mockGetCategories.mockReturnValue(promise);

      render(
        <TestWrapper>
          <TestComponent />
        </TestWrapper>
      );

      // 快速点击多次重新获取按钮
      screen.getByTestId('refetch-btn').click();
      screen.getByTestId('refetch-btn').click();
      screen.getByTestId('refetch-btn').click();

      // 解析Promise
      resolvePromise!(mockCategories);

      await waitFor(() => {
        expect(screen.getByTestId('loading-status')).toHaveTextContent('not-loading');
      });

      expect(screen.getByTestId('categories-count')).toHaveTextContent('2');
    });
  });
});
