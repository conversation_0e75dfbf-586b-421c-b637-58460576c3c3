/**
 * API服务的Mock配置
 * 提供统一的API响应模拟和错误模拟
 */

import { vi } from 'vitest';
import type {
  CategoryRead,
  CategoryCreate,
  CategoryUpdate,
  CategoryListResponse,
  ImageRead,
  ImageUpdate,
  BodyUploadImage,
  TagRead,
  TagCreate,
  TagUpdate,
  SpeciesRead,
  SpeciesSuggestionsResponse,
  TokenResponse,
} from '../../types';

// Mock数据
export const mockTokenResponse: TokenResponse = {
  access_token: 'mock-access-token',
  token_type: 'bearer',
  expires_in: 3600,
  refresh_token: 'mock-refresh-token',
};

export const mockCategory: CategoryRead = {
  id: '1',
  name: 'Test Category',
  description: 'Test Description',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  thumbnail_path: '/thumbnails/category-1.jpg',
  thumbnail_url: '/thumbnails/category-1.jpg',
};

export const mockCategoryListResponse: CategoryListResponse = [mockCategory];

export const mockImage: ImageRead = {
  id: '1',
  category_id: '1',
  title: 'Test Image',
  original_filename: 'test-image.jpg',
  stored_filename: 'stored-test-image.jpg',
  relative_file_path: '/images/test-image.jpg',
  relative_thumbnail_path: '/thumbnails/test-image.jpg',
  mime_type: 'image/jpeg',
  size_bytes: 1024000,
  description: 'A test image',
  tags: [],
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
  file_metadata: {},
  exif_info: null,
  image_url: '/images/test-image.jpg',
  thumbnail_url: '/thumbnails/test-image.jpg',
  set_as_category_thumbnail: false,
};

export const mockTag: TagRead = {
  id: '1',
  name: 'Test Tag',
  created_at: '2025-01-01T00:00:00Z',
  updated_at: '2025-01-01T00:00:00Z',
};

export const mockSpecies: SpeciesRead = {
  id: 1,
  order_details: 'Test Order',
  family_details: 'Test Family',
  genus_details: 'Test Genus',
  name_chinese: '测试物种',
  name_english: 'Test Species',
  name_latin: 'Testus speciesus',
  href: 'http://example.com/species/1',
  pinyin_full: 'ce shi wu zhong',
  pinyin_initials: 'cswz',
};

export const mockSpeciesSuggestions: SpeciesSuggestionsResponse = ['测试物种', '另一个物种'];

// Axios Mock配置
export const createAxiosMock = () => {
  const axiosMock = {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
    request: vi.fn(),
    interceptors: {
      request: {
        use: vi.fn(),
        eject: vi.fn(),
      },
      response: {
        use: vi.fn(),
        eject: vi.fn(),
      },
    },
  };

  // 默认成功响应
  axiosMock.get.mockResolvedValue({ data: mockCategoryListResponse });
  axiosMock.post.mockResolvedValue({ data: mockCategory });
  axiosMock.put.mockResolvedValue({ data: mockCategory });
  axiosMock.patch.mockResolvedValue({ data: mockCategory });
  axiosMock.delete.mockResolvedValue({ data: { success: true } });

  return axiosMock;
};

// Electron API Mock配置
export const createElectronAPIMock = () => {
  return {
    auth: {
      login: vi.fn().mockResolvedValue({ success: true, data: mockTokenResponse }),
      logout: vi.fn().mockResolvedValue({ success: true }),
      getCurrentUser: vi.fn().mockResolvedValue({ success: true, data: { id: '1', username: 'testuser' } }),
    },
    categories: {
      getCategories: vi.fn().mockResolvedValue({ success: true, data: mockCategoryListResponse }),
      createCategory: vi.fn().mockResolvedValue({ success: true, data: mockCategory }),
      updateCategory: vi.fn().mockResolvedValue({ success: true, data: mockCategory }),
      deleteCategory: vi.fn().mockResolvedValue({ success: true }),
    },
    images: {
      getImages: vi.fn().mockResolvedValue({ success: true, data: { images: [mockImage], total: 1 } }),
      uploadImage: vi.fn().mockResolvedValue({ success: true, data: mockImage }),
      updateImage: vi.fn().mockResolvedValue({ success: true, data: mockImage }),
      deleteImage: vi.fn().mockResolvedValue({ success: true }),
      deleteImages: vi.fn().mockResolvedValue({ success: true, results: [] }),
    },
    tags: {
      getTags: vi.fn().mockResolvedValue({ success: true, data: { tags: [mockTag], total: 1 } }),
      createTag: vi.fn().mockResolvedValue({ success: true, data: mockTag }),
      updateTag: vi.fn().mockResolvedValue({ success: true, data: mockTag }),
      deleteTag: vi.fn().mockResolvedValue({ success: true }),
    },
    species: {
      getSpecies: vi.fn().mockResolvedValue({ success: true, data: { species: [mockSpecies], total: 1 } }),
      getSpeciesSuggestions: vi.fn().mockResolvedValue({ success: true, data: mockSpeciesSuggestions }),
    },
  };
};

// 错误响应Mock
export const createErrorMocks = () => {
  const networkError = new Error('Network Error');
  const validationError = {
    response: {
      status: 422,
      data: {
        detail: [
          {
            loc: ['body', 'name'],
            msg: 'field required',
            type: 'value_error.missing',
          },
        ],
      },
    },
  };
  const serverError = {
    response: {
      status: 500,
      data: {
        detail: 'Internal Server Error',
      },
    },
  };

  return {
    networkError,
    validationError,
    serverError,
  };
};

// 全局Mock设置函数
export const setupAPIMocks = () => {
  const axiosMock = createAxiosMock();
  const electronAPIMock = createElectronAPIMock();
  
  // Mock axios
  vi.doMock('axios', () => ({
    default: axiosMock,
    ...axiosMock,
  }));

  // Mock window.electronAPI
  Object.defineProperty(window, 'electronAPI', {
    value: electronAPIMock,
    writable: true,
  });

  return {
    axiosMock,
    electronAPIMock,
  };
};

// 清理Mock函数
export const cleanupAPIMocks = () => {
  vi.clearAllMocks();
  delete (window as any).electronAPI;
};
