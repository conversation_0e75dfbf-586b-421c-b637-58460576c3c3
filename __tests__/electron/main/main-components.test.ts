import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock electron模块
const mockDialog = {
  showMessageBox: vi.fn(() => Promise.resolve({ response: 0 })),
  showOpenDialog: vi.fn(() => Promise.resolve({ canceled: false, filePaths: ['/test/path'] })),
  showSaveDialog: vi.fn(() => Promise.resolve({ canceled: false, filePath: '/test/save/path' })),
  showErrorBox: vi.fn()
};

const mockApp = {
  getPath: vi.fn(() => '/tmp/test-userData'),
  quit: vi.fn(),
  exit: vi.fn(),
  isReady: vi.fn(() => true),
  whenReady: vi.fn(() => Promise.resolve()),
  on: vi.fn(),
  once: vi.fn(),
  emit: vi.fn()
};

const mockBrowserWindow = vi.fn(() => ({
  loadFile: vi.fn(() => Promise.resolve()),
  loadURL: vi.fn(() => Promise.resolve()),
  setTitle: vi.fn(),
  setMenuBarVisibility: vi.fn(),
  webContents: {
    on: vi.fn(),
    send: vi.fn(),
    openDevTools: vi.fn()
  },
  on: vi.fn(),
  once: vi.fn(),
  show: vi.fn(),
  focus: vi.fn(),
  destroy: vi.fn(),
  isDestroyed: vi.fn(() => false)
}));

const mockMenu = {
  setApplicationMenu: vi.fn(),
  buildFromTemplate: vi.fn(() => ({}))
};

vi.mock('electron', () => ({
  app: mockApp,
  BrowserWindow: mockBrowserWindow,
  ipcMain: {
    handle: vi.fn(),
    on: vi.fn(),
    once: vi.fn(),
    removeAllListeners: vi.fn()
  },
  protocol: {
    registerFileProtocol: vi.fn(),
    interceptFileProtocol: vi.fn()
  },
  Menu: mockMenu,
  shell: {
    openExternal: vi.fn(() => Promise.resolve()),
    showItemInFolder: vi.fn()
  },
  dialog: mockDialog
}));

// Mock服务类
const mockSettingsService = {
  getSettings: vi.fn(() => ({
    storagePath: '/test/storage',
    usesCategoryFolders: true,
    isFirstTimeSetup: false,
    lastMigrationVersion: '1.0.0',
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z'
  })),
  saveSettings: vi.fn(() => true),
  isFirstTimeSetup: vi.fn(() => false),
  markFirstTimeSetupComplete: vi.fn(() => true),
  getStoragePath: vi.fn(() => '/test/storage'),
  updateStoragePath: vi.fn(() => true),
  ensureConfigDirectory: vi.fn()
};

vi.mock('../../../electron/services/SettingsService', () => ({
  SettingsService: vi.fn(() => mockSettingsService)
}));

describe('Main Process Components', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // 重置默认返回值
    mockDialog.showMessageBox.mockResolvedValue({ response: 0 });
    mockDialog.showOpenDialog.mockResolvedValue({ canceled: false, filePaths: ['/test/path'] });
    console.log = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
  });

  describe('首次启动配置对话框', () => {
    it('应该正确处理用户选择默认位置', async () => {
      // 模拟用户选择默认位置的响应
      mockDialog.showMessageBox
        .mockResolvedValueOnce({ response: 1 }) // 选择默认位置
        .mockResolvedValueOnce({ response: 0 }) // 选择分类文件夹
        .mockResolvedValueOnce({ response: 0 }); // 确认设置完成

      // 模拟首次启动
      mockSettingsService.isFirstTimeSetup.mockReturnValue(true);

      // 创建一个模拟的showFirstTimeSetupDialog函数
      const showFirstTimeSetupDialog = async () => {
        try {
          console.log('🚀 首次启动检测，显示存储位置选择对话框');
          
          const welcomeResult = await mockDialog.showMessageBox(null, {
            type: 'info',
            title: 'Pokedex - 欢迎',
            message: '欢迎使用 Pokedex 图片管理应用！',
            detail: '请选择您希望存储图片的位置。您可以选择默认位置或自定义路径。',
            buttons: ['选择自定义位置', '使用默认位置'],
            defaultId: 1,
            cancelId: 1
          });

          let storagePath: string;
          let usesCategoryFolders = true;

          if (welcomeResult.response === 0) {
            const directoryResult = await mockDialog.showOpenDialog(null, {
              title: '选择图片存储位置',
              message: '请选择一个文件夹用于存储您的图片',
              properties: ['openDirectory', 'createDirectory'],
              buttonLabel: '选择此文件夹'
            });

            if (directoryResult.canceled || !directoryResult.filePaths.length) {
              storagePath = mockSettingsService.getSettings().storagePath;
            } else {
              storagePath = directoryResult.filePaths[0];
            }
          } else {
            storagePath = mockSettingsService.getSettings().storagePath;
          }

          const folderStructureResult = await mockDialog.showMessageBox(null, {
            type: 'question',
            title: '文件夹结构设置',
            message: '是否为每个分类创建独立的文件夹？',
            detail: '推荐开启：每个分类都会有自己的文件夹，便于管理。\n关闭：所有图片存储在统一的文件夹中。',
            buttons: ['为每个分类创建文件夹 (推荐)', '使用统一文件夹'],
            defaultId: 0,
            cancelId: 0
          });

          usesCategoryFolders = folderStructureResult.response === 0;

          const saved = mockSettingsService.saveSettings({
            storagePath,
            usesCategoryFolders,
            isFirstTimeSetup: true
          });

          if (saved) {
            await mockDialog.showMessageBox(null, {
              type: 'info',
              title: '设置完成',
              message: '存储配置已完成！',
              detail: `存储位置: ${storagePath}\n文件夹结构: ${usesCategoryFolders ? '按分类分文件夹' : '统一文件夹'}\n\n您可以随时通过应用菜单更改这些设置。`,
              buttons: ['开始使用']
            });

            return true;
          } else {
            throw new Error('保存设置失败');
          }
        } catch (error) {
          console.error('❌ 首次设置失败:', error);
          
          await mockDialog.showErrorBox(
            '设置错误', 
            `首次设置失败: ${error instanceof Error ? error.message : String(error)}\n\n将使用默认设置继续运行。`
          );
          
          return false;
        }
      };

      const result = await showFirstTimeSetupDialog();

      expect(result).toBe(true);
      expect(mockDialog.showMessageBox).toHaveBeenCalledTimes(3);
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        storagePath: '/test/storage',
        usesCategoryFolders: true,
        isFirstTimeSetup: true
      });
    });

    it('应该正确处理用户选择自定义位置', async () => {
      // 模拟用户选择自定义位置的响应
      mockDialog.showMessageBox
        .mockResolvedValueOnce({ response: 0 }) // 选择自定义位置
        .mockResolvedValueOnce({ response: 1 }) // 选择统一文件夹
        .mockResolvedValueOnce({ response: 0 }); // 确认设置完成

      mockDialog.showOpenDialog.mockResolvedValueOnce({
        canceled: false,
        filePaths: ['/custom/storage/path']
      });

      const showFirstTimeSetupDialog = async () => {
        const welcomeResult = await mockDialog.showMessageBox(null, {
          type: 'info',
          title: 'Pokedex - 欢迎',
          buttons: ['选择自定义位置', '使用默认位置']
        });

        let storagePath: string;
        if (welcomeResult.response === 0) {
          const directoryResult = await mockDialog.showOpenDialog(null, {
            title: '选择图片存储位置',
            properties: ['openDirectory', 'createDirectory']
          });
          storagePath = directoryResult.filePaths[0];
        } else {
          storagePath = mockSettingsService.getSettings().storagePath;
        }

        const folderStructureResult = await mockDialog.showMessageBox(null, {
          type: 'question',
          title: '文件夹结构设置',
          buttons: ['为每个分类创建文件夹 (推荐)', '使用统一文件夹']
        });

        const usesCategoryFolders = folderStructureResult.response === 0;

        return mockSettingsService.saveSettings({
          storagePath,
          usesCategoryFolders,
          isFirstTimeSetup: true
        });
      };

      const result = await showFirstTimeSetupDialog();

      expect(result).toBe(true);
      expect(mockDialog.showOpenDialog).toHaveBeenCalled();
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        storagePath: '/custom/storage/path',
        usesCategoryFolders: false,
        isFirstTimeSetup: true
      });
    });

    it('应该处理用户取消目录选择的情况', async () => {
      mockDialog.showMessageBox.mockResolvedValueOnce({ response: 0 }); // 选择自定义位置
      mockDialog.showOpenDialog.mockResolvedValueOnce({
        canceled: true,
        filePaths: []
      });

      const showFirstTimeSetupDialog = async () => {
        const welcomeResult = await mockDialog.showMessageBox(null, {
          type: 'info',
          buttons: ['选择自定义位置', '使用默认位置']
        });

        let storagePath: string;
        if (welcomeResult.response === 0) {
          const directoryResult = await mockDialog.showOpenDialog(null, {
            title: '选择图片存储位置',
            properties: ['openDirectory', 'createDirectory']
          });

          if (directoryResult.canceled || !directoryResult.filePaths.length) {
            storagePath = mockSettingsService.getSettings().storagePath;
          } else {
            storagePath = directoryResult.filePaths[0];
          }
        } else {
          storagePath = mockSettingsService.getSettings().storagePath;
        }

        return storagePath;
      };

      const result = await showFirstTimeSetupDialog();

      expect(result).toBe('/test/storage'); // 应该回退到默认路径
      expect(mockDialog.showOpenDialog).toHaveBeenCalled();
    });

    it('应该处理设置保存失败的情况', async () => {
      mockDialog.showMessageBox
        .mockResolvedValueOnce({ response: 1 }) // 使用默认位置
        .mockResolvedValueOnce({ response: 0 }); // 选择分类文件夹

      mockSettingsService.saveSettings.mockReturnValue(false); // 模拟保存失败

      const showFirstTimeSetupDialog = async () => {
        try {
          const welcomeResult = await mockDialog.showMessageBox(null, {
            buttons: ['选择自定义位置', '使用默认位置']
          });

          const storagePath = mockSettingsService.getSettings().storagePath;

          const folderStructureResult = await mockDialog.showMessageBox(null, {
            buttons: ['为每个分类创建文件夹 (推荐)', '使用统一文件夹']
          });

          const usesCategoryFolders = folderStructureResult.response === 0;

          const saved = mockSettingsService.saveSettings({
            storagePath,
            usesCategoryFolders,
            isFirstTimeSetup: true
          });

          if (!saved) {
            throw new Error('保存设置失败');
          }

          return true;
        } catch (error) {
          await mockDialog.showErrorBox('设置错误', `首次设置失败: ${error}`);
          return false;
        }
      };

      const result = await showFirstTimeSetupDialog();

      expect(result).toBe(false);
      expect(mockDialog.showErrorBox).toHaveBeenCalled();
    });
  });

  describe('窗口创建功能', () => {
    it('应该创建具有正确配置的BrowserWindow', () => {
      const createWindow = () => {
        const mainWindow = new mockBrowserWindow({
          width: 1200,
          height: 800,
          webPreferences: {
            nodeIntegration: false,
            contextIsolation: true,
            preload: '/path/to/preload.js'
          }
        });

        return mainWindow;
      };

      const window = createWindow();

      expect(mockBrowserWindow).toHaveBeenCalledWith({
        width: 1200,
        height: 800,
        webPreferences: {
          nodeIntegration: false,
          contextIsolation: true,
          preload: '/path/to/preload.js'
        }
      });

      expect(window).toBeDefined();
    });
  });

  describe('菜单创建功能', () => {
    it('应该创建正确的应用菜单结构', () => {
      const createMenu = () => {
        const template = [
          {
            label: 'File',
            submenu: [
              {
                label: 'Open',
                accelerator: 'CmdOrCtrl+O',
                click: () => console.log('Open clicked')
              },
              {
                label: 'Exit',
                accelerator: 'CmdOrCtrl+Q',
                click: () => mockApp.quit()
              }
            ]
          },
          {
            label: 'Edit',
            submenu: [
              { role: 'undo' },
              { role: 'redo' },
              { type: 'separator' },
              { role: 'cut' },
              { role: 'copy' },
              { role: 'paste' }
            ]
          }
        ];

        const menu = mockMenu.buildFromTemplate(template);
        mockMenu.setApplicationMenu(menu);

        return menu;
      };

      const menu = createMenu();

      expect(mockMenu.buildFromTemplate).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            label: 'File',
            submenu: expect.any(Array)
          }),
          expect.objectContaining({
            label: 'Edit',
            submenu: expect.any(Array)
          })
        ])
      );

      expect(mockMenu.setApplicationMenu).toHaveBeenCalledWith(menu);
    });
  });

  describe('存储位置更改功能', () => {
    it('应该正确处理存储位置更改流程', async () => {
      mockDialog.showMessageBox
        .mockResolvedValueOnce({ response: 0 }) // 确认更改
        .mockResolvedValueOnce({ response: 0 }); // 确认操作完成

      mockDialog.showOpenDialog.mockResolvedValueOnce({
        canceled: false,
        filePaths: ['/new/storage/path']
      });

      mockSettingsService.updateStoragePath.mockReturnValue(true);

      const changeStorageLocation = async () => {
        try {
          const currentSettings = mockSettingsService.getSettings();
          const currentPath = currentSettings.storagePath;
          
          const confirmResult = await mockDialog.showMessageBox(null, {
            type: 'question',
            title: '更改存储位置',
            message: '确定要更改图片存储位置吗？',
            detail: `当前存储位置: ${currentPath}\n\n更改后会将现有图片移动到新位置。`,
            buttons: ['选择新位置', '取消'],
            defaultId: 0,
            cancelId: 1
          });

          if (confirmResult.response === 1) {
            return false; // 用户取消
          }

          const directoryResult = await mockDialog.showOpenDialog(null, {
            title: '选择新的图片存储位置',
            properties: ['openDirectory', 'createDirectory']
          });

          if (directoryResult.canceled || !directoryResult.filePaths.length) {
            return false;
          }

          const newStoragePath = directoryResult.filePaths[0];
          
          if (newStoragePath === currentPath) {
            await mockDialog.showMessageBox(null, {
              type: 'info',
              title: '相同路径',
              message: '您选择的路径与当前存储位置相同。',
              buttons: ['确定']
            });
            return false;
          }

          const success = mockSettingsService.updateStoragePath(newStoragePath);
          
          if (success) {
            await mockDialog.showMessageBox(null, {
              type: 'info',
              title: '更改成功',
              message: '存储位置已成功更改！',
              buttons: ['确定']
            });
          }

          return success;
        } catch (error) {
          console.error('❌ 更改存储位置失败:', error);
          return false;
        }
      };

      const result = await changeStorageLocation();

      expect(result).toBe(true);
      expect(mockDialog.showOpenDialog).toHaveBeenCalled();
      expect(mockSettingsService.updateStoragePath).toHaveBeenCalledWith('/new/storage/path');
    });

    it('应该处理用户取消更改的情况', async () => {
      // 清除之前的mock设置
      mockDialog.showMessageBox.mockReset();
      mockDialog.showMessageBox.mockResolvedValueOnce({ response: 1 }); // 用户取消

      const changeStorageLocation = async () => {
        const confirmResult = await mockDialog.showMessageBox(null, {
          type: 'question',
          title: '更改存储位置',
          buttons: ['选择新位置', '取消'],
          cancelId: 1
        });

        if (confirmResult.response === 1) {
          return false; // 用户取消
        }
        return true;
      };

      const result = await changeStorageLocation();

      expect(result).toBe(false);
      expect(mockDialog.showMessageBox).toHaveBeenCalledWith(null, expect.objectContaining({
        type: 'question',
        title: '更改存储位置'
      }));
    });
  });

  describe('错误处理', () => {
    it('应该正确处理对话框错误', async () => {
      const error = new Error('Dialog error');
      
      // 清除之前的mock设置并设置错误
      mockDialog.showMessageBox.mockReset();
      mockDialog.showMessageBox.mockRejectedValueOnce(error);

      let caughtError = null;
      const safeShowDialog = async () => {
        try {
          await mockDialog.showMessageBox(null, {
            type: 'info',
            title: 'Test',
            message: 'Test message'
          });
          return true;
        } catch (err) {
          caughtError = err;
          console.error('Dialog error:', err);
          return false;
        }
      };

      const result = await safeShowDialog();

      expect(result).toBe(false);
      expect(caughtError).toBe(error);
      expect(console.error).toHaveBeenCalledWith('Dialog error:', error);
    });

    it('应该验证mock错误被正确抛出', async () => {
      const error = new Error('Test error');
      mockDialog.showMessageBox.mockReset();
      mockDialog.showMessageBox.mockRejectedValueOnce(error);

      await expect(mockDialog.showMessageBox(null, { type: 'info' }))
        .rejects.toThrow('Test error');
    });
  });
});