import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ipcMain } from 'electron';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  },
  ipcMain: {
    handle: vi.fn(),
    on: vi.fn(),
    removeAllListeners: vi.fn()
  },
  BrowserWindow: vi.fn(),
  Menu: {
    setApplicationMenu: vi.fn(),
    buildFromTemplate: vi.fn()
  },
  protocol: {
    registerFileProtocol: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  mkdirSync: vi.fn(),
  unlinkSync: vi.fn(),
  accessSync: vi.fn(),
  constants: {
    W_OK: 2
  }
}));

// Mock path模块
vi.mock('path', async (importOriginal) => {
  const originalPath = await importOriginal<typeof path>();
  return {
    ...originalPath,
    join: vi.fn((...args) => args.join('/')),
    dirname: vi.fn((filePath) => filePath.split('/').slice(0, -1).join('/')),
    basename: vi.fn((filePath) => filePath.split('/').pop() || ''),
    extname: vi.fn((filePath) => {
      const parts = filePath.split('.');
      return parts.length > 1 ? '.' + parts.pop() : '';
    })
  };
});

// Mock better-sqlite3
vi.mock('better-sqlite3', () => ({
  default: vi.fn().mockImplementation(() => ({
    prepare: vi.fn(() => ({
      get: vi.fn(),
      all: vi.fn(),
      run: vi.fn()
    })),
    close: vi.fn(),
    exec: vi.fn()
  }))
}));

// Mock 服务类
vi.mock('../../../electron/services/OSSService', () => ({
  OSSService: vi.fn().mockImplementation(() => ({
    isConfigured: vi.fn(() => true),
    uploadFile: vi.fn(),
    downloadFile: vi.fn(),
    listFiles: vi.fn(),
    fileExists: vi.fn(),
    updateConfig: vi.fn()
  }))
}));

vi.mock('../../../electron/services/SettingsService', () => ({
  SettingsService: vi.fn().mockImplementation(() => ({
    getSettings: vi.fn(() => ({})),
    saveSettings: vi.fn(() => true),
    getOSSConfig: vi.fn(() => ({
      endpoint: 'https://test.com',
      region: 'test-region',
      accessKeyId: 'test-key',
      secretAccessKey: 'test-secret',
      bucket: 'test-bucket'
    }))
  }))
}));

vi.mock('../../../electron/services/DatabaseSyncService', () => ({
  DatabaseSyncService: vi.fn().mockImplementation(() => ({
    backupDatabaseToOSS: vi.fn(),
    listDatabaseBackups: vi.fn(),
    restoreDatabaseFromOSS: vi.fn(),
    validateDatabaseBackup: vi.fn(),
    getSyncStatus: vi.fn(),
    canSync: vi.fn(() => true)
  }))
}));

vi.mock('../../../electron/database', () => ({
  DatabaseManager: vi.fn().mockImplementation(() => ({
    getDatabasePath: vi.fn(() => '/tmp/test-db.db'),
    restoreFromBackup: vi.fn(),
    createDatabaseBackup: vi.fn(),
    closeDatabaseSafely: vi.fn()
  }))
}));

describe('数据库同步IPC处理器测试', () => {
  let mockEvent: any;
  let mockDatabaseSyncService: any;
  let mockSettingsService: any;
  let mockOSSService: any;
  let ipcHandlers: Map<string, Function>;

  beforeEach(() => {
    // 重置所有mocks
    vi.clearAllMocks();

    // 创建模拟事件对象
    mockEvent = {
      sender: {
        send: vi.fn()
      }
    };

    // 存储IPC处理器
    ipcHandlers = new Map();
    vi.mocked(ipcMain.handle).mockImplementation((channel: string, handler: Function) => {
      ipcHandlers.set(channel, handler);
    });

    // 创建模拟服务实例
    mockDatabaseSyncService = {
      backupDatabaseToOSS: vi.fn(),
      listDatabaseBackups: vi.fn(),
      restoreDatabaseFromOSS: vi.fn(),
      validateDatabaseBackup: vi.fn(),
      getSyncStatus: vi.fn(),
      canSync: vi.fn(() => true)
    };

    mockSettingsService = {
      getSettings: vi.fn(() => ({
        enableDatabaseSync: true,
        lastBackupTime: null,
        lastRestoreTime: null
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn(() => ({
        endpoint: 'https://test.com',
        region: 'test-region',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket'
      }))
    };

    mockOSSService = {
      isConfigured: vi.fn(() => true),
      uploadFile: vi.fn(),
      downloadFile: vi.fn(),
      listFiles: vi.fn(),
      fileExists: vi.fn(),
      updateConfig: vi.fn()
    };

    // 模拟main.ts中的全局变量
    global.settingsService = mockSettingsService;
    global.ossService = mockOSSService;
    global.databaseSyncService = mockDatabaseSyncService;
  });

  afterEach(() => {
    vi.restoreAllMocks();
    delete global.settingsService;
    delete global.ossService;
    delete global.databaseSyncService;
  });

  describe('backup-database-to-oss IPC处理器', () => {
    it('应该成功处理备份请求', async () => {
      // 准备测试数据
      const customName = 'custom-backup.db';
      const expectedResult = {
        success: true,
        message: '数据库备份成功',
        backupInfo: {
          name: 'backup-2025-07-17-14-30-45.db',
          size: 1024,
          lastModified: new Date().toISOString()
        }
      };

      // 设置mock返回值
      mockDatabaseSyncService.backupDatabaseToOSS.mockResolvedValue(expectedResult);

      // 创建IPC处理器
      const handler = async (event: any, customName?: string) => {
        try {
          console.log('📤 开始备份数据库到OSS:', customName || '自动命名');
          
          // 确保OSS服务已配置
          const ossConfig = mockSettingsService.getOSSConfig();
          if (!ossConfig) {
            return {
              success: false,
              message: '未配置OSS存储，无法执行备份操作'
            };
          }
          
          // 更新OSS服务配置
          mockOSSService.updateConfig(ossConfig);
          
          // 定义进度回调函数
          const progressCallback = (progress: number, message: string) => {
            event.sender.send('backup-progress', { progress, message });
          };
          
          const result = await mockDatabaseSyncService.backupDatabaseToOSS(customName, progressCallback);
          
          if (result.success) {
            console.log('✅ 数据库备份成功:', result.backupInfo?.name);
          } else {
            console.error('❌ 数据库备份失败:', result.message);
          }
          return result;
        } catch (error) {
          console.error('❌ 备份数据库到OSS失败:', error);
          return {
            success: false,
            message: `备份失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent, customName);

      // 验证结果
      expect(result).toEqual(expectedResult);
      expect(mockSettingsService.getOSSConfig).toHaveBeenCalled();
      expect(mockOSSService.updateConfig).toHaveBeenCalled();
      expect(mockDatabaseSyncService.backupDatabaseToOSS).toHaveBeenCalledWith(
        customName,
        expect.any(Function)
      );

      // 验证进度回调的调用
      const progressCallback = mockDatabaseSyncService.backupDatabaseToOSS.mock.calls[0][1];
      progressCallback(50, '正在上传...');
      expect(mockEvent.sender.send).toHaveBeenCalledWith('backup-progress', {
        progress: 50,
        message: '正在上传...'
      });
    });

    it('应该处理OSS未配置的情况', async () => {
      // 设置OSS未配置
      mockSettingsService.getOSSConfig.mockReturnValue(null);

      const handler = async (event: any, customName?: string) => {
        const ossConfig = mockSettingsService.getOSSConfig();
        if (!ossConfig) {
          return {
            success: false,
            message: '未配置OSS存储，无法执行备份操作'
          };
        }
        return { success: true };
      };

      // 执行测试
      const result = await handler(mockEvent, 'test-backup.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS存储，无法执行备份操作');
      expect(mockDatabaseSyncService.backupDatabaseToOSS).not.toHaveBeenCalled();
    });

    it('应该处理备份服务异常', async () => {
      // 设置备份服务抛出异常
      mockDatabaseSyncService.backupDatabaseToOSS.mockRejectedValue(new Error('Service error'));

      const handler = async (event: any, customName?: string) => {
        try {
          const ossConfig = mockSettingsService.getOSSConfig();
          mockOSSService.updateConfig(ossConfig);
          
          const progressCallback = (progress: number, message: string) => {
            event.sender.send('backup-progress', { progress, message });
          };
          
          return await mockDatabaseSyncService.backupDatabaseToOSS(customName, progressCallback);
        } catch (error) {
          return {
            success: false,
            message: `备份失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent);

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('备份失败: Service error');
    });
  });

  describe('list-database-backups IPC处理器', () => {
    it('应该成功列出数据库备份', async () => {
      // 准备测试数据
      const expectedBackups = [
        {
          name: 'backup-2025-07-17-14-30-45.db',
          size: 1024,
          lastModified: '2025-07-17T14:30:45.000Z'
        },
        {
          name: 'backup-2025-07-16-10-15-20.db',
          size: 2048,
          lastModified: '2025-07-16T10:15:20.000Z'
        }
      ];

      const expectedResult = {
        success: true,
        backups: expectedBackups,
        message: '找到 2 个数据库备份'
      };

      // 设置mock返回值
      mockDatabaseSyncService.listDatabaseBackups.mockResolvedValue(expectedResult);

      const handler = async () => {
        try {
          console.log('📋 获取数据库备份列表');
          
          // 确保OSS服务已配置
          const ossConfig = mockSettingsService.getOSSConfig();
          if (!ossConfig) {
            return {
              success: false,
              message: '未配置OSS存储，无法获取备份列表'
            };
          }
          
          // 更新OSS服务配置
          mockOSSService.updateConfig(ossConfig);
          
          const result = await mockDatabaseSyncService.listDatabaseBackups();
          
          if (result.success) {
            console.log('✅ 获取备份列表成功，共', result.backups?.length || 0, '个备份');
          } else {
            console.error('❌ 获取备份列表失败:', result.message);
          }
          
          return result;
        } catch (error) {
          console.error('❌ 获取备份列表失败:', error);
          return {
            success: false,
            message: `获取备份列表失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler();

      // 验证结果
      expect(result).toEqual(expectedResult);
      expect(mockSettingsService.getOSSConfig).toHaveBeenCalled();
      expect(mockOSSService.updateConfig).toHaveBeenCalled();
      expect(mockDatabaseSyncService.listDatabaseBackups).toHaveBeenCalled();
    });

    it('应该处理OSS未配置的情况', async () => {
      // 设置OSS未配置
      mockSettingsService.getOSSConfig.mockReturnValue(null);

      const handler = async () => {
        const ossConfig = mockSettingsService.getOSSConfig();
        if (!ossConfig) {
          return {
            success: false,
            message: '未配置OSS存储，无法获取备份列表'
          };
        }
        return { success: true };
      };

      // 执行测试
      const result = await handler();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS存储，无法获取备份列表');
    });
  });

  describe('restore-database-from-oss IPC处理器', () => {
    it('应该成功恢复数据库', async () => {
      // 准备测试数据
      const backupName = 'backup-2025-07-17-14-30-45.db';
      const expectedResult = {
        success: true,
        message: '数据库恢复成功'
      };

      // 设置mock返回值
      mockDatabaseSyncService.restoreDatabaseFromOSS.mockResolvedValue(expectedResult);

      const handler = async (event: any, backupName: string) => {
        try {
          console.log('📥 开始从OSS恢复数据库:', backupName);
          
          if (!backupName) {
            return {
              success: false,
              message: '备份文件名不能为空'
            };
          }

          // 确保OSS服务已配置
          const ossConfig = mockSettingsService.getOSSConfig();
          if (!ossConfig) {
            return {
              success: false,
              message: '未配置OSS存储，无法执行恢复操作'
            };
          }

          // 更新OSS服务配置
          mockOSSService.updateConfig(ossConfig);

          // 定义进度回调函数
          const progressCallback = (progress: number, message: string) => {
            event.sender.send('restore-progress', { progress, message });
          };

          const result = await mockDatabaseSyncService.restoreDatabaseFromOSS(backupName, progressCallback);
          
          if (result.success) {
            console.log('✅ 数据库恢复成功:', backupName);
          } else {
            console.error('❌ 数据库恢复失败:', result.message);
          }

          return result;
        } catch (error) {
          console.error('❌ 从OSS恢复数据库失败:', error);
          return {
            success: false,
            message: `恢复失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent, backupName);

      // 验证结果
      expect(result).toEqual(expectedResult);
      expect(mockSettingsService.getOSSConfig).toHaveBeenCalled();
      expect(mockOSSService.updateConfig).toHaveBeenCalled();
      expect(mockDatabaseSyncService.restoreDatabaseFromOSS).toHaveBeenCalledWith(
        backupName,
        expect.any(Function)
      );

      // 验证进度回调的调用
      const progressCallback = mockDatabaseSyncService.restoreDatabaseFromOSS.mock.calls[0][1];
      progressCallback(75, '正在恢复...');
      expect(mockEvent.sender.send).toHaveBeenCalledWith('restore-progress', {
        progress: 75,
        message: '正在恢复...'
      });
    });

    it('应该处理空备份名称', async () => {
      const handler = async (event: any, backupName: string) => {
        if (!backupName) {
          return {
            success: false,
            message: '备份文件名不能为空'
          };
        }
        return { success: true };
      };

      // 执行测试
      const result = await handler(mockEvent, '');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('备份文件名不能为空');
      expect(mockDatabaseSyncService.restoreDatabaseFromOSS).not.toHaveBeenCalled();
    });

    it('应该处理恢复服务异常', async () => {
      // 设置恢复服务抛出异常
      mockDatabaseSyncService.restoreDatabaseFromOSS.mockRejectedValue(new Error('Restore error'));

      const handler = async (event: any, backupName: string) => {
        try {
          const ossConfig = mockSettingsService.getOSSConfig();
          mockOSSService.updateConfig(ossConfig);
          
          const progressCallback = (progress: number, message: string) => {
            event.sender.send('restore-progress', { progress, message });
          };

          return await mockDatabaseSyncService.restoreDatabaseFromOSS(backupName, progressCallback);
        } catch (error) {
          return {
            success: false,
            message: `恢复失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent, 'test-backup.db');

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('恢复失败: Restore error');
    });
  });

  describe('get-database-sync-settings IPC处理器', () => {
    it('应该成功获取数据库同步设置', async () => {
      // 准备测试数据
      const mockSettings = {
        enableDatabaseSync: true,
        lastBackupTime: '2025-07-17T14:30:45.000Z',
        lastRestoreTime: '2025-07-16T10:15:20.000Z'
      };

      const mockSyncStatus = {
        canSync: true,
        lastBackupTime: '2025-07-17T14:30:45.000Z',
        lastRestoreTime: '2025-07-16T10:15:20.000Z'
      };

      // 设置mock返回值
      mockSettingsService.getSettings.mockReturnValue(mockSettings);
      mockDatabaseSyncService.getSyncStatus.mockReturnValue(mockSyncStatus);

      const handler = async () => {
        try {
          const settings = mockSettingsService.getSettings();
          const syncStatus = mockDatabaseSyncService.getSyncStatus();
          
          const result = {
            enabled: settings.enableDatabaseSync || false,
            canSync: syncStatus.canSync,
            lastBackupTime: syncStatus.lastBackupTime,
            lastRestoreTime: syncStatus.lastRestoreTime
          };

          return {
            success: true,
            data: result
          };
        } catch (error) {
          console.error('❌ 获取数据库同步设置失败:', error);
          return {
            success: false,
            message: `获取设置失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler();

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        enabled: true,
        canSync: true,
        lastBackupTime: '2025-07-17T14:30:45.000Z',
        lastRestoreTime: '2025-07-16T10:15:20.000Z'
      });
      expect(mockSettingsService.getSettings).toHaveBeenCalled();
      expect(mockDatabaseSyncService.getSyncStatus).toHaveBeenCalled();
    });

    it('应该处理获取设置时的异常', async () => {
      // 设置服务抛出异常
      mockSettingsService.getSettings.mockImplementation(() => {
        throw new Error('Settings error');
      });

      const handler = async () => {
        try {
          const settings = mockSettingsService.getSettings();
          const syncStatus = mockDatabaseSyncService.getSyncStatus();
          
          return {
            success: true,
            data: {
              enabled: settings.enableDatabaseSync || false,
              canSync: syncStatus.canSync,
              lastBackupTime: syncStatus.lastBackupTime,
              lastRestoreTime: syncStatus.lastRestoreTime
            }
          };
        } catch (error) {
          return {
            success: false,
            message: `获取设置失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler();

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('获取设置失败: Settings error');
    });
  });

  describe('update-database-sync-settings IPC处理器', () => {
    it('应该成功更新数据库同步设置', async () => {
      // 准备测试数据
      const newSettings = { enableDatabaseSync: true };
      
      const updatedSettings = {
        enableDatabaseSync: true,
        lastBackupTime: null,
        lastRestoreTime: null
      };

      const updatedSyncStatus = {
        canSync: true,
        lastBackupTime: null,
        lastRestoreTime: null
      };

      // 设置mock返回值
      mockSettingsService.saveSettings.mockReturnValue(true);
      mockSettingsService.getSettings.mockReturnValue(updatedSettings);
      mockDatabaseSyncService.getSyncStatus.mockReturnValue(updatedSyncStatus);

      const handler = async (_: any, settings: { enableDatabaseSync?: boolean }) => {
        try {
          console.log('⚙️ 更新数据库同步设置:', settings);
          
          // 更新设置
          const saved = mockSettingsService.saveSettings(settings);
          if (!saved) {
            return {
              success: false,
              message: '保存设置失败'
            };
          }
          
          // 获取更新后的设置和状态
          const currentSettings = mockSettingsService.getSettings();
          const syncStatus = mockDatabaseSyncService.getSyncStatus();
          
          const result = {
            enabled: currentSettings.enableDatabaseSync || false,
            canSync: syncStatus.canSync,
            lastBackupTime: syncStatus.lastBackupTime,
            lastRestoreTime: syncStatus.lastRestoreTime
          };

          console.log('✅ 数据库同步设置更新成功');
          return {
            success: true,
            data: result
          };
        } catch (error) {
          console.error('❌ 更新数据库同步设置失败:', error);
          return {
            success: false,
            message: `更新设置失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent, newSettings);

      // 验证结果
      expect(result.success).toBe(true);
      expect(result.data).toEqual({
        enabled: true,
        canSync: true,
        lastBackupTime: null,
        lastRestoreTime: null
      });
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith(newSettings);
      expect(mockSettingsService.getSettings).toHaveBeenCalled();
      expect(mockDatabaseSyncService.getSyncStatus).toHaveBeenCalled();
    });

    it('应该处理设置保存失败', async () => {
      // 设置保存失败
      mockSettingsService.saveSettings.mockReturnValue(false);

      const handler = async (_: any, settings: { enableDatabaseSync?: boolean }) => {
        const saved = mockSettingsService.saveSettings(settings);
        if (!saved) {
          return {
            success: false,
            message: '保存设置失败'
          };
        }
        return { success: true };
      };

      // 执行测试
      const result = await handler(mockEvent, { enableDatabaseSync: true });

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('保存设置失败');
    });

    it('应该处理更新设置时的异常', async () => {
      // 设置服务抛出异常
      mockSettingsService.saveSettings.mockImplementation(() => {
        throw new Error('Save error');
      });

      const handler = async (_: any, settings: { enableDatabaseSync?: boolean }) => {
        try {
          mockSettingsService.saveSettings(settings);
          return { success: true };
        } catch (error) {
          return {
            success: false,
            message: `更新设置失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent, { enableDatabaseSync: true });

      // 验证结果
      expect(result.success).toBe(false);
      expect(result.message).toBe('更新设置失败: Save error');
    });
  });

  describe('参数验证和安全性检查', () => {
    it('应该验证备份文件名的安全性', async () => {
      // 测试危险的文件名
      const dangerousNames = [
        '../../../etc/passwd',
        '../../secrets.txt',
        'backup-2025-07-17.db; rm -rf /',
        'backup<script>alert("xss")</script>.db'
      ];

      const handler = async (event: any, backupName: string) => {
        // 简单的安全检查
        if (backupName.includes('..') || backupName.includes('/') || backupName.includes('<') || backupName.includes('>')) {
          return {
            success: false,
            message: '备份文件名包含不安全字符'
          };
        }
        
        if (!backupName.match(/^backup-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.db$/)) {
          return {
            success: false,
            message: '备份文件名格式无效'
          };
        }

        return { success: true };
      };

      // 测试每个危险文件名
      for (const dangerousName of dangerousNames) {
        const result = await handler(mockEvent, dangerousName);
        expect(result.success).toBe(false);
        expect(result.message).toMatch(/不安全字符|格式无效/);
      }

      // 测试有效的文件名
      const validName = 'backup-2025-07-17-14-30-45.db';
      const validResult = await handler(mockEvent, validName);
      expect(validResult.success).toBe(true);
    });

    it('应该限制并发操作数量', async () => {
      let activeOperations = 0;
      const maxConcurrentOperations = 2;

      const handler = async () => {
        if (activeOperations >= maxConcurrentOperations) {
          return {
            success: false,
            message: '系统繁忙，请稍后重试'
          };
        }

        activeOperations++;
        
        // 模拟异步操作
        await new Promise(resolve => setTimeout(resolve, 100));
        
        activeOperations--;
        
        return { success: true };
      };

      // 同时启动多个操作
      const operations = Array(5).fill(null).map(() => handler());
      const results = await Promise.all(operations);

      // 验证有些操作被拒绝了
      const successCount = results.filter(r => r.success).length;
      const failedCount = results.filter(r => !r.success).length;

      expect(successCount).toBeGreaterThan(0);
      expect(failedCount).toBeGreaterThan(0);
      expect(successCount + failedCount).toBe(5);
    });
  });

  describe('异步操作的正确性', () => {
    it('应该正确处理异步操作的时序', async () => {
      const operationOrder: string[] = [];

      // 模拟备份操作
      mockDatabaseSyncService.backupDatabaseToOSS.mockImplementation(async () => {
        operationOrder.push('backup-start');
        await new Promise(resolve => setTimeout(resolve, 50));
        operationOrder.push('backup-end');
        return { success: true };
      });

      // 模拟列表操作
      mockDatabaseSyncService.listDatabaseBackups.mockImplementation(async () => {
        operationOrder.push('list-start');
        await new Promise(resolve => setTimeout(resolve, 30));
        operationOrder.push('list-end');
        return { success: true, backups: [] };
      });

      const backupHandler = async (event: any) => {
        const ossConfig = mockSettingsService.getOSSConfig();
        mockOSSService.updateConfig(ossConfig);
        return await mockDatabaseSyncService.backupDatabaseToOSS();
      };

      const listHandler = async () => {
        const ossConfig = mockSettingsService.getOSSConfig();
        mockOSSService.updateConfig(ossConfig);
        return await mockDatabaseSyncService.listDatabaseBackups();
      };

      // 并发执行两个操作
      const [backupResult, listResult] = await Promise.all([
        backupHandler(mockEvent),
        listHandler()
      ]);

      // 验证操作都成功
      expect(backupResult.success).toBe(true);
      expect(listResult.success).toBe(true);

      // 验证操作顺序正确（应该并发执行）
      expect(operationOrder).toContain('backup-start');
      expect(operationOrder).toContain('backup-end');
      expect(operationOrder).toContain('list-start');
      expect(operationOrder).toContain('list-end');
    });

    it('应该正确处理Promise拒绝', async () => {
      // 设置操作失败
      mockDatabaseSyncService.backupDatabaseToOSS.mockRejectedValue(new Error('Network error'));

      const handler = async (event: any) => {
        try {
          return await mockDatabaseSyncService.backupDatabaseToOSS();
        } catch (error) {
          return {
            success: false,
            message: `操作失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };

      // 执行测试
      const result = await handler(mockEvent);

      // 验证错误处理
      expect(result.success).toBe(false);
      expect(result.message).toBe('操作失败: Network error');
    });
  });
});