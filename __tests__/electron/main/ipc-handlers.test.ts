import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock服务类
const mockDbManager = {
  testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
  getStats: vi.fn(() => ({ success: true, data: { categories: { count: 5 } } })),
  resetDatabase: vi.fn(() => ({ 
    success: true, 
    message: '数据库重置成功', 
    timestamp: '2024-01-01T00:00:00Z' 
  }))
};

const mockCategoryService = {
  getCategories: vi.fn(() => Promise.resolve([{ id: '1', name: 'Test Category' }])),
  createCategory: vi.fn(() => Promise.resolve({ id: '1', name: 'New Category' })),
  updateCategory: vi.fn(() => Promise.resolve({ id: '1', name: 'Updated Category' })),
  deleteCategory: vi.fn(() => Promise.resolve()),
  getCategoryById: vi.fn(() => Promise.resolve({ id: '1', name: 'Category' })),
  getCategoryWithImages: vi.fn(() => Promise.resolve({ id: '1', name: 'Category', images: [] }))
};

const mockImageService = {
  uploadImage: vi.fn(() => Promise.resolve({ id: '1', title: 'Test Image' })),
  getImageById: vi.fn(() => Promise.resolve({ id: '1', title: 'Image' })),
  updateImage: vi.fn(() => Promise.resolve({ id: '1', title: 'Updated Image' })),
  deleteImage: vi.fn(() => Promise.resolve()),
  getImagePath: vi.fn(() => '/path/to/image.jpg'),
  getThumbnailPath: vi.fn(() => '/path/to/thumbnail.jpg')
};

const mockTagService = {
  getAllTags: vi.fn(() => Promise.resolve([{ id: '1', name: 'Test Tag' }])),
  createTag: vi.fn(() => Promise.resolve({ id: '1', name: 'New Tag' })),
  updateTag: vi.fn(() => Promise.resolve({ id: '1', name: 'Updated Tag' })),
  deleteTag: vi.fn(() => Promise.resolve()),
  getTagById: vi.fn(() => Promise.resolve({ id: '1', name: 'Tag' })),
  searchTags: vi.fn(() => Promise.resolve([{ id: '1', name: 'Search Result' }])),
  addTagToImage: vi.fn(() => Promise.resolve()),
  removeTagFromImage: vi.fn(() => Promise.resolve()),
  getTagsForImage: vi.fn(() => Promise.resolve([{ id: '1', name: 'Tag' }])),
  searchImagesByTags: vi.fn(() => Promise.resolve(['image1', 'image2']))
};

const mockSettingsService = {
  getSettings: vi.fn(() => ({
    storagePath: '/test/storage',
    usesCategoryFolders: true,
    isFirstTimeSetup: false
  })),
  saveSettings: vi.fn(() => true),
  updateStoragePath: vi.fn(() => true)
};

const mockDialog = {
  showOpenDialog: vi.fn(() => Promise.resolve({
    canceled: false,
    filePaths: ['/selected/directory']
  }))
};

// Mock electron
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    on: vi.fn(),
    removeAllListeners: vi.fn()
  },
  dialog: mockDialog
}));

describe('IPC Handlers', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    console.log = vi.fn();
    console.error = vi.fn();
  });

  describe('数据库相关IPC处理器', () => {
    it('应该处理test-database请求', async () => {
      const handler = () => mockDbManager.testConnection();
      
      const result = handler();
      
      expect(result).toEqual({ success: true, message: '连接成功' });
      expect(mockDbManager.testConnection).toHaveBeenCalled();
    });

    it('应该处理get-database-stats请求', async () => {
      const handler = () => mockDbManager.getStats();
      
      const result = handler();
      
      expect(result).toEqual({ success: true, data: { categories: { count: 5 } } });
      expect(mockDbManager.getStats).toHaveBeenCalled();
    });

    it('应该处理reset-database请求', async () => {
      const handler = async () => {
        try {
          console.log('IPC: 收到重置数据库请求');
          const result = mockDbManager.resetDatabase();
          console.log('IPC: 数据库重置结果:', result.message);
          return result;
        } catch (error) {
          console.error('重置数据库失败:', error);
          return {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            message: '重置数据库失败'
          };
        }
      };
      
      const result = await handler();
      
      expect(result).toEqual({
        success: true,
        message: '数据库重置成功',
        timestamp: '2024-01-01T00:00:00Z'
      });
      expect(mockDbManager.resetDatabase).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到重置数据库请求');
      expect(console.log).toHaveBeenCalledWith('IPC: 数据库重置结果:', '数据库重置成功');
    });

    it('应该处理reset-database请求失败的情况', async () => {
      // Mock重置失败
      mockDbManager.resetDatabase.mockReturnValueOnce({
        success: false,
        error: 'Database connection failed',
        message: '数据库重置失败'
      });

      const handler = async () => {
        try {
          console.log('IPC: 收到重置数据库请求');
          const result = mockDbManager.resetDatabase();
          console.log('IPC: 数据库重置结果:', result.message);
          return result;
        } catch (error) {
          console.error('重置数据库失败:', error);
          return {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            message: '重置数据库失败'
          };
        }
      };
      
      const result = await handler();
      
      expect(result).toEqual({
        success: false,
        error: 'Database connection failed',
        message: '数据库重置失败'
      });
      expect(mockDbManager.resetDatabase).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到重置数据库请求');
      expect(console.log).toHaveBeenCalledWith('IPC: 数据库重置结果:', '数据库重置失败');
    });

    it('应该处理reset-database请求异常的情况', async () => {
      // Mock抛出异常
      const testError = new Error('Database reset error');
      mockDbManager.resetDatabase.mockImplementationOnce(() => {
        throw testError;
      });

      const handler = async () => {
        try {
          console.log('IPC: 收到重置数据库请求');
          const result = mockDbManager.resetDatabase();
          console.log('IPC: 数据库重置结果:', result.message);
          return result;
        } catch (error) {
          console.error('重置数据库失败:', error);
          return {
            success: false,
            error: error instanceof Error ? error.message : String(error),
            message: '重置数据库失败'
          };
        }
      };
      
      const result = await handler();
      
      expect(result).toEqual({
        success: false,
        error: 'Database reset error',
        message: '重置数据库失败'
      });
      expect(mockDbManager.resetDatabase).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到重置数据库请求');
      expect(console.error).toHaveBeenCalledWith('重置数据库失败:', testError);
    });
  });

  describe('分类相关IPC处理器', () => {
    it('应该处理get-categories请求', async () => {
      const handler = async (_, skip = 0, limit = 100) => {
        try {
          console.log('IPC: 收到获取分类请求', { skip, limit });
          const result = await mockCategoryService.getCategories(skip, limit);
          console.log('IPC: 分类查询成功', { count: result.length });
          return result;
        } catch (error) {
          console.error('IPC: 分类查询失败', error);
          throw error;
        }
      };

      const result = await handler(null, 0, 10);

      expect(result).toEqual([{ id: '1', name: 'Test Category' }]);
      expect(mockCategoryService.getCategories).toHaveBeenCalledWith(0, 10);
      expect(console.log).toHaveBeenCalledWith('IPC: 收到获取分类请求', { skip: 0, limit: 10 });
    });

    it('应该处理create-category请求', async () => {
      const handler = async (_, categoryData) => {
        try {
          return await mockCategoryService.createCategory(categoryData);
        } catch (error) {
          console.error('IPC: 创建分类失败', error);
          throw error;
        }
      };

      const categoryData = { name: 'New Category' };
      const result = await handler(null, categoryData);

      expect(result).toEqual({ id: '1', name: 'New Category' });
      expect(mockCategoryService.createCategory).toHaveBeenCalledWith(categoryData);
    });

    it('应该处理update-category请求', async () => {
      const handler = async (_, categoryId, categoryData) => {
        try {
          return await mockCategoryService.updateCategory(categoryId, categoryData);
        } catch (error) {
          console.error('IPC: 更新分类失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1', { name: 'Updated Category' });

      expect(result).toEqual({ id: '1', name: 'Updated Category' });
      expect(mockCategoryService.updateCategory).toHaveBeenCalledWith('1', { name: 'Updated Category' });
    });

    it('应该处理delete-category请求', async () => {
      const handler = async (_, categoryId) => {
        try {
          await mockCategoryService.deleteCategory(categoryId);
        } catch (error) {
          console.error('IPC: 删除分类失败', error);
          throw error;
        }
      };

      await handler(null, '1');

      expect(mockCategoryService.deleteCategory).toHaveBeenCalledWith('1');
    });

    it('应该处理get-category-by-id请求', async () => {
      const handler = async (_, categoryId) => {
        try {
          return await mockCategoryService.getCategoryById(categoryId);
        } catch (error) {
          console.error('IPC: 获取分类失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1');

      expect(result).toEqual({ id: '1', name: 'Category' });
      expect(mockCategoryService.getCategoryById).toHaveBeenCalledWith('1');
    });

    it('应该处理get-category-with-images请求', async () => {
      const handler = async (_, categoryId) => {
        try {
          console.log('IPC: 收到获取分类详情(含图片)请求', { categoryId });
          const result = await mockCategoryService.getCategoryWithImages(categoryId);
          console.log('IPC: 分类详情查询成功', { categoryId, imageCount: result?.images?.length || 0 });
          return result;
        } catch (error) {
          console.error('IPC: 获取分类详情失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1');

      expect(result).toEqual({ id: '1', name: 'Category', images: [] });
      expect(mockCategoryService.getCategoryWithImages).toHaveBeenCalledWith('1');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到获取分类详情(含图片)请求', { categoryId: '1' });
    });

    it('应该处理分类操作错误', async () => {
      const error = new Error('Category error');
      mockCategoryService.createCategory.mockRejectedValueOnce(error);

      const handler = async (_, categoryData) => {
        try {
          return await mockCategoryService.createCategory(categoryData);
        } catch (error) {
          console.error('IPC: 创建分类失败', error);
          throw error;
        }
      };

      await expect(handler(null, { name: 'Test' })).rejects.toThrow('Category error');
      expect(console.error).toHaveBeenCalledWith('IPC: 创建分类失败', error);
    });
  });

  describe('图片相关IPC处理器', () => {
    it('应该处理upload-image请求', async () => {
      const handler = async (_, categoryId, fileBuffer, originalFilename, mimeType) => {
        try {
          console.log('IPC: 收到图片上传请求', { categoryId, originalFilename, size: fileBuffer.length });
          return await mockImageService.uploadImage(categoryId, fileBuffer, originalFilename, mimeType);
        } catch (error) {
          console.error('IPC: 图片上传失败', error);
          throw error;
        }
      };

      const fileBuffer = Buffer.from('fake image data');
      const result = await handler(null, '1', fileBuffer, 'test.jpg', 'image/jpeg');

      expect(result).toEqual({ id: '1', title: 'Test Image' });
      expect(mockImageService.uploadImage).toHaveBeenCalledWith('1', fileBuffer, 'test.jpg', 'image/jpeg');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到图片上传请求', {
        categoryId: '1',
        originalFilename: 'test.jpg',
        size: fileBuffer.length
      });
    });

    it('应该处理get-image-by-id请求', async () => {
      const handler = async (_, imageId) => {
        try {
          return await mockImageService.getImageById(imageId);
        } catch (error) {
          console.error('IPC: 获取图片失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1');

      expect(result).toEqual({ id: '1', title: 'Image' });
      expect(mockImageService.getImageById).toHaveBeenCalledWith('1');
    });

    it('应该处理update-image请求', async () => {
      const handler = async (_, imageId, imageData) => {
        try {
          return await mockImageService.updateImage(imageId, imageData);
        } catch (error) {
          console.error('IPC: 更新图片失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1', { title: 'Updated Image' });

      expect(result).toEqual({ id: '1', title: 'Updated Image' });
      expect(mockImageService.updateImage).toHaveBeenCalledWith('1', { title: 'Updated Image' });
    });

    it('应该处理delete-image请求', async () => {
      const handler = async (_, imageId) => {
        try {
          await mockImageService.deleteImage(imageId);
        } catch (error) {
          console.error('IPC: 删除图片失败', error);
          throw error;
        }
      };

      await handler(null, '1');

      expect(mockImageService.deleteImage).toHaveBeenCalledWith('1');
    });

    it('应该处理get-image-path请求', () => {
      const handler = (_, filename) => mockImageService.getImagePath(filename);

      const result = handler(null, 'test.jpg');

      expect(result).toBe('/path/to/image.jpg');
      expect(mockImageService.getImagePath).toHaveBeenCalledWith('test.jpg');
    });

    it('应该处理get-thumbnail-path请求', () => {
      const handler = (_, filename) => mockImageService.getThumbnailPath(filename);

      const result = handler(null, 'test.jpg');

      expect(result).toBe('/path/to/thumbnail.jpg');
      expect(mockImageService.getThumbnailPath).toHaveBeenCalledWith('test.jpg');
    });

    it('应该处理图片操作错误', async () => {
      const error = new Error('Image error');
      mockImageService.uploadImage.mockRejectedValueOnce(error);

      const handler = async (_, categoryId, fileBuffer, originalFilename, mimeType) => {
        try {
          return await mockImageService.uploadImage(categoryId, fileBuffer, originalFilename, mimeType);
        } catch (error) {
          console.error('IPC: 图片上传失败', error);
          throw error;
        }
      };

      await expect(handler(null, '1', Buffer.from('data'), 'test.jpg', 'image/jpeg'))
        .rejects.toThrow('Image error');
      expect(console.error).toHaveBeenCalledWith('IPC: 图片上传失败', error);
    });
  });

  describe('标签相关IPC处理器', () => {
    it('应该处理get-all-tags请求', async () => {
      const handler = async () => {
        try {
          console.log('IPC: 收到获取所有标签请求');
          const result = await mockTagService.getAllTags();
          console.log('IPC: 获取标签成功', { count: result.length });
          return result;
        } catch (error) {
          console.error('IPC: 获取标签失败', error);
          throw error;
        }
      };

      const result = await handler();

      expect(result).toEqual([{ id: '1', name: 'Test Tag' }]);
      expect(mockTagService.getAllTags).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到获取所有标签请求');
    });

    it('应该处理create-tag请求', async () => {
      const handler = async (_, tagData) => {
        try {
          console.log('IPC: 收到创建标签请求', tagData);
          return await mockTagService.createTag(tagData);
        } catch (error) {
          console.error('IPC: 创建标签失败', error);
          throw error;
        }
      };

      const tagData = { name: 'New Tag' };
      const result = await handler(null, tagData);

      expect(result).toEqual({ id: '1', name: 'New Tag' });
      expect(mockTagService.createTag).toHaveBeenCalledWith(tagData);
      expect(console.log).toHaveBeenCalledWith('IPC: 收到创建标签请求', tagData);
    });

    it('应该处理update-tag请求', async () => {
      const handler = async (_, tagId, tagData) => {
        try {
          console.log('IPC: 收到更新标签请求', { tagId, tagData });
          return await mockTagService.updateTag(tagId, tagData);
        } catch (error) {
          console.error('IPC: 更新标签失败', error);
          throw error;
        }
      };

      const result = await handler(null, '1', { name: 'Updated Tag' });

      expect(result).toEqual({ id: '1', name: 'Updated Tag' });
      expect(mockTagService.updateTag).toHaveBeenCalledWith('1', { name: 'Updated Tag' });
    });

    it('应该处理delete-tag请求', async () => {
      const handler = async (_, tagId) => {
        try {
          console.log('IPC: 收到删除标签请求', { tagId });
          await mockTagService.deleteTag(tagId);
        } catch (error) {
          console.error('IPC: 删除标签失败', error);
          throw error;
        }
      };

      await handler(null, '1');

      expect(mockTagService.deleteTag).toHaveBeenCalledWith('1');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到删除标签请求', { tagId: '1' });
    });

    it('应该处理search-tags请求', async () => {
      const handler = async (_, query) => {
        try {
          console.log('IPC: 收到搜索标签请求', { query });
          const result = await mockTagService.searchTags(query);
          console.log('IPC: 标签搜索成功', { query, count: result.length });
          return result;
        } catch (error) {
          console.error('IPC: 标签搜索失败', error);
          throw error;
        }
      };

      const result = await handler(null, 'test');

      expect(result).toEqual([{ id: '1', name: 'Search Result' }]);
      expect(mockTagService.searchTags).toHaveBeenCalledWith('test');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到搜索标签请求', { query: 'test' });
    });

    it('应该处理add-tag-to-image请求', async () => {
      const handler = async (_, imageId, tagId) => {
        try {
          console.log('IPC: 收到为图片添加标签请求', { imageId, tagId });
          await mockTagService.addTagToImage(imageId, tagId);
        } catch (error) {
          console.error('IPC: 添加标签失败', error);
          throw error;
        }
      };

      await handler(null, 'image1', 'tag1');

      expect(mockTagService.addTagToImage).toHaveBeenCalledWith('image1', 'tag1');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到为图片添加标签请求', {
        imageId: 'image1',
        tagId: 'tag1'
      });
    });

    it('应该处理remove-tag-from-image请求', async () => {
      const handler = async (_, imageId, tagId) => {
        try {
          console.log('IPC: 收到从图片移除标签请求', { imageId, tagId });
          await mockTagService.removeTagFromImage(imageId, tagId);
        } catch (error) {
          console.error('IPC: 移除标签失败', error);
          throw error;
        }
      };

      await handler(null, 'image1', 'tag1');

      expect(mockTagService.removeTagFromImage).toHaveBeenCalledWith('image1', 'tag1');
      expect(console.log).toHaveBeenCalledWith('IPC: 收到从图片移除标签请求', {
        imageId: 'image1',
        tagId: 'tag1'
      });
    });

    it('应该处理get-tags-for-image请求', async () => {
      const handler = async (_, imageId) => {
        try {
          return await mockTagService.getTagsForImage(imageId);
        } catch (error) {
          console.error('IPC: 获取图片标签失败', error);
          throw error;
        }
      };

      const result = await handler(null, 'image1');

      expect(result).toEqual([{ id: '1', name: 'Tag' }]);
      expect(mockTagService.getTagsForImage).toHaveBeenCalledWith('image1');
    });

    it('应该处理search-images-by-tags请求', async () => {
      const handler = async (_, tagNames) => {
        try {
          console.log('IPC: 收到根据标签搜索图片请求', { tagNames });
          const result = await mockTagService.searchImagesByTags(tagNames);
          console.log('IPC: 根据标签搜索图片成功', { tagNames, count: result.length });
          return result;
        } catch (error) {
          console.error('IPC: 根据标签搜索图片失败', error);
          throw error;
        }
      };

      const result = await handler(null, ['tag1', 'tag2']);

      expect(result).toEqual(['image1', 'image2']);
      expect(mockTagService.searchImagesByTags).toHaveBeenCalledWith(['tag1', 'tag2']);
      expect(console.log).toHaveBeenCalledWith('IPC: 收到根据标签搜索图片请求', {
        tagNames: ['tag1', 'tag2']
      });
    });
  });

  describe('设置相关IPC处理器', () => {
    it('应该处理get-storage-settings请求', () => {
      const handler = () => {
        try {
          console.log('IPC: 收到获取存储设置请求');
          const settings = mockSettingsService.getSettings();
          console.log('IPC: 获取存储设置成功', settings);
          return settings;
        } catch (error) {
          console.error('IPC: 获取存储设置失败', error);
          throw error;
        }
      };

      const result = handler();

      expect(result).toEqual({
        storagePath: '/test/storage',
        usesCategoryFolders: true,
        isFirstTimeSetup: false
      });
      expect(mockSettingsService.getSettings).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到获取存储设置请求');
    });

    it('应该处理update-storage-settings请求', async () => {
      const handler = async (_, newSettings) => {
        try {
          console.log('IPC: 收到更新存储设置请求', newSettings);
          const success = mockSettingsService.saveSettings(newSettings);
          
          if (success) {
            console.log('IPC: 存储设置更新成功');
            return { success: true, message: '设置更新成功' };
          } else {
            throw new Error('设置保存失败');
          }
        } catch (error) {
          console.error('IPC: 更新存储设置失败', error);
          throw error;
        }
      };

      const newSettings = { storagePath: '/new/path' };
      const result = await handler(null, newSettings);

      expect(result).toEqual({ success: true, message: '设置更新成功' });
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith(newSettings);
      expect(console.log).toHaveBeenCalledWith('IPC: 收到更新存储设置请求', newSettings);
    });

    it('应该处理select-directory请求', async () => {
      const handler = async () => {
        try {
          console.log('IPC: 收到选择目录请求');
          const result = await mockDialog.showOpenDialog(null, {
            title: '选择图片存储目录',
            properties: ['openDirectory', 'createDirectory'],
            buttonLabel: '选择目录'
          });

          if (result.canceled) {
            console.log('IPC: 用户取消了目录选择');
            return { success: false, path: null };
          } else {
            console.log('IPC: 目录选择成功', result.filePaths[0]);
            return { success: true, path: result.filePaths[0] };
          }
        } catch (error) {
          console.error('IPC: 目录选择失败', error);
          throw error;
        }
      };

      const result = await handler();

      expect(result).toEqual({ success: true, path: '/selected/directory' });
      expect(mockDialog.showOpenDialog).toHaveBeenCalled();
      expect(console.log).toHaveBeenCalledWith('IPC: 收到选择目录请求');
    });

    it('应该处理目录选择取消的情况', async () => {
      mockDialog.showOpenDialog.mockResolvedValueOnce({ canceled: true, filePaths: [] });

      const handler = async () => {
        try {
          const result = await mockDialog.showOpenDialog(null, {
            title: '选择图片存储目录',
            properties: ['openDirectory', 'createDirectory']
          });

          if (result.canceled) {
            console.log('IPC: 用户取消了目录选择');
            return { success: false, path: null };
          } else {
            return { success: true, path: result.filePaths[0] };
          }
        } catch (error) {
          console.error('IPC: 目录选择失败', error);
          throw error;
        }
      };

      const result = await handler();

      expect(result).toEqual({ success: false, path: null });
      expect(console.log).toHaveBeenCalledWith('IPC: 用户取消了目录选择');
    });

    it('应该处理设置保存失败的情况', async () => {
      mockSettingsService.saveSettings.mockReturnValueOnce(false);

      const handler = async (_, newSettings) => {
        try {
          const success = mockSettingsService.saveSettings(newSettings);
          
          if (success) {
            return { success: true, message: '设置更新成功' };
          } else {
            throw new Error('设置保存失败');
          }
        } catch (error) {
          console.error('IPC: 更新存储设置失败', error);
          throw error;
        }
      };

      await expect(handler(null, { storagePath: '/new/path' }))
        .rejects.toThrow('设置保存失败');
      expect(console.error).toHaveBeenCalledWith(
        'IPC: 更新存储设置失败',
        expect.any(Error)
      );
    });
  });

  describe('错误处理和边界情况', () => {
    it('应该处理服务层抛出的异常', async () => {
      const error = new Error('Service error');
      mockCategoryService.getCategories.mockRejectedValueOnce(error);

      const handler = async (_, skip = 0, limit = 100) => {
        try {
          return await mockCategoryService.getCategories(skip, limit);
        } catch (error) {
          console.error('IPC: 分类查询失败', error);
          throw error;
        }
      };

      await expect(handler(null, 0, 10)).rejects.toThrow('Service error');
      expect(console.error).toHaveBeenCalledWith('IPC: 分类查询失败', error);
    });

    it('应该处理空参数的情况', async () => {
      const handler = async (_, categoryData) => {
        try {
          return await mockCategoryService.createCategory(categoryData);
        } catch (error) {
          console.error('IPC: 创建分类失败', error);
          throw error;
        }
      };

      await handler(null, null);
      expect(mockCategoryService.createCategory).toHaveBeenCalledWith(null);
    });

    it('应该正确传递所有参数', async () => {
      const handler = async (_, imageId, tagId) => {
        await mockTagService.addTagToImage(imageId, tagId);
      };

      await handler(null, 'testImageId', 'testTagId');
      expect(mockTagService.addTagToImage).toHaveBeenCalledWith('testImageId', 'testTagId');
    });
  });
});