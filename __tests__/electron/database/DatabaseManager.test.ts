import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn((name: string) => {
      switch (name) {
        case 'userData':
          return '/tmp/test-userData';
        case 'documents':
          return '/tmp/test-documents';
        default:
          return '/tmp/test';
      }
    }),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';

describe('DatabaseManager', () => {
  let dbManager: DatabaseManager;
  let testDbManager: TestDatabaseManager;

  beforeEach(() => {
    testDbManager = TestDatabaseManager.getInstance();
    // 创建一个新的DatabaseManager实例进行测试
    dbManager = new DatabaseManager();
  });

  afterEach(() => {
    if (dbManager) {
      dbManager.close();
    }
    testDbManager.cleanupAll();
  });

  describe('构造函数和初始化', () => {
    it('应该成功创建DatabaseManager实例', () => {
      expect(dbManager).toBeInstanceOf(DatabaseManager);
      expect(dbManager.getDatabase()).toBeDefined();
    });

    it('应该创建所有必需的数据库表', () => {
      const db = dbManager.getDatabase();
      
      // 检查表是否存在
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);

      expect(tables).toContain('categories');
      expect(tables).toContain('images');
      expect(tables).toContain('tags');
      expect(tables).toContain('image_tags');
    });

    it('应该创建所有必需的索引', () => {
      const db = dbManager.getDatabase();
      
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);

      expect(indexes).toContain('idx_images_category');
      expect(indexes).toContain('idx_images_created');
      expect(indexes).toContain('idx_tags_name');
      expect(indexes).toContain('idx_image_tags_image');
      expect(indexes).toContain('idx_image_tags_tag');
    });

    it('应该插入初始数据', () => {
      const stats = dbManager.getStats();
      
      expect(stats.success).toBe(true);
      expect(stats.data.categories.count).toBe(3); // 现在只有3个分类
      expect(stats.data.tags.count).toBe(0); // 现在没有初始标签
    });

    it('应该启用外键约束', () => {
      const db = dbManager.getDatabase();
      const foreignKeys = db.pragma('foreign_keys');
      expect(foreignKeys[0].foreign_keys).toBe(1);
    });

    it('应该设置WAL模式', () => {
      const db = dbManager.getDatabase();
      const journalMode = db.pragma('journal_mode');
      expect(journalMode[0].journal_mode).toBe('wal');
    });
  });

  describe('数据库操作方法', () => {
    it('getDatabase() 应该返回数据库实例', () => {
      const db = dbManager.getDatabase();
      expect(db).toBeDefined();
      expect(typeof db.prepare).toBe('function');
    });

    it('testConnection() 应该返回成功的连接测试结果', () => {
      const result = dbManager.testConnection();
      
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库连接成功');
      expect(result.mode).toBe('SQLite');
      expect(typeof result.categories).toBe('number');
      expect(typeof result.images).toBe('number');
      expect(typeof result.tags).toBe('number');
      expect(typeof result.imageTags).toBe('number');
    });

    it('getStats() 应该返回数据库统计信息', () => {
      const result = dbManager.getStats();
      
      expect(result.success).toBe(true);
      expect(result.message).toBe('获取统计信息成功');
      expect(result.mode).toBe('SQLite');
      expect(result.data).toBeDefined();
      expect(typeof result.data.categories.count).toBe('number');
      expect(typeof result.data.images.count).toBe('number');
      expect(typeof result.data.tags.count).toBe('number');
      expect(typeof result.data.imageTags.count).toBe('number');
    });

    it('close() 应该正常关闭数据库连接', () => {
      expect(() => {
        dbManager.close();
      }).not.toThrow();
    });
  });

  describe('初始数据验证', () => {
    it('应该包含预期的初始分类', () => {
      const db = dbManager.getDatabase();
      const categories = db.prepare('SELECT * FROM categories').all();
      
      expect(categories.length).toBe(3);
      
      const categoryNames = categories.map((cat: any) => cat.name);
      expect(categoryNames).toContain('喜鹊');
      expect(categoryNames).toContain('麻雀');
      expect(categoryNames).toContain('斑鸠');
    });

    it('应该不包含初始标签', () => {
      const db = dbManager.getDatabase();
      const tags = db.prepare('SELECT * FROM tags').all();
      
      expect(tags.length).toBe(0); // 现在没有初始标签
    });

    it('初始数据应该只插入一次', () => {
      // 创建另一个DatabaseManager实例
      const dbManager2 = new DatabaseManager();
      
      const stats1 = dbManager.getStats();
      const stats2 = dbManager2.getStats();
      
      // 应该有相同的数据计数，说明没有重复插入
      expect(stats1.data.categories.count).toBe(stats2.data.categories.count);
      expect(stats1.data.tags.count).toBe(stats2.data.tags.count);
      expect(stats1.data.categories.count).toBe(3); // 验证确实是3个分类
      expect(stats1.data.tags.count).toBe(0); // 验证确实没有标签
      
      dbManager2.close();
    });
  });

  describe('错误处理', () => {
    it('当数据库路径无效时应该处理错误', () => {
      // 这个测试已经在上面的构造函数测试中验证了
      // DatabaseManager能够创建目录结构，所以即使路径不存在也能正常工作
      expect(dbManager).toBeInstanceOf(DatabaseManager);
    });

    it('testConnection() 应该处理数据库访问异常', () => {
      // 测试已初始化的数据库的正常连接测试
      const result = dbManager.testConnection();
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库连接成功');
      expect(result.categories).toBeGreaterThanOrEqual(0);
      expect(result.tags).toBeGreaterThanOrEqual(0);
      expect(result.images).toBeGreaterThanOrEqual(0);
      expect(result.imageTags).toBeGreaterThanOrEqual(0);
    });

    it('getStats() 应该处理数据库统计查询异常', () => {
      // 测试已初始化的数据库的正常统计查询
      const result = dbManager.getStats();
      expect(result.success).toBe(true);
      expect(result.message).toBe('获取统计信息成功');
      expect(result.data).toBeDefined();
      expect(result.data.categories).toBeDefined();
      expect(result.data.images).toBeDefined();
      expect(result.data.tags).toBeDefined();
      expect(result.data.imageTags).toBeDefined();
    });
  });

  describe('数据完整性和约束', () => {
    it('应该强制执行外键约束', () => {
      const db = dbManager.getDatabase();
      
      // 尝试插入不存在分类的图片应该失败
      expect(() => {
        db.prepare(`
          INSERT INTO images (id, category_id, image_url, created_at)
          VALUES ('test-id', 'non-existent-category', 'test-url', '2024-01-01T00:00:00Z')
        `).run();
      }).toThrow();
    });

    it('应该强制执行唯一约束', () => {
      const db = dbManager.getDatabase();
      
      // 尝试插入重复的标签名应该失败
      expect(() => {
        const transaction = db.transaction(() => {
          db.prepare(`
            INSERT INTO tags (id, name, created_at, updated_at)
            VALUES ('test-id-1', 'duplicate-tag', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
          `).run();
          
          db.prepare(`
            INSERT INTO tags (id, name, created_at, updated_at)
            VALUES ('test-id-2', 'duplicate-tag', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
          `).run();
        });
        
        transaction();
      }).toThrow();
    });
  });

  describe('数据库重置功能', () => {
    // 辅助函数：为测试添加数据
    function addTestData(db: any, testId: string = '') {
      const tagSuffix = testId ? `-${testId}` : '';
      const imageSuffix = testId ? `-${testId}` : '';
      
      // 添加一些标签
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-1${tagSuffix}', '测试标签1${tagSuffix}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-2${tagSuffix}', '测试标签2${tagSuffix}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      // 添加一些图片数据
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('test-image-1${imageSuffix}', 'magpie', 'test-url-1${imageSuffix}', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('test-image-2${imageSuffix}', 'sparrow', 'test-url-2${imageSuffix}', '2024-01-01T00:00:00Z')
      `).run();
      
      // 添加图片标签关联
      db.prepare(`
        INSERT INTO image_tags (image_id, tag_id)
        VALUES ('test-image-1${imageSuffix}', 'test-tag-1${tagSuffix}')
      `).run();
    }

    it('resetDatabase() 应该成功重置数据库', () => {
      const db = dbManager.getDatabase();
      
      // 为此测试添加数据
      addTestData(db, 'reset-test');
      
      // 验证重置前有数据
      const statsBefore = dbManager.getStats();
      expect(statsBefore.data.categories.count).toBe(3);
      expect(statsBefore.data.tags.count).toBe(2);
      expect(statsBefore.data.images.count).toBe(2);
      expect(statsBefore.data.imageTags.count).toBe(1);
      
      // 执行重置
      const result = dbManager.resetDatabase();
      
      // 验证重置结果
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库重置成功');
      expect(result.timestamp).toBeDefined();
      
      // 验证重置后数据
      const statsAfter = dbManager.getStats();
      expect(statsAfter.data.categories.count).toBe(3); // 重新插入的默认分类
      expect(statsAfter.data.tags.count).toBe(0); // 没有标签
      expect(statsAfter.data.images.count).toBe(0); // 没有图片
      expect(statsAfter.data.imageTags.count).toBe(0); // 没有图片标签关联
    });

    it('resetDatabase() 应该重新插入正确的默认分类', () => {
      const db = dbManager.getDatabase();
      
      // 为此测试添加数据
      addTestData(db, 'category-test');
      
      // 执行重置
      const result = dbManager.resetDatabase();
      expect(result.success).toBe(true);
      
      // 验证分类数据
      const categories = db.prepare('SELECT * FROM categories ORDER BY name').all();
      
      expect(categories.length).toBe(3);
      
      const categoryNames = categories.map((cat: any) => cat.name);
      expect(categoryNames).toContain('喜鹊');
      expect(categoryNames).toContain('麻雀');
      expect(categoryNames).toContain('斑鸠');
      
      // 验证分类ID
      const categoryIds = categories.map((cat: any) => cat.id);
      expect(categoryIds).toContain('magpie');
      expect(categoryIds).toContain('sparrow');
      expect(categoryIds).toContain('turtle-dove');
    });

    it('resetDatabase() 应该正确处理事务', () => {
      const db = dbManager.getDatabase();
      
      // 为此测试添加数据
      addTestData(db, 'transaction-test');
      
      // 添加更多数据来测试事务
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-3-transaction-test', '测试标签3-transaction-test', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      const statsBefore = dbManager.getStats();
      expect(statsBefore.data.tags.count).toBe(3);
      
      // 执行重置
      const result = dbManager.resetDatabase();
      expect(result.success).toBe(true);
      
      // 验证所有数据都被正确清理
      const statsAfter = dbManager.getStats();
      expect(statsAfter.data.tags.count).toBe(0);
      expect(statsAfter.data.images.count).toBe(0);
      expect(statsAfter.data.imageTags.count).toBe(0);
      expect(statsAfter.data.categories.count).toBe(3);
    });

    it('resetDatabase() 应该正确处理sqlite_sequence表不存在的情况', () => {
      const db = dbManager.getDatabase();
      
      // 为此测试添加数据
      addTestData(db, 'sequence-test');
      
      // 首先验证sqlite_sequence表是否存在
      const sequenceTableExists = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='sqlite_sequence'
      `).get();
      
      console.log('sqlite_sequence表存在:', !!sequenceTableExists);
      
      // 这个测试应该验证即使sqlite_sequence表不存在，重置也能成功
      const result = dbManager.resetDatabase();
      expect(result.success).toBe(true);
      expect(result.message).toBe('数据库重置成功');
      
      // 验证重置后的数据状态
      const stats = dbManager.getStats();
      expect(stats.data.categories.count).toBe(3);
      expect(stats.data.tags.count).toBe(0);
      expect(stats.data.images.count).toBe(0);
      expect(stats.data.imageTags.count).toBe(0);
    });

    it('resetDatabase() 应该在sqlite_sequence表不存在时正常工作', () => {
      // 这个测试专门模拟没有sqlite_sequence表的情况
      const db = dbManager.getDatabase();
      
      // 验证我们的数据库模式确实不会创建sqlite_sequence表
      // 因为我们使用的是TEXT PRIMARY KEY而不是INTEGER PRIMARY KEY AUTOINCREMENT
      const sequenceTableExists = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='sqlite_sequence'
      `).get();
      
      // 由于我们使用UUID主键，sqlite_sequence表通常不存在
      if (!sequenceTableExists) {
        console.log('✅ 正确：sqlite_sequence表不存在（使用UUID主键）');
      }
      
      // 添加一些测试数据（使用唯一ID避免冲突）
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-unique', '测试标签-unique', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      // 验证数据已添加
      const tagsBefore = db.prepare('SELECT COUNT(*) as count FROM tags').get() as { count: number };
      expect(tagsBefore.count).toBe(1);
      
      // 执行重置 - 这应该成功，即使sqlite_sequence表不存在
      const result = dbManager.resetDatabase();
      expect(result.success).toBe(true);
      
      // 验证数据已被清理
      const tagsAfter = db.prepare('SELECT COUNT(*) as count FROM tags').get() as { count: number };
      expect(tagsAfter.count).toBe(0);
      
      // 验证默认分类已恢复
      const categoriesAfter = db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number };
      expect(categoriesAfter.count).toBe(3);
    });

    it('resetDatabase() 错误处理', () => {
      // 创建一个独立的数据库管理器实例来测试错误处理
      const testDbManager = new DatabaseManager();
      
      // 为了确保测试独立性，先验证能正常工作
      expect(testDbManager.getStats().success).toBe(true);
      
      // 关闭数据库连接来模拟错误
      testDbManager.close();
      
      // 尝试重置应该失败
      const result = testDbManager.resetDatabase();
      expect(result.success).toBe(false);
      expect(result.message).toBe('数据库重置失败');
      expect(result.error).toBeDefined();
    });

    it('resetDatabase() 应该保持数据库结构完整', () => {
      const db = dbManager.getDatabase();
      
      // 为此测试添加数据
      addTestData(db, 'structure-test');
      
      // 执行重置
      const result = dbManager.resetDatabase();
      expect(result.success).toBe(true);
      
      // 验证表结构仍然存在
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);
      
      expect(tables).toContain('categories');
      expect(tables).toContain('images');
      expect(tables).toContain('tags');
      expect(tables).toContain('image_tags');
      
      // 验证索引仍然存在
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);
      
      expect(indexes).toContain('idx_images_category');
      expect(indexes).toContain('idx_images_created');
      expect(indexes).toContain('idx_tags_name');
      expect(indexes).toContain('idx_image_tags_image');
      expect(indexes).toContain('idx_image_tags_tag');
    });
  });
});