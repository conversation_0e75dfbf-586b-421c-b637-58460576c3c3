import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn((name: string) => {
      switch (name) {
        case 'userData':
          return '/tmp/test-userData';
        case 'documents':
          return '/tmp/test-documents';
        default:
          return '/tmp/test';
      }
    }),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';

describe('数据库重置边界情况测试', () => {
  let dbManager: DatabaseManager;
  let testDbManager: TestDatabaseManager;

  beforeEach(() => {
    testDbManager = TestDatabaseManager.getInstance();
    dbManager = new DatabaseManager();
  });

  afterEach(() => {
    if (dbManager) {
      dbManager.close();
    }
    testDbManager.cleanupAll();
  });

  describe('sqlite_sequence表处理', () => {
    it('应该正确处理sqlite_sequence表不存在的情况', () => {
      const db = dbManager.getDatabase();
      
      // 验证sqlite_sequence表是否存在
      const sequenceTableCheck = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='sqlite_sequence'
      `).get();
      
      console.log('sqlite_sequence表存在状态:', !!sequenceTableCheck);
      
      // 用现在的时间戳生成唯一ID避免冲突
      const timestamp = Date.now();
      
      // 添加测试数据
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-${timestamp}', '测试标签${timestamp}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('test-img-${timestamp}', 'magpie', 'test-url-${timestamp}', '2024-01-01T00:00:00Z')
      `).run();
      
      // 验证数据已添加
      const statsBefore = dbManager.getStats();
      expect(statsBefore.data.tags.count).toBe(1);
      expect(statsBefore.data.images.count).toBe(1);
      
      // 执行重置 - 这应该成功，无论sqlite_sequence表是否存在
      const resetResult = dbManager.resetDatabase();
      
      // 验证重置成功
      expect(resetResult.success).toBe(true);
      expect(resetResult.message).toBe('数据库重置成功');
      
      // 验证数据已被清理
      const statsAfter = dbManager.getStats();
      expect(statsAfter.data.categories.count).toBe(3); // 默认分类
      expect(statsAfter.data.tags.count).toBe(0);
      expect(statsAfter.data.images.count).toBe(0);
      expect(statsAfter.data.imageTags.count).toBe(0);
    });

    it('应该在尝试访问不存在的sqlite_sequence表时不抛出错误', () => {
      const db = dbManager.getDatabase();
      
      // 手动测试我们的修复逻辑
      expect(() => {
        const sequenceTableExists = db.prepare(`
          SELECT name FROM sqlite_master 
          WHERE type='table' AND name='sqlite_sequence'
        `).get();
        
        if (sequenceTableExists) {
          db.exec('DELETE FROM sqlite_sequence WHERE name IN ("categories", "images", "tags", "image_tags")');
        }
      }).not.toThrow();
    });

    it('应该在直接调用sqlite_sequence删除时抛出错误（证明修复的必要性）', () => {
      const db = dbManager.getDatabase();
      
      // 这个测试证明了原始代码的问题
      expect(() => {
        db.exec('DELETE FROM sqlite_sequence WHERE name IN ("categories", "images", "tags", "image_tags")');
      }).toThrow(/no such table: sqlite_sequence/);
    });
  });

  describe('数据库schema验证', () => {
    it('应该验证我们的数据库不使用AUTOINCREMENT', () => {
      const db = dbManager.getDatabase();
      
      // 检查表的创建语句
      const tableSchemas = db.prepare(`
        SELECT name, sql FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all();
      
      for (const table of tableSchemas) {
        const createSQL = (table as any).sql;
        const tableName = (table as any).name;
        
        // 验证没有使用AUTOINCREMENT
        expect(createSQL.toUpperCase()).not.toContain('AUTOINCREMENT');
        
        // 只对有单个id主键的表验证TEXT PRIMARY KEY
        // image_tags表使用的是复合主键，不包含id列
        if (tableName !== 'image_tags') {
          expect(createSQL).toMatch(/id TEXT PRIMARY KEY/);
        } else {
          // 验证image_tags表使用的是复合主键
          expect(createSQL).toMatch(/PRIMARY KEY \(image_id, tag_id\)/);
        }
      }
    });

    it('应该验证sqlite_sequence表确实不存在', () => {
      const db = dbManager.getDatabase();
      
      const sequenceTable = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='sqlite_sequence'
      `).get();
      
      // 由于我们使用UUID主键，sqlite_sequence表不应该存在
      expect(sequenceTable).toBeUndefined();
    });
  });

  describe('重置功能健壮性测试', () => {
    it('应该在多次重置后保持稳定', () => {
      const db = dbManager.getDatabase();
      
      // 执行多次重置
      for (let i = 0; i < 3; i++) {
        // 使用时间戳和循环索引生成唯一ID
        const timestamp = Date.now();
        
        // 添加数据
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES ('test-tag-${i}-${timestamp}', '测试标签${i}-${timestamp}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run();
        
        // 重置
        const result = dbManager.resetDatabase();
        expect(result.success).toBe(true);
        
        // 验证状态
        const stats = dbManager.getStats();
        expect(stats.data.categories.count).toBe(3);
        expect(stats.data.tags.count).toBe(0);
        expect(stats.data.images.count).toBe(0);
        expect(stats.data.imageTags.count).toBe(0);
      }
    });

    it('应该在数据库被污染后能正确恢复', () => {
      const db = dbManager.getDatabase();
      
      // 用现在的时间戳生成唯一ID避免冲突
      const timestamp = Date.now();
      
      // 模拟"污染"数据库
      const corruptData = [
        { id: `corrupt-tag-1-${timestamp}`, name: `损坏标签1-${timestamp}` },
        { id: `corrupt-tag-2-${timestamp}`, name: `损坏标签2-${timestamp}` },
        { id: `corrupt-tag-3-${timestamp}`, name: `损坏标签3-${timestamp}` }
      ];
      
      for (const tag of corruptData) {
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES (?, ?, '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run(tag.id, tag.name);
      }
      
      // 验证"污染"状态
      const corruptStats = dbManager.getStats();
      expect(corruptStats.data.tags.count).toBe(3);
      
      // 执行重置
      const resetResult = dbManager.resetDatabase();
      expect(resetResult.success).toBe(true);
      
      // 验证完全恢复
      const cleanStats = dbManager.getStats();
      expect(cleanStats.data.categories.count).toBe(3);
      expect(cleanStats.data.tags.count).toBe(0);
      expect(cleanStats.data.images.count).toBe(0);
      expect(cleanStats.data.imageTags.count).toBe(0);
      
      // 验证恢复的分类是正确的（按名称排序）
      const categories = db.prepare('SELECT id, name FROM categories ORDER BY name').all();
      
      // 验证分类数量和内容正确
      expect(categories.length).toBe(3);
      
      // 验证所有默认分类都存在
      const categoryIds = categories.map(cat => cat.id);
      expect(categoryIds).toContain('turtle-dove');
      expect(categoryIds).toContain('magpie');
      expect(categoryIds).toContain('sparrow');
      
      const categoryNames = categories.map(cat => cat.name);
      expect(categoryNames).toContain('斑鸠');
      expect(categoryNames).toContain('喜鹊');
      expect(categoryNames).toContain('麻雀');
    });
  });
});