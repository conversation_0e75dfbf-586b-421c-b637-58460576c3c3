import { v4 as uuidv4 } from 'uuid';

export interface TestCategory {
  id: string;
  name: string;
  description?: string;
  thumbnail_path?: string;
  thumbnail_url?: string;
  created_at: string;
  updated_at: string;
}

export interface TestImage {
  id: string;
  category_id: string;
  title?: string;
  original_filename?: string;
  stored_filename?: string;
  relative_file_path?: string;
  relative_thumbnail_path?: string;
  mime_type?: string;
  size_bytes?: number;
  description?: string;
  created_at: string;
  updated_at?: string;
  file_metadata?: string;
  exif_info?: string;
  image_url: string;
  thumbnail_url?: string;
}

export interface TestTag {
  id: string;
  name: string;
  created_at: string;
  updated_at: string;
}

export interface TestImageTag {
  image_id: string;
  tag_id: string;
}

export class TestDataGenerator {
  private static getTimestamp(): string {
    return new Date().toISOString();
  }

  static createCategory(overrides: Partial<TestCategory> = {}): TestCategory {
    const id = overrides.id || uuidv4();
    const timestamp = this.getTimestamp();
    
    return {
      id,
      name: `Test Category ${id.slice(0, 8)}`,
      description: `Description for test category ${id.slice(0, 8)}`,
      thumbnail_path: null,
      thumbnail_url: null,
      created_at: timestamp,
      updated_at: timestamp,
      ...overrides
    };
  }

  static createImage(categoryId: string, overrides: Partial<TestImage> = {}): TestImage {
    const id = overrides.id || uuidv4();
    const timestamp = this.getTimestamp();
    const filename = `test-image-${id.slice(0, 8)}.jpg`;
    
    return {
      id,
      category_id: categoryId,
      title: `Test Image ${id.slice(0, 8)}`,
      original_filename: filename,
      stored_filename: filename,
      relative_file_path: `images/${filename}`,
      relative_thumbnail_path: `thumbnails/thumb_${filename}`,
      mime_type: 'image/jpeg',
      size_bytes: 1024000,
      description: `Description for test image ${id.slice(0, 8)}`,
      created_at: timestamp,
      updated_at: timestamp,
      file_metadata: JSON.stringify({ width: 1920, height: 1080 }),
      exif_info: JSON.stringify({ camera: 'Test Camera', iso: 100 }),
      image_url: `/images/${filename}`,
      thumbnail_url: `/thumbnails/thumb_${filename}`,
      ...overrides
    };
  }

  static createTag(overrides: Partial<TestTag> = {}): TestTag {
    const id = overrides.id || uuidv4();
    const timestamp = this.getTimestamp();
    
    return {
      id,
      name: `Test Tag ${id.slice(0, 8)}`,
      created_at: timestamp,
      updated_at: timestamp,
      ...overrides
    };
  }

  static createImageTag(imageId: string, tagId: string): TestImageTag {
    return {
      image_id: imageId,
      tag_id: tagId
    };
  }

  static createMultipleCategories(count: number): TestCategory[] {
    return Array.from({ length: count }, (_, index) => 
      this.createCategory({ name: `Category ${index + 1}` })
    );
  }

  static createMultipleImages(categoryId: string, count: number): TestImage[] {
    return Array.from({ length: count }, (_, index) => 
      this.createImage(categoryId, { title: `Image ${index + 1}` })
    );
  }

  static createMultipleTags(count: number): TestTag[] {
    return Array.from({ length: count }, (_, index) => 
      this.createTag({ name: `Tag ${index + 1}` })
    );
  }

  // 创建完整的测试数据集
  static createTestDataset() {
    const categories = this.createMultipleCategories(3);
    const tags = this.createMultipleTags(5);
    
    const images: TestImage[] = [];
    const imageTags: TestImageTag[] = [];

    categories.forEach((category, categoryIndex) => {
      const categoryImages = this.createMultipleImages(category.id, 2);
      images.push(...categoryImages);

      // 为每个图片添加1-3个标签
      categoryImages.forEach((image, imageIndex) => {
        const tagCount = Math.min(categoryIndex + 1, tags.length);
        for (let i = 0; i < tagCount; i++) {
          imageTags.push(this.createImageTag(image.id, tags[i].id));
        }
      });
    });

    return {
      categories,
      images,
      tags,
      imageTags
    };
  }

  // 插入测试数据到数据库
  static insertTestData(db: any, dataset: ReturnType<typeof TestDataGenerator.createTestDataset>) {
    const { categories, images, tags, imageTags } = dataset;

    // 准备插入语句
    const insertCategory = db.prepare(`
      INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);

    const insertImage = db.prepare(`
      INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                         relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                         file_metadata, exif_info, image_url, thumbnail_url)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const insertTag = db.prepare(`
      INSERT INTO tags (id, name, created_at, updated_at)
      VALUES (?, ?, ?, ?)
    `);

    const insertImageTag = db.prepare(`
      INSERT INTO image_tags (image_id, tag_id)
      VALUES (?, ?)
    `);

    // 使用事务插入数据
    const transaction = db.transaction(() => {
      // 插入分类
      for (const category of categories) {
        insertCategory.run(
          category.id, category.name, category.description,
          category.thumbnail_path, category.thumbnail_url,
          category.created_at, category.updated_at
        );
      }

      // 插入标签
      for (const tag of tags) {
        insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);
      }

      // 插入图片
      for (const image of images) {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename,
          image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
          image.mime_type, image.size_bytes, image.description, image.created_at,
          image.updated_at, image.file_metadata, image.exif_info,
          image.image_url, image.thumbnail_url
        );
      }

      // 插入图片标签关联
      for (const imageTag of imageTags) {
        insertImageTag.run(imageTag.image_id, imageTag.tag_id);
      }
    });

    transaction();
    return dataset;
  }
}