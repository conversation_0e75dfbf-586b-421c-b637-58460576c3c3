import Database from 'better-sqlite3';
import { vi } from 'vitest';
import path from 'path';
import fs from 'fs';

export interface TestDatabase {
  db: Database.Database;
  cleanup: () => void;
}

export class TestDatabaseManager {
  private static instance: TestDatabaseManager;
  private testDatabases: Map<string, Database.Database> = new Map();
  private testCounter: number = 0;

  static getInstance(): TestDatabaseManager {
    if (!TestDatabaseManager.instance) {
      TestDatabaseManager.instance = new TestDatabaseManager();
    }
    return TestDatabaseManager.instance;
  }

  createTestDatabase(): TestDatabase {
    // 使用内存数据库避免文件系统冲突
    const testId = `test_${++this.testCounter}_${Date.now()}`;
    const db = new Database(':memory:');
    
    this.testDatabases.set(testId, db);
    
    // 初始化测试数据库结构
    this.initializeTestSchema(db);
    
    return {
      db,
      cleanup: () => {
        try {
          if (this.testDatabases.has(testId)) {
            const database = this.testDatabases.get(testId);
            if (database && database.open) {
              database.close();
            }
            this.testDatabases.delete(testId);
          }
        } catch (error) {
          console.warn(`清理测试数据库失败: ${testId}`, error);
        }
      }
    };
  }

  private initializeTestSchema(db: Database.Database) {
    // 启用外键约束
    db.pragma('foreign_keys = ON');
    
    // 创建表结构（与主数据库一致）
    db.exec(`
      CREATE TABLE categories (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        description TEXT,
        thumbnail_path TEXT,
        thumbnail_url TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `);

    db.exec(`
      CREATE TABLE images (
        id TEXT PRIMARY KEY,
        category_id TEXT NOT NULL,
        title TEXT,
        original_filename TEXT,
        stored_filename TEXT,
        relative_file_path TEXT,
        relative_thumbnail_path TEXT,
        mime_type TEXT,
        size_bytes INTEGER,
        description TEXT,
        created_at TEXT NOT NULL,
        updated_at TEXT,
        file_metadata TEXT,
        exif_info TEXT,
        image_url TEXT NOT NULL,
        thumbnail_url TEXT,
        FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE
      );
    `);

    db.exec(`
      CREATE TABLE tags (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL UNIQUE,
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      );
    `);

    db.exec(`
      CREATE TABLE image_tags (
        image_id TEXT NOT NULL,
        tag_id TEXT NOT NULL,
        PRIMARY KEY (image_id, tag_id),
        FOREIGN KEY (image_id) REFERENCES images(id) ON DELETE CASCADE,
        FOREIGN KEY (tag_id) REFERENCES tags(id) ON DELETE CASCADE
      );
    `);

    // 创建索引
    db.exec(`
      CREATE INDEX idx_images_category ON images(category_id);
      CREATE INDEX idx_images_created ON images(created_at);
      CREATE INDEX idx_tags_name ON tags(name);
      CREATE INDEX idx_image_tags_image ON image_tags(image_id);
      CREATE INDEX idx_image_tags_tag ON image_tags(tag_id);
    `);
  }

  cleanupAll() {
    for (const [testId, db] of this.testDatabases) {
      try {
        if (db && db.open) {
          db.close();
        }
      } catch (error) {
        console.warn(`清理测试数据库失败: ${testId}`, error);
      }
    }
    this.testDatabases.clear();
  }
}

// Mock DatabaseManager类用于测试
export function mockDatabaseManager(testDb?: Database.Database) {
  const mockGetDatabase = vi.fn();
  const mockGetDatabasePath = vi.fn();
  const mockTestConnection = vi.fn();
  const mockGetStats = vi.fn();
  const mockClose = vi.fn();

  if (testDb) {
    mockGetDatabase.mockReturnValue(testDb);
  }

  mockGetDatabasePath.mockReturnValue('/mock/test/database.db');

  mockTestConnection.mockReturnValue({
    success: true,
    categories: 0,
    tags: 0,
    images: 0,
    imageTags: 0,
    message: '数据库连接成功',
    mode: 'SQLite'
  });

  mockGetStats.mockReturnValue({
    success: true,
    data: {
      categories: { count: 0 },
      images: { count: 0 },
      tags: { count: 0 },
      imageTags: { count: 0 }
    },
    message: '获取统计信息成功',
    mode: 'SQLite'
  });

  return {
    getDatabase: mockGetDatabase,
    getDatabasePath: mockGetDatabasePath,
    testConnection: mockTestConnection,
    getStats: mockGetStats,
    close: mockClose
  };
}

// 便捷函数
export function createTestDatabase(): TestDatabase {
  return TestDatabaseManager.getInstance().createTestDatabase();
}

export function cleanupTestDatabase(): void {
  TestDatabaseManager.getInstance().cleanupAll();
}