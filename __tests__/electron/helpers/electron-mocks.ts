import { vi } from 'vitest';
import path from 'path';
import fs from 'fs';

// Mock Electron app module
export const mockElectronApp = {
  getPath: vi.fn((name: string) => {
    switch (name) {
      case 'userData':
        return path.join(__dirname, '../../../test-temp/userData');
      case 'documents':
        return path.join(__dirname, '../../../test-temp/documents');
      case 'desktop':
        return path.join(__dirname, '../../../test-temp/desktop');
      case 'temp':
        return path.join(__dirname, '../../../test-temp/temp');
      default:
        return path.join(__dirname, '../../../test-temp');
    }
  }),
  quit: vi.fn(),
  exit: vi.fn(),
  isReady: vi.fn(() => true),
  whenReady: vi.fn(() => Promise.resolve()),
  on: vi.fn(),
  once: vi.fn(),
  emit: vi.fn()
};

// Mock Electron ipcMain module
export const mockIpcMain = {
  handle: vi.fn(),
  on: vi.fn(),
  once: vi.fn(),
  removeHandler: vi.fn(),
  removeAllListeners: vi.fn()
};

// Mock Electron dialog module
export const mockDialog = {
  showOpenDialog: vi.fn(),
  showSaveDialog: vi.fn(),
  showMessageBox: vi.fn(),
  showErrorBox: vi.fn()
};

// Mock Electron BrowserWindow class
export class MockBrowserWindow {
  static getAllWindows = vi.fn(() => []);
  static getFocusedWindow = vi.fn(() => null);

  constructor(options?: any) {
    this.webContents = new MockWebContents();
  }

  webContents: MockWebContents;
  loadFile = vi.fn();
  loadURL = vi.fn();
  show = vi.fn();
  hide = vi.fn();
  close = vi.fn();
  focus = vi.fn();
  minimize = vi.fn();
  maximize = vi.fn();
  unmaximize = vi.fn();
  isMaximized = vi.fn(() => false);
  setFullScreen = vi.fn();
  isFullScreen = vi.fn(() => false);
  on = vi.fn();
  once = vi.fn();
  emit = vi.fn();
  destroy = vi.fn();
  isDestroyed = vi.fn(() => false);
}

// Mock Electron WebContents class
export class MockWebContents {
  send = vi.fn();
  invoke = vi.fn();
  executeJavaScript = vi.fn();
  openDevTools = vi.fn();
  closeDevTools = vi.fn();
  isDevToolsOpened = vi.fn(() => false);
  on = vi.fn();
  once = vi.fn();
  emit = vi.fn();
}

// Mock Electron Menu class
export class MockMenu {
  static buildFromTemplate = vi.fn(() => new MockMenu());
  static setApplicationMenu = vi.fn();
  static getApplicationMenu = vi.fn(() => null);

  popup = vi.fn();
  closePopup = vi.fn();
  append = vi.fn();
  insert = vi.fn();
  items: any[] = [];
}

// Mock Electron MenuItem class
export class MockMenuItem {
  constructor(options: any) {
    Object.assign(this, options);
  }
}

// 创建完整的Electron模块mock
export function createElectronMocks() {
  return {
    app: mockElectronApp,
    ipcMain: mockIpcMain,
    dialog: mockDialog,
    BrowserWindow: MockBrowserWindow,
    Menu: MockMenu,
    MenuItem: MockMenuItem
  };
}

// 创建 electron mocks 实例在模块顶层
const electronMocks = createElectronMocks();

// Mock electron模块 - 必须在模块顶层
vi.mock('electron', () => electronMocks);

// 设置测试环境的Electron mocks
export function setupElectronMocks() {
  // 确保测试目录存在
  const testTempDir = path.join(__dirname, '../../../test-temp');
  if (!fs.existsSync(testTempDir)) {
    fs.mkdirSync(testTempDir, { recursive: true });
  }

  return electronMocks;
}

// 清理测试环境
export function cleanupElectronMocks() {
  const testTempDir = path.join(__dirname, '../../../test-temp');
  if (fs.existsSync(testTempDir)) {
    try {
      fs.rmSync(testTempDir, { recursive: true, force: true });
    } catch (error) {
      console.warn('清理测试临时目录失败:', error);
    }
  }
}