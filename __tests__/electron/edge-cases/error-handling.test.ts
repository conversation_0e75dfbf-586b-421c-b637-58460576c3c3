import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { TagService } from '../../../electron/services/TagService';
import { SettingsService } from '../../../electron/services/SettingsService';

describe('Edge Cases and Error Handling Tests', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let imageService: ImageService;
  let tagService: TagService;
  let settingsService: SettingsService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建真实的DatabaseManager实例
    dbManager = {
      getDatabase: () => testDb,
      getDatabasePath: vi.fn(() => '/mock/test/database.db'),
      testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
      getStats: vi.fn(() => ({ success: true, message: '统计成功', mode: 'SQLite', data: {} })),
      close: vi.fn()
    } as any;

    // 创建真实的服务实例
    categoryService = new CategoryService(dbManager);
    tagService = new TagService(dbManager);
    settingsService = new SettingsService();
    imageService = new ImageService(dbManager, settingsService);

    // 静默控制台输出
    console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('无效输入和恶意数据处理', () => {
    it('应该处理空字符串和null输入', async () => {
      // 系统对空字符串采用宽容处理
      const emptyCategory = await categoryService.createCategory({ name: '' });
      expect(emptyCategory.name).toBe('');
      
      // null输入应该被拒绝
      await expect(categoryService.createCategory({ name: null as any }))
        .rejects.toThrow();

      // 标签的空字符串处理
      const emptyTag = await tagService.createTag({ name: '' });
      expect(emptyTag.name).toBe('');

      await expect(tagService.createTag({ name: null as any }))
        .rejects.toThrow();
    });

    it('应该处理超长字符串输入', async () => {
      const longString = 'x'.repeat(1000); // 减少长度以便测试
      
      // 系统对超长字符串采用宽容处理，但验证能否正常存储和检索
      const longCategory = await categoryService.createCategory({ 
        name: longString,
        description: longString 
      });
      
      expect(longCategory.name).toBe(longString);
      expect(longCategory.description).toBe(longString);

      // 验证能否正常检索
      const retrieved = await categoryService.getCategoryById(longCategory.id);
      expect(retrieved?.name).toBe(longString);
      
      // 超长标签测试
      const longTag = await tagService.createTag({ name: longString });
      expect(longTag.name).toBe(longString);
    });

    it('应该处理特殊字符和Unicode输入', async () => {
      const specialChars = [
        '../../etc/passwd',
        '<script>alert("xss")</script>',
        'Robert\'); DROP TABLE categories;--',
        '中文测试名称',
        '🔥💯🚀测试',
        '\x00\x01\x02',
        '\\n\\r\\t',
        String.fromCharCode(0),
      ];

      for (const testInput of specialChars) {
        try {
          if (testInput.trim() && !testInput.includes('\x00')) {
            // 允许正常的Unicode字符，但应该正确处理
            const category = await categoryService.createCategory({ name: testInput });
            expect(category.name).toBe(testInput);
          }
        } catch (error) {
          // 对于危险字符，应该抛出错误或安全处理
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('应该处理无效的UUID格式', async () => {
      const invalidUUIDs = [
        'not-a-uuid',
        '12345',
        '',
        'xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx',
        '123e4567-e89b-12d3-a456-42661417400',  // 少一位
        '123e4567-e89b-12d3-a456-4266141740000', // 多一位
      ];

      for (const invalidId of invalidUUIDs) {
        // 系统对无效UUID返回null而不是抛出错误
        const categoryResult = await categoryService.getCategoryById(invalidId);
        expect(categoryResult).toBeNull();
        
        const tagResult = await tagService.getTagById(invalidId);
        expect(tagResult).toBeNull();
      }
      
      // null和undefined返回null而不是抛出错误
      const nullResult = await categoryService.getCategoryById(null as any);
      expect(nullResult).toBeNull();
      
      const undefinedResult = await categoryService.getCategoryById(undefined as any);
      expect(undefinedResult).toBeNull();
    });

    it('应该处理无效的数字输入', async () => {
      // 系统对无效数字采用宽容处理
      
      // 负数被处理为0或返回空结果
      const negativeResult = await categoryService.getCategories(-1, 10);
      expect(Array.isArray(negativeResult)).toBe(true);
      
      const negativeLimit = await categoryService.getCategories(0, -1);
      expect(Array.isArray(negativeLimit)).toBe(true);
      
      // 测试极大数值的处理
      const largeOffset = await categoryService.getCategories(Number.MAX_SAFE_INTEGER, 10);
      expect(Array.isArray(largeOffset)).toBe(true);
      expect(largeOffset).toHaveLength(0); // 应该返回空数组
      
      // NaN和Infinity应该被拒绝或处理为合理值
      try {
        const nanResult = await categoryService.getCategories(NaN, 10);
        expect(Array.isArray(nanResult)).toBe(true);
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
      }
    });
  });

  describe('大量数据处理测试', () => {
    it('应该处理大量分类创建和查询', async () => {
      const startTime = Date.now();
      const categoryCount = 1000;
      
      // 创建大量分类
      const categories = [];
      for (let i = 0; i < categoryCount; i++) {
        const category = await categoryService.createCategory({
          name: `Bulk Category ${i}`,
          description: `Bulk description ${i}`
        });
        categories.push(category);
        
        // 每100个检查一次性能
        if (i % 100 === 0 && i > 0) {
          const elapsed = Date.now() - startTime;
          console.log(`创建了 ${i} 个分类，耗时: ${elapsed}ms`);
          
          // 如果耗时超过30秒，停止测试
          if (elapsed > 30000) {
            console.warn('大量数据测试超时，提前终止');
            break;
          }
        }
      }

      const finalCount = categories.length;
      expect(finalCount).toBeGreaterThan(100); // 至少应该创建100个

      // 测试分页查询性能
      const pageStartTime = Date.now();
      const firstPage = await categoryService.getCategories(0, 50);
      const lastPage = await categoryService.getCategories(finalCount - 50, 50);
      const pageEndTime = Date.now();

      expect(firstPage).toHaveLength(50);
      expect(pageEndTime - pageStartTime).toBeLessThan(1000); // 分页查询应该在1秒内完成

      console.log(`大量数据测试完成: ${finalCount} 个分类，总耗时: ${Date.now() - startTime}ms`);
    });

    it('应该处理大量标签的搜索性能', async () => {
      const tagCount = 500;
      const tags = [];

      // 创建大量标签
      for (let i = 0; i < tagCount; i++) {
        const tag = await tagService.createTag({
          name: `BulkTag${i}`
        });
        tags.push(tag);
      }

      // 测试搜索性能
      const searchStartTime = Date.now();
      
      const searchResults1 = await tagService.searchTags('BulkTag1');
      const searchResults2 = await tagService.searchTags('Bulk');
      const searchResults3 = await tagService.searchTags('Tag');
      
      const searchEndTime = Date.now();

      expect(searchResults1.length).toBeGreaterThan(0);
      expect(searchResults2.length).toBeGreaterThan(100);
      expect(searchResults3.length).toBeGreaterThan(100);
      
      // 搜索应该在合理时间内完成
      expect(searchEndTime - searchStartTime).toBeLessThan(5000);

      console.log(`大量标签搜索测试: ${tagCount} 个标签，搜索耗时: ${searchEndTime - searchStartTime}ms`);
    });

    it('应该处理复杂的多对多关系大量数据', async () => {
      // 创建多个分类
      const categories = await Promise.all([
        categoryService.createCategory({ name: 'Bulk Category A' }),
        categoryService.createCategory({ name: 'Bulk Category B' }),
        categoryService.createCategory({ name: 'Bulk Category C' })
      ]);

      // 创建多个标签
      const tags = [];
      for (let i = 0; i < 50; i++) {
        const tag = await tagService.createTag({ name: `BulkRelTag${i}` });
        tags.push(tag);
      }

      // 为每个分类创建图片
      const images = [];
      for (const category of categories) {
        for (let i = 0; i < 10; i++) {
          const image = await imageService.uploadImage(
            category.id,
            Buffer.from(`bulk image data ${category.id}-${i}`),
            `bulk-${category.id}-${i}.jpg`,
            'image/jpeg'
          );
          images.push(image);
        }
      }

      // 建立大量标签关联
      const relationStartTime = Date.now();
      
      for (const image of images) {
        // 每个图片关联5-10个随机标签
        const tagCount = 5 + Math.floor(Math.random() * 6);
        const randomTags = tags.slice(0, tagCount);
        
        for (const tag of randomTags) {
          await tagService.addTagToImage(image.id, tag.id);
        }
      }

      const relationEndTime = Date.now();

      // 测试复杂查询性能
      const queryStartTime = Date.now();
      
      // 查找有最多标签的图片
      const allImageTags = await Promise.all(
        images.map(img => tagService.getTagsForImage(img.id))
      );
      
      // 搜索包含特定标签的图片
      const searchResults = await tagService.searchImagesByTags(['BulkRelTag0', 'BulkRelTag1']);
      
      const queryEndTime = Date.now();

      expect(allImageTags.length).toBe(images.length);
      expect(searchResults.length).toBeGreaterThan(0);
      
      // 操作应该在合理时间内完成
      expect(relationEndTime - relationStartTime).toBeLessThan(10000);
      expect(queryEndTime - queryStartTime).toBeLessThan(5000);

      console.log(`复杂关系测试: 关联耗时 ${relationEndTime - relationStartTime}ms, 查询耗时 ${queryEndTime - queryStartTime}ms`);
    });
  });

  describe('并发操作和竞态条件测试', () => {
    it('应该处理并发创建相同名称的分类', async () => {
      const sameName = 'Concurrent Category';
      
      // 并发创建多个相同名称的分类
      const promises = Array.from({ length: 10 }, () =>
        categoryService.createCategory({ name: sameName })
      );

      const results = await Promise.allSettled(promises);
      
      // 应该只有一个成功，其他的应该失败或者都成功（取决于实现）
      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');
      
      // 至少应该有一个成功
      expect(successful.length).toBeGreaterThan(0);
      
      console.log(`并发创建测试: ${successful.length} 成功, ${failed.length} 失败`);
    });

    it('应该处理并发标签关联操作', async () => {
      const category = await categoryService.createCategory({ name: 'Concurrent Test Cat' });
      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('concurrent test'),
        'concurrent.jpg',
        'image/jpeg'
      );

      // 创建多个标签
      const tags = await Promise.all(
        Array.from({ length: 20 }, (_, i) =>
          tagService.createTag({ name: `ConcurrentTag${i}` })
        )
      );

      // 并发添加所有标签到同一图片
      const addPromises = tags.map(tag =>
        tagService.addTagToImage(image.id, tag.id)
      );

      const addResults = await Promise.allSettled(addPromises);
      const successfulAdds = addResults.filter(r => r.status === 'fulfilled');

      expect(successfulAdds.length).toBeGreaterThan(15); // 大部分应该成功

      // 验证最终状态
      const finalTags = await tagService.getTagsForImage(image.id);
      expect(finalTags.length).toBe(successfulAdds.length);
    });

    it('应该处理并发删除操作', async () => {
      // 创建测试数据
      const categories = await Promise.all(
        Array.from({ length: 5 }, (_, i) =>
          categoryService.createCategory({ name: `DeleteTest${i}` })
        )
      );

      // 并发删除所有分类
      const deletePromises = categories.map(cat =>
        categoryService.deleteCategory(cat.id)
      );

      const deleteResults = await Promise.allSettled(deletePromises);
      const successfulDeletes = deleteResults.filter(r => r.status === 'fulfilled');

      // 所有删除操作都应该成功
      expect(successfulDeletes.length).toBe(categories.length);

      // 验证所有分类都被删除
      for (const category of categories) {
        const result = await categoryService.getCategoryById(category.id);
        expect(result).toBeNull();
      }
    });
  });

  describe('内存限制和资源清理测试', () => {
    it('应该正确管理大量数据的内存使用', async () => {
      const initialMemory = process.memoryUsage();
      
      // 创建大量数据
      const categories = [];
      const images = [];
      
      for (let i = 0; i < 100; i++) {
        const category = await categoryService.createCategory({
          name: `Memory Test Category ${i}`,
          description: `Long description for memory test category ${i}. `.repeat(10)
        });
        categories.push(category);

        // 每个分类创建多个图片
        for (let j = 0; j < 5; j++) {
          const image = await imageService.uploadImage(
            category.id,
            Buffer.alloc(1024, `image-${i}-${j}`), // 1KB的测试数据
            `memory-test-${i}-${j}.jpg`,
            'image/jpeg'
          );
          images.push(image);
        }
      }

      const peakMemory = process.memoryUsage();
      
      // 清理所有数据
      for (const image of images) {
        await imageService.deleteImage(image.id);
      }
      
      for (const category of categories) {
        await categoryService.deleteCategory(category.id);
      }

      // 强制垃圾回收（如果可用）
      if (global.gc) {
        global.gc();
      }

      const finalMemory = process.memoryUsage();

      // 验证内存使用情况
      const memoryIncrease = peakMemory.heapUsed - initialMemory.heapUsed;
      const memoryAfterCleanup = finalMemory.heapUsed - initialMemory.heapUsed;
      
      console.log(`内存测试: 初始 ${Math.round(initialMemory.heapUsed / 1024 / 1024)}MB, 峰值 ${Math.round(peakMemory.heapUsed / 1024 / 1024)}MB, 清理后 ${Math.round(finalMemory.heapUsed / 1024 / 1024)}MB`);
      
      // 内存增长应该是合理的
      expect(memoryIncrease).toBeLessThan(100 * 1024 * 1024); // 不超过100MB
      
      // 清理后内存可能不会立即减少（V8垃圾回收的延迟性）
      // 验证内存使用在合理范围内即可
      expect(Math.abs(memoryAfterCleanup)).toBeLessThan(Math.abs(memoryIncrease) * 10 + 50 * 1024 * 1024); // 允许内存清理延迟
    });

    it('应该正确处理数据库连接资源', async () => {
      // 执行大量数据库操作
      const operations = [];
      
      for (let i = 0; i < 100; i++) {
        operations.push(
          categoryService.createCategory({ name: `Resource Test ${i}` })
            .then(cat => categoryService.getCategoryById(cat.id))
            .then(cat => categoryService.updateCategory(cat!.id, { description: `Updated ${i}` }))
        );
      }

      const results = await Promise.allSettled(operations);
      const successful = results.filter(r => r.status === 'fulfilled');
      
      expect(successful.length).toBeGreaterThan(90); // 大部分操作应该成功

      // 测试连接状态
      const connectionTest = dbManager.testConnection();
      expect(connectionTest.success).toBe(true);
    });
  });

  describe('异常情况恢复测试', () => {
    it('应该处理数据库操作中断后的恢复', async () => {
      // 创建一些初始数据
      const category = await categoryService.createCategory({ name: 'Recovery Test' });
      
      // 模拟操作中断（通过抛出错误）
      const originalPrepare = testDb.prepare;
      let errorCount = 0;
      
      testDb.prepare = vi.fn().mockImplementation((sql: string) => {
        if (sql.includes('INSERT') && errorCount < 3) {
          errorCount++;
          throw new Error('模拟数据库错误');
        }
        return originalPrepare.call(testDb, sql);
      });

      // 尝试创建分类，前几次应该失败
      for (let i = 0; i < 3; i++) {
        await expect(
          categoryService.createCategory({ name: `Failed ${i}` })
        ).rejects.toThrow('模拟数据库错误');
      }

      // 恢复正常操作
      testDb.prepare = originalPrepare;

      // 验证系统可以恢复正常
      const recoveryCategory = await categoryService.createCategory({ name: 'Recovery Success' });
      expect(recoveryCategory.id).toBeDefined();

      // 验证原有数据仍然存在
      const originalCategory = await categoryService.getCategoryById(category.id);
      expect(originalCategory).not.toBeNull();
    });

    it('应该处理部分失败的批量操作', async () => {
      const testData = [
        { name: 'Valid Category 1' },
        { name: '' }, // 无效数据
        { name: 'Valid Category 2' },
        { name: null as any }, // 无效数据
        { name: 'Valid Category 3' },
      ];

      const results = await Promise.allSettled(
        testData.map(data => categoryService.createCategory(data))
      );

      const successful = results.filter(r => r.status === 'fulfilled');
      const failed = results.filter(r => r.status === 'rejected');

      expect(successful.length).toBeGreaterThan(2); // 至少有2个有效的成功
      expect(failed.length).toBeGreaterThan(0); // 至少有一些失败

      // 验证成功的数据确实被创建
      const allCategories = await categoryService.getCategories();
      const validNames = successful
        .map(r => (r as PromiseFulfilledResult<any>).value.name)
        .sort();
      
      // 系统允许空字符串，所以包含它
      expect(validNames).toEqual(['', 'Valid Category 1', 'Valid Category 2', 'Valid Category 3']);
    });
  });

  describe('安全性和输入验证测试', () => {
    it('应该处理潜在危险的路径字符', async () => {
      const maliciousPaths = [
        '../../../etc/passwd',
        '..\\..\\..\\windows\\system32\\config\\sam',
        '/etc/shadow',
        'C:\\Windows\\System32\\config\\SAM',
        '....//....//....//etc/passwd',
        '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd'
      ];

      // 系统允许这些字符串作为名称，但验证它们被安全存储
      for (const maliciousPath of maliciousPaths) {
        const category = await categoryService.createCategory({ name: maliciousPath });
        expect(category.name).toBe(maliciousPath);
        
        // 验证可以安全检索
        const retrieved = await categoryService.getCategoryById(category.id);
        expect(retrieved?.name).toBe(maliciousPath);
      }
    });

    it('应该正确处理JSON注入尝试', async () => {
      const jsonPayloads = [
        '{"malicious": "payload"}',
        '[{"evil": true}]',
        '\\u0000\\u0001\\u0002',
        '\\"escape attempt\\"',
        "'; DROP TABLE categories; --"
      ];

      for (const payload of jsonPayloads) {
        try {
          const category = await categoryService.createCategory({ 
            name: 'JSON Test',
            description: payload 
          });
          
          // 如果创建成功，验证数据被正确转义
          expect(category.description).toBe(payload);
        } catch (error) {
          // 如果失败，应该是由于输入验证
          expect(error).toBeInstanceOf(Error);
        }
      }
    });

    it('应该处理极端查询参数', async () => {
      // 测试系统对极端参数的处理
      const negativeResult = await categoryService.getCategories(-1, 10);
      expect(Array.isArray(negativeResult)).toBe(true);
      
      const negativeLimitResult = await categoryService.getCategories(0, -1);
      expect(Array.isArray(negativeLimitResult)).toBe(true);
      
      const largePageResult = await categoryService.getCategories(0, 10000);
      expect(Array.isArray(largePageResult)).toBe(true);
      
      const extremeOffsetResult = await categoryService.getCategories(Number.MAX_SAFE_INTEGER, 10);
      expect(Array.isArray(extremeOffsetResult)).toBe(true);
      expect(extremeOffsetResult).toHaveLength(0); // 超出范围应该返回空
    });
  });
});