import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { TagService } from '../../../electron/services/TagService';
import { SettingsService } from '../../../electron/services/SettingsService';

describe('Database Integration Tests', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let imageService: ImageService;
  let tagService: TagService;
  let settingsService: SettingsService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用集成测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建真实的DatabaseManager实例
    dbManager = {
      getDatabase: () => testDb,
      getDatabasePath: vi.fn(() => '/mock/test/database.db'),
      testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
      getStats: vi.fn(() => ({ success: true, message: '统计成功', mode: 'SQLite', data: {} })),
      close: vi.fn()
    } as any;

    // 创建真实的服务实例
    categoryService = new CategoryService(dbManager);
    imageService = new ImageService(dbManager);
    tagService = new TagService(dbManager);
    settingsService = new SettingsService();

    // 静默控制台输出
    console.log = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('跨服务数据一致性测试', () => {
    it('应该保持分类、图片和标签之间的关联一致性', async () => {
      // 1. 创建分类
      const category = await categoryService.createCategory({
        name: 'Integration Test Category',
        description: '集成测试分类'
      });

      // 2. 为分类创建图片
      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('fake image data'),
        'integration-test.jpg',
        'image/jpeg'
      );

      // 3. 创建标签并关联到图片
      const tag = await tagService.createTag({ name: 'Integration Tag' });
      await tagService.addTagToImage(image.id, tag.id);

      // 4. 验证数据一致性
      const categoryWithImages = await categoryService.getCategoryWithImages(category.id);
      expect(categoryWithImages).toBeDefined();
      expect(categoryWithImages!.images).toHaveLength(1);
      expect(categoryWithImages!.images[0].id).toBe(image.id);

      const imageTags = await tagService.getTagsForImage(image.id);
      expect(imageTags).toHaveLength(1);
      expect(imageTags[0].id).toBe(tag.id);

      // 5. 通过标签搜索图片
      const imagesByTag = await tagService.searchImagesByTags([tag.name]);
      expect(imagesByTag).toContain(image.id);
    });

    it('应该正确处理级联删除操作', async () => {
      // 1. 创建完整的数据链
      const category = await categoryService.createCategory({ name: 'Cascade Test Category' });
      
      const image1 = await imageService.uploadImage(
        category.id,
        Buffer.from('image1 data'),
        'cascade-test-1.jpg',
        'image/jpeg'
      );
      
      const image2 = await imageService.uploadImage(
        category.id,
        Buffer.from('image2 data'),
        'cascade-test-2.jpg',
        'image/jpeg'
      );

      const tag = await tagService.createTag({ name: 'Cascade Tag' });

      await tagService.addTagToImage(image1.id, tag.id);
      await tagService.addTagToImage(image2.id, tag.id);

      // 2. 删除分类应该级联删除图片和图片标签关联
      await categoryService.deleteCategory(category.id);

      // 3. 验证级联删除效果
      const deletedCategory = await categoryService.getCategoryById(category.id);
      expect(deletedCategory).toBeNull();

      const deletedImage1 = await imageService.getImageById(image1.id);
      expect(deletedImage1).toBeNull();

      const deletedImage2 = await imageService.getImageById(image2.id);
      expect(deletedImage2).toBeNull();

      // 标签本身应该保留（只删除关联）
      const remainingTag = await tagService.getTagById(tag.id);
      expect(remainingTag).toBeDefined();

      // 图片标签关联应该被删除
      const image1Tags = await tagService.getTagsForImage(image1.id);
      expect(image1Tags).toHaveLength(0);

      const image2Tags = await tagService.getTagsForImage(image2.id);
      expect(image2Tags).toHaveLength(0);
    });

    it('应该正确处理复杂的多对多关系', async () => {
      // 创建多个分类、图片和标签的复杂关系
      const categories = await Promise.all([
        categoryService.createCategory({ name: 'Category A' }),
        categoryService.createCategory({ name: 'Category B' }),
        categoryService.createCategory({ name: 'Category C' })
      ]);

      const images = await Promise.all([
        imageService.uploadImage(categories[0].id, Buffer.from('image a1'), 'a1.jpg', 'image/jpeg'),
        imageService.uploadImage(categories[0].id, Buffer.from('image a2'), 'a2.jpg', 'image/jpeg'),
        imageService.uploadImage(categories[1].id, Buffer.from('image b1'), 'b1.jpg', 'image/jpeg'),
        imageService.uploadImage(categories[2].id, Buffer.from('image c1'), 'c1.jpg', 'image/jpeg')
      ]);

      const tags = await Promise.all([
        tagService.createTag({ name: 'Tag X' }),
        tagService.createTag({ name: 'Tag Y' }),
        tagService.createTag({ name: 'Tag Z' })
      ]);

      // 建立复杂的标签关联
      await tagService.addTagToImage(images[0].id, tags[0].id); // A1 -> X
      await tagService.addTagToImage(images[0].id, tags[1].id); // A1 -> Y
      await tagService.addTagToImage(images[1].id, tags[0].id); // A2 -> X
      await tagService.addTagToImage(images[2].id, tags[1].id); // B1 -> Y
      await tagService.addTagToImage(images[2].id, tags[2].id); // B1 -> Z
      await tagService.addTagToImage(images[3].id, tags[2].id); // C1 -> Z

      // 验证多标签搜索
      const imagesWithXY = await tagService.searchImagesByTags(['Tag X', 'Tag Y']);
      expect(imagesWithXY).toContain(images[0].id); // A1 同时有 X 和 Y

      const imagesWithZ = await tagService.searchImagesByTags(['Tag Z']);
      expect(imagesWithZ).toHaveLength(2);
      expect(imagesWithZ).toContain(images[2].id); // B1
      expect(imagesWithZ).toContain(images[3].id); // C1

      // 验证标签到图片的反向查询
      const tagXImages = await tagService.searchImagesByTags(['Tag X']);
      expect(tagXImages).toHaveLength(2);
      expect(tagXImages).toContain(images[0].id); // A1
      expect(tagXImages).toContain(images[1].id); // A2
    });
  });

  describe('复杂查询操作测试', () => {
    it('应该支持分页查询的一致性', async () => {
      // 创建大量分类数据
      const categories = [];
      for (let i = 1; i <= 15; i++) {
        const category = await categoryService.createCategory(
          TestDataGenerator.createCategory({ 
            name: `Pagination Category ${i.toString().padStart(2, '0')}` 
          })
        );
        categories.push(category);
      }

      // 测试分页查询
      const page1 = await categoryService.getCategories(0, 5);
      const page2 = await categoryService.getCategories(5, 5);
      const page3 = await categoryService.getCategories(10, 5);

      expect(page1).toHaveLength(5);
      expect(page2).toHaveLength(5);
      expect(page3).toHaveLength(5);

      // 验证分页数据不重复
      const allIds = [...page1, ...page2, ...page3].map(cat => cat.id);
      const uniqueIds = new Set(allIds);
      expect(uniqueIds.size).toBe(15);

      // 验证排序一致性（按创建时间降序）
      const allCategories = await categoryService.getCategories(0, 20);
      for (let i = 0; i < allCategories.length - 1; i++) {
        const current = new Date(allCategories[i].created_at).getTime();
        const next = new Date(allCategories[i + 1].created_at).getTime();
        expect(current).toBeGreaterThanOrEqual(next);
      }
    });

    it('应该处理并发操作的数据一致性', async () => {
      const category = await categoryService.createCategory({ name: 'Concurrent Test Category' });

      // 并发创建多个图片
      const concurrentImages = await Promise.all([
        imageService.uploadImage(category.id, Buffer.from('img1'), 'concurrent1.jpg', 'image/jpeg'),
        imageService.uploadImage(category.id, Buffer.from('img2'), 'concurrent2.jpg', 'image/jpeg'),
        imageService.uploadImage(category.id, Buffer.from('img3'), 'concurrent3.jpg', 'image/jpeg'),
        imageService.uploadImage(category.id, Buffer.from('img4'), 'concurrent4.jpg', 'image/jpeg'),
        imageService.uploadImage(category.id, Buffer.from('img5'), 'concurrent5.jpg', 'image/jpeg')
      ]);

      // 验证所有图片都成功创建
      expect(concurrentImages).toHaveLength(5);
      concurrentImages.forEach(image => {
        expect(image.id).toBeDefined();
        expect(image.category_id).toBe(category.id);
      });

      // 验证分类中包含所有图片
      const categoryWithImages = await categoryService.getCategoryWithImages(category.id);
      expect(categoryWithImages!.images).toHaveLength(5);

      // 并发创建标签并关联到图片
      const tag = await tagService.createTag({ name: 'Concurrent Tag' });

      await Promise.all(
        concurrentImages.map(image => 
          tagService.addTagToImage(image.id, tag.id)
        )
      );

      // 验证所有关联都成功
      const imagesByTag = await tagService.searchImagesByTags([tag.name]);
      expect(imagesByTag).toHaveLength(5);
    });
  });

  describe('数据迁移和完整性测试', () => {
    it('应该保持外键约束的完整性', async () => {
      // 测试外键约束
      const category = await categoryService.createCategory({ name: 'FK Test Category' });

      // 尝试为不存在的分类创建图片（应该失败）
      await expect(
        imageService.uploadImage(
          'non-existent-category-id',
          Buffer.from('invalid image'),
          'invalid.jpg',
          'image/jpeg'
        )
      ).rejects.toThrow();

      // 尝试为不存在的图片添加标签（应该失败）
      const tag = await tagService.createTag({ name: 'FK Test Tag' });

      await expect(
        tagService.addTagToImage('non-existent-image-id', tag.id)
      ).rejects.toThrow();

      await expect(
        tagService.addTagToImage(category.id, 'non-existent-tag-id')
      ).rejects.toThrow();
    });

    it('应该正确处理数据库统计信息', async () => {
      const initialStats = dbManager.getStats();
      
      // 创建测试数据
      const category = await categoryService.createCategory({ name: 'Stats Test Category' });

      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('stats test image'),
        'stats-test.jpg',
        'image/jpeg'
      );

      const tag = await tagService.createTag({ name: 'Stats Test Tag' });

      await tagService.addTagToImage(image.id, tag.id);

      // 验证统计信息更新
      const updatedStats = dbManager.getStats();
      expect(updatedStats.success).toBe(true);
      
      // 验证计数增加（注意：可能包含初始数据）
      const categories = await categoryService.getCategories();
      const tags = await tagService.getAllTags();
      
      expect(categories.length).toBeGreaterThan(0);
      expect(tags.length).toBeGreaterThan(0);
    });

    it('应该处理事务性操作', async () => {
      // 创建基础数据
      const category = await categoryService.createCategory({ name: 'Transaction Test Category' });

      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('transaction test image'),
        'transaction-test.jpg',
        'image/jpeg'
      );

      // 创建多个标签并进行批量关联
      const tags = await Promise.all([
        tagService.createTag({ name: 'Batch Tag 1' }),
        tagService.createTag({ name: 'Batch Tag 2' }),
        tagService.createTag({ name: 'Batch Tag 3' })
      ]);

      // 批量添加标签关联
      await Promise.all(
        tags.map(tag => tagService.addTagToImage(image.id, tag.id))
      );

      // 验证所有关联都成功
      const imageTags = await tagService.getTagsForImage(image.id);
      expect(imageTags).toHaveLength(3);

      // 批量移除标签关联
      await Promise.all(
        tags.map(tag => tagService.removeTagFromImage(image.id, tag.id))
      );

      // 验证所有关联都被移除
      const remainingTags = await tagService.getTagsForImage(image.id);
      expect(remainingTags).toHaveLength(0);
    });
  });

  describe('性能和资源管理测试', () => {
    it('应该高效处理大量数据查询', async () => {
      // 创建大量测试数据
      const category = await categoryService.createCategory({ name: 'Performance Test Category' });

      // 创建大量图片（模拟但不过载）
      const images = [];
      for (let i = 1; i <= 50; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.from(`performance test image ${i}`),
          `perf-test-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);
      }

      // 创建标签
      const tag = await tagService.createTag({ name: 'Performance Tag' });

      // 为所有图片添加标签
      await Promise.all(
        images.map(image => tagService.addTagToImage(image.id, tag.id))
      );

      // 测试大量数据的查询性能
      const startTime = Date.now();
      
      const categoryWithImages = await categoryService.getCategoryWithImages(category.id);
      const imagesByTag = await tagService.searchImagesByTags([tag.name]);
      const allTags = await tagService.getAllTags();
      
      const endTime = Date.now();
      const queryTime = endTime - startTime;

      // 验证查询结果正确性
      expect(categoryWithImages!.images).toHaveLength(50);
      expect(imagesByTag).toHaveLength(50);
      expect(allTags.length).toBeGreaterThan(0);

      // 性能应该合理（不超过2秒）
      expect(queryTime).toBeLessThan(2000);

      console.log(`大量数据查询耗时: ${queryTime}ms`);
    });

    it('应该正确管理数据库连接资源', async () => {
      // 测试连接状态
      const connectionTest = dbManager.testConnection();
      expect(connectionTest.success).toBe(true);

      // 进行多次操作验证连接稳定性
      for (let i = 0; i < 10; i++) {
        const category = await categoryService.createCategory(
          TestDataGenerator.createCategory({ name: `Connection Test ${i}` })
        );
        expect(category.id).toBeDefined();
      }

      // 验证最终连接状态
      const finalConnectionTest = dbManager.testConnection();
      expect(finalConnectionTest.success).toBe(true);
    });
  });
});