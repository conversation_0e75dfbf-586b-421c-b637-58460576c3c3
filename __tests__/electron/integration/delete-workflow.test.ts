import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ImageService } from '../../../electron/services/ImageService';
import { CategoryService } from '../../../electron/services/CategoryService';
import { DatabaseManager } from '../../../electron/database';
import { SettingsService } from '../../../electron/services/SettingsService';
import { TestDataGenerator } from '../helpers/test-data-generator';
import { createTestDatabase, cleanupTestDatabase, mockDatabaseManager } from '../helpers/test-database';
import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';

// Mock Electron modules
vi.mock('electron', async () => {
  const { createElectronMocks } = await import('../helpers/electron-mocks');
  return createElectronMocks();
});

// Mock fs operations for testing
vi.mock('fs');
const mockFs = vi.mocked(fs);

describe('删除功能集成测试', () => {
  let imageService: ImageService;
  let categoryService: CategoryService;
  let dbManager: DatabaseManager;
  let settingsService: SettingsService;
  let testDb: Database.Database;
  let tempStorageDir: string;

  beforeEach(async () => {
    // 设置测试数据库
    const testDbSetup = createTestDatabase();
    testDb = testDbSetup.db;
    dbManager = mockDatabaseManager(testDb) as any;
    
    // 设置临时存储目录
    tempStorageDir = path.join(__dirname, '../../../test-temp/delete-workflow-test');
    
    // Mock SettingsService
    settingsService = {
      getStoragePath: vi.fn(() => tempStorageDir),
      usesCategoryFolders: vi.fn(() => true),
      getSettings: vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'local' as 'local',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn(() => null),
      updateOSSConfig: vi.fn(() => true),
      clearOSSConfig: vi.fn(() => true),
      getStorageType: vi.fn(() => 'local')
    } as any;

    // 创建服务实例
    imageService = new ImageService(dbManager, settingsService);
    categoryService = new CategoryService(dbManager);
    
    // 设置服务间依赖
    categoryService.setImageService(imageService);

    // Mock fs operations
    mockFs.existsSync.mockReturnValue(true);
    mockFs.unlinkSync.mockImplementation(() => {});
    
    console.log('✅ 删除工作流测试环境初始化完成');
  });

  afterEach(async () => {
    cleanupTestDatabase();
    vi.clearAllMocks();
  });

  describe('完整的删除工作流程', () => {
    it('应该完成从上传到删除的完整流程', async () => {
      // 1. 创建分类
      const categoryData = {
        name: '测试分类',
        description: '用于测试删除工作流程的分类'
      };
      const category = await categoryService.createCategory(categoryData);
      expect(category.id).toBeDefined();

      // 2. 模拟上传图片
      const imageData = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        imageData.id, imageData.category_id, imageData.title, imageData.original_filename, imageData.stored_filename,
        imageData.relative_file_path, imageData.relative_thumbnail_path, imageData.mime_type, imageData.size_bytes,
        imageData.description, imageData.created_at, imageData.updated_at, 
        JSON.stringify(imageData.file_metadata), imageData.image_url, imageData.thumbnail_url
      );

      // 3. 验证图片存在
      const uploadedImage = await imageService.getImageById(imageData.id);
      expect(uploadedImage).toBeDefined();
      expect(uploadedImage!.title).toBe(imageData.title);

      // 4. 删除图片
      const deleteResult = await imageService.deleteImage(imageData.id);
      expect(deleteResult.success).toBe(true);
      expect(deleteResult.storageDeleted).toBe(true);
      expect(deleteResult.databaseDeleted).toBe(true);

      // 5. 验证图片已被删除
      const deletedImage = await imageService.getImageById(imageData.id);
      expect(deletedImage).toBeNull();

      // 6. 验证文件删除操作被调用
      expect(mockFs.unlinkSync).toHaveBeenCalledTimes(2); // 原图和缩略图
    });

    it('应该正确处理分类删除时的图片批量删除', async () => {
      // 1. 创建分类
      const categoryData = {
        name: '批量删除测试分类',
        description: '用于测试分类删除时的图片批量删除'
      };
      const category = await categoryService.createCategory(categoryData);

      // 2. 创建多张图片
      const images = [
        TestDataGenerator.createImage(category.id),
        TestDataGenerator.createImage(category.id),
        TestDataGenerator.createImage(category.id)
      ];

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const image of images) {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
          image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
          image.description, image.created_at, image.updated_at, 
          JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
        );
      }

      // 3. 验证图片存在
      for (const image of images) {
        const existingImage = await imageService.getImageById(image.id);
        expect(existingImage).toBeDefined();
      }

      // 4. 删除分类（应该同时删除所有图片）
      const deleteResult = await categoryService.deleteCategory(category.id);
      expect(deleteResult.success).toBe(true);
      expect(deleteResult.details?.categoryDeleted).toBe(true);
      expect(deleteResult.details?.imagesDeleted.totalCount).toBe(3);
      expect(deleteResult.details?.imagesDeleted.successCount).toBe(3);

      // 5. 验证分类已被删除
      const deletedCategory = await categoryService.getCategoryById(category.id);
      expect(deletedCategory).toBeNull();

      // 6. 验证所有图片已被删除
      for (const image of images) {
        const deletedImage = await imageService.getImageById(image.id);
        expect(deletedImage).toBeNull();
      }

      // 7. 验证文件删除操作被调用（3张图片 × 2个文件）
      expect(mockFs.unlinkSync).toHaveBeenCalledTimes(6);
    });
  });

  describe('存储类型切换时的删除行为', () => {
    it('应该在本地存储模式下正确删除', async () => {
      // 确保是本地存储模式
      settingsService.getSettings = vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'local' as 'local',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));

      // 重新创建ImageService
      imageService = new ImageService(dbManager, settingsService);

      // 创建测试数据
      const category = await categoryService.createCategory({ name: '本地存储测试' });
      const imageData = TestDataGenerator.createImage(category.id);
      
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        imageData.id, imageData.category_id, imageData.title, imageData.original_filename, imageData.stored_filename,
        imageData.relative_file_path, imageData.relative_thumbnail_path, imageData.mime_type, imageData.size_bytes,
        imageData.description, imageData.created_at, imageData.updated_at, 
        JSON.stringify(imageData.file_metadata), imageData.image_url, imageData.thumbnail_url
      );

      const result = await imageService.deleteImage(imageData.id);

      expect(result.success).toBe(true);
      expect(result.details?.storageType).toBe('local');
      expect(mockFs.unlinkSync).toHaveBeenCalled();
    });

    it('应该在OSS存储模式下正确删除', async () => {
      // 设置OSS存储模式
      settingsService.getSettings = vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'oss' as 'oss',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));
      settingsService.getStorageType = vi.fn(() => 'oss' as 'oss');

      // 重新创建ImageService
      imageService = new ImageService(dbManager, settingsService);

      // Mock OSS服务
      const mockOSSService = {
        isConfigured: vi.fn(() => true),
        deleteFile: vi.fn(() => Promise.resolve({ success: true }))
      };
      (imageService as any).ossService = mockOSSService;

      // 创建测试数据
      const category = await categoryService.createCategory({ name: 'OSS存储测试' });
      const imageData = TestDataGenerator.createImage(category.id);
      
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        imageData.id, imageData.category_id, imageData.title, imageData.original_filename, imageData.stored_filename,
        imageData.relative_file_path, imageData.relative_thumbnail_path, imageData.mime_type, imageData.size_bytes,
        imageData.description, imageData.created_at, imageData.updated_at, 
        JSON.stringify(imageData.file_metadata), imageData.image_url, imageData.thumbnail_url
      );

      const result = await imageService.deleteImage(imageData.id);

      expect(result.success).toBe(true);
      expect(result.details?.storageType).toBe('oss');
      expect(mockOSSService.deleteFile).toHaveBeenCalledTimes(2);
    });
  });

  describe('数据一致性测试', () => {
    it('应该确保删除操作的原子性', async () => {
      // 创建测试数据
      const category = await categoryService.createCategory({ name: '原子性测试' });
      const imageData = TestDataGenerator.createImage(category.id);
      
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        imageData.id, imageData.category_id, imageData.title, imageData.original_filename, imageData.stored_filename,
        imageData.relative_file_path, imageData.relative_thumbnail_path, imageData.mime_type, imageData.size_bytes,
        imageData.description, imageData.created_at, imageData.updated_at, 
        JSON.stringify(imageData.file_metadata), imageData.image_url, imageData.thumbnail_url
      );

      // Mock文件删除失败
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('文件删除失败');
      });

      const result = await imageService.deleteImage(imageData.id);

      // 即使文件删除失败，数据库记录也应该被删除
      expect(result.databaseDeleted).toBe(true);
      expect(result.storageDeleted).toBe(false);
      expect(result.success).toBe(false);

      // 验证数据库记录确实被删除
      const deletedImage = await imageService.getImageById(imageData.id);
      expect(deletedImage).toBeNull();
    });
  });
});
