import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import * as fs from 'fs';
import * as path from 'path';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  readFileSync: vi.fn(),
  writeFileSync: vi.fn(),
  mkdirSync: vi.fn(),
  unlinkSync: vi.fn(),
  accessSync: vi.fn(),
  constants: {
    W_OK: 2
  }
}));

// Mock path模块
vi.mock('path', async (importOriginal) => {
  const originalPath = await importOriginal<typeof path>();
  return {
    ...originalPath,
    join: vi.fn((...args) => args.join('/')),
    dirname: vi.fn((filePath) => filePath.split('/').slice(0, -1).join('/')),
    basename: vi.fn((filePath) => filePath.split('/').pop() || ''),
    extname: vi.fn((filePath) => {
      const parts = filePath.split('.');
      return parts.length > 1 ? '.' + parts.pop() : '';
    })
  };
});

// Mock crypto模块
vi.mock('crypto', () => ({
  createHash: vi.fn(() => ({
    update: vi.fn().mockReturnThis(),
    digest: vi.fn(() => 'mocked-hash-value')
  }))
}));

// Mock better-sqlite3
vi.mock('better-sqlite3', () => ({
  default: vi.fn().mockImplementation(() => ({
    prepare: vi.fn((query: string) => {
      if (query.includes('sqlite_version()')) {
        return {
          get: vi.fn(() => ({ version: '3.36.0' })),
          all: vi.fn(),
          run: vi.fn()
        };
      }
      if (query.includes('sqlite_master')) {
        return {
          get: vi.fn(),
          all: vi.fn(() => [
            { name: 'categories' },
            { name: 'images' },
            { name: 'tags' },
            { name: 'image_tags' }
          ]),
          run: vi.fn()
        };
      }
      return {
        get: vi.fn(),
        all: vi.fn(),
        run: vi.fn()
      };
    }),
    close: vi.fn(),
    exec: vi.fn()
  }))
}));

import { DatabaseSyncService } from '../../../electron/services/DatabaseSyncService';
import { OSSService } from '../../../electron/services/OSSService';
import { SettingsService } from '../../../electron/services/SettingsService';
import { DatabaseManager } from '../../../electron/database';

describe('数据库同步集成测试', () => {
  let databaseSyncService: DatabaseSyncService;
  let mockOSSService: any;
  let mockSettingsService: any;
  let mockDatabaseManager: any;

  beforeEach(() => {
    // 重置所有mocks
    vi.clearAllMocks();

    // 创建Mock服务实例
    mockOSSService = {
      isConfigured: vi.fn(() => true),
      getConfig: vi.fn(() => ({
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      })),
      uploadFile: vi.fn(),
      downloadFile: vi.fn(),
      listFiles: vi.fn(),
      fileExists: vi.fn(),
      updateConfig: vi.fn()
    };

    mockSettingsService = {
      getSettings: vi.fn(() => ({
        lastBackupTime: null,
        lastRestoreTime: null,
        enableDatabaseSync: true
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn(() => ({
        endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        region: 'cn-hangzhou',
        accessKeyId: 'test-key',
        secretAccessKey: 'test-secret',
        bucket: 'test-bucket',
        pathPrefix: 'pokedex'
      }))
    };

    mockDatabaseManager = {
      getDatabasePath: vi.fn(() => '/tmp/test-db.db'),
      restoreFromBackup: vi.fn(),
      createDatabaseBackup: vi.fn(),
      closeDatabaseSafely: vi.fn()
    };

    // 创建DatabaseSyncService实例
    databaseSyncService = new DatabaseSyncService(
      mockOSSService,
      mockSettingsService,
      mockDatabaseManager
    );

    // Mock validateDatabaseStructure 方法以避免实际文件操作
    // @ts-ignore
    databaseSyncService.validateDatabaseStructure = vi.fn().mockResolvedValue({
      isValid: true,
      tableCount: 4,
      tables: ['categories', 'images', 'tags', 'image_tags'],
      sqliteVersion: '3.36.0'
    });

    // 设置fs.existsSync默认返回true
    vi.mocked(fs.existsSync).mockReturnValue(true);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('完整的备份和恢复工作流', () => {
    it('应该完成完整的备份到恢复流程', async () => {
      // 1. 准备测试数据
      const mockDatabaseContent = Buffer.from('SQLite format 3\0mock database content');
      const backupName = 'backup-2025-07-17-14-30-45.db';

      // 设置文件系统mocks
      vi.mocked(fs.readFileSync).mockReturnValue(mockDatabaseContent);
      vi.mocked(fs.existsSync).mockReturnValue(true);

      // 2. 执行备份
      mockOSSService.uploadFile.mockResolvedValue({ success: true });
      
      const backupResult = await databaseSyncService.backupDatabaseToOSS();
      
      expect(backupResult.success).toBe(true);
      expect(backupResult.message).toBe('数据库备份成功');
      expect(mockOSSService.uploadFile).toHaveBeenCalledWith(
        expect.stringMatching(/^databases\/backup-\d{4}-\d{2}-\d{2}-\d{2}-\d{2}-\d{2}\.db$/),
        mockDatabaseContent,
        'application/x-sqlite3'
      );

      // 3. 列出备份
      mockOSSService.listFiles.mockResolvedValue({
        success: true,
        files: [`databases/${backupName}`]
      });
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });

      const listResult = await databaseSyncService.listDatabaseBackups();
      
      expect(listResult.success).toBe(true);
      expect(listResult.backups).toHaveLength(1);
      expect(listResult.backups?.[0].name).toBe(backupName);

      // 4. 恢复备份
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockDatabaseContent
      });
      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: '恢复成功'
      });

      const restoreResult = await databaseSyncService.restoreDatabaseFromOSS(backupName);
      
      expect(restoreResult.success).toBe(true);
      expect(restoreResult.message).toBe('数据库恢复成功');
      expect(mockDatabaseManager.restoreFromBackup).toHaveBeenCalledWith(mockDatabaseContent);

      // 5. 验证设置更新
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        lastBackupTime: expect.any(String)
      });
      expect(mockSettingsService.saveSettings).toHaveBeenCalledWith({
        lastRestoreTime: expect.any(String)
      });
    });

    it('应该处理网络中断和重连的场景', async () => {
      // 1. 准备测试数据
      const mockDatabaseContent = Buffer.from('SQLite format 3\0test database for retry');

      vi.mocked(fs.readFileSync).mockReturnValue(mockDatabaseContent);

      // 2. 模拟网络不稳定：前两次失败，第三次成功
      mockOSSService.uploadFile
        .mockResolvedValueOnce({ success: false, message: 'Network timeout' })
        .mockResolvedValueOnce({ success: false, message: 'Connection lost' })
        .mockResolvedValueOnce({ success: true });

      // 3. 执行备份（应该重试成功）
      const backupResult = await databaseSyncService.backupDatabaseToOSS();

      expect(backupResult.success).toBe(true);
      expect(mockOSSService.uploadFile).toHaveBeenCalledTimes(3);

      // 4. 模拟下载时的网络问题
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile
        .mockResolvedValueOnce({ success: false, message: 'Download failed' })
        .mockResolvedValueOnce({ success: false, message: 'Timeout' })
        .mockResolvedValueOnce({ success: true, data: mockDatabaseContent });

      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: '恢复成功'
      });

      // 5. 执行恢复（应该重试成功）
      const restoreResult = await databaseSyncService.restoreDatabaseFromOSS('test-backup.db');

      expect(restoreResult.success).toBe(true);
      expect(mockOSSService.downloadFile).toHaveBeenCalledTimes(3);
    });
  });

  describe('多个备份的管理和选择', () => {
    it('应该正确管理多个备份文件', async () => {
      // 1. 模拟多个备份文件
      const mockBackups = [
        'databases/backup-2025-07-17-14-30-45.db',
        'databases/backup-2025-07-16-10-15-20.db',
        'databases/backup-2025-07-15-08-20-30.db',
        'databases/backup-2025-07-14-16-45-10.db'
      ];

      mockOSSService.listFiles.mockResolvedValue({
        success: true,
        files: mockBackups
      });
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });

      // 2. 获取备份列表
      const listResult = await databaseSyncService.listDatabaseBackups();

      expect(listResult.success).toBe(true);
      expect(listResult.backups).toHaveLength(4);

      // 3. 验证备份按时间倒序排列（最新的在前）
      const backups = listResult.backups!;
      expect(backups[0].name).toBe('backup-2025-07-17-14-30-45.db');
      expect(backups[1].name).toBe('backup-2025-07-16-10-15-20.db');
      expect(backups[2].name).toBe('backup-2025-07-15-08-20-30.db');
      expect(backups[3].name).toBe('backup-2025-07-14-16-45-10.db');

      // 4. 选择特定备份进行恢复
      const selectedBackup = 'backup-2025-07-16-10-15-20.db';
      const mockBackupData = Buffer.from('SQLite format 3\0specific backup content');

      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: mockBackupData
      });
      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: true,
        message: '恢复成功'
      });

      const restoreResult = await databaseSyncService.restoreDatabaseFromOSS(selectedBackup);

      expect(restoreResult.success).toBe(true);
      expect(mockOSSService.downloadFile).toHaveBeenCalledWith(`databases/${selectedBackup}`);
      expect(mockDatabaseManager.restoreFromBackup).toHaveBeenCalledWith(mockBackupData);
    });

    it('应该过滤掉无效的备份文件', async () => {
      // 1. 模拟包含无效文件的列表
      const mockFiles = [
        'databases/',
        'databases/backup-2025-07-17-14-30-45.db',  // 有效
        'databases/invalid-backup.txt',              // 无效扩展名
        'databases/not-backup-format.db',            // 无效命名格式
        'databases/backup-2025-07-16-10-15-20.db',  // 有效
        'databases/random-file.json',                // 无效文件
        'databases/backup-invalid-date.db'           // 无效日期格式
      ];

      mockOSSService.listFiles.mockResolvedValue({
        success: true,
        files: mockFiles
      });
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });

      // 2. 获取备份列表
      const listResult = await databaseSyncService.listDatabaseBackups();

      expect(listResult.success).toBe(true);
      expect(listResult.backups).toHaveLength(3); // 3个文件：2个有效格式 + 1个格式无效但符合基本规则

      // 3. 验证包含符合基本规则的备份文件
      const backupNames = listResult.backups!.map(b => b.name);
      expect(backupNames).toContain('backup-2025-07-17-14-30-45.db');
      expect(backupNames).toContain('backup-2025-07-16-10-15-20.db');
      expect(backupNames).toContain('backup-invalid-date.db'); // 虽然日期无效但符合基本规则
      expect(backupNames).not.toContain('invalid-backup.txt');
      expect(backupNames).not.toContain('not-backup-format.db');
    });
  });

  describe('数据库同步配置管理', () => {
    it('应该正确管理同步配置状态', () => {
      // 1. 测试OSS配置正常时的同步状态
      mockOSSService.isConfigured.mockReturnValue(true);
      mockSettingsService.getSettings.mockReturnValue({
        enableDatabaseSync: true,
        lastBackupTime: '2025-07-17T14:30:45.000Z',
        lastRestoreTime: '2025-07-16T10:15:20.000Z'
      });

      const status1 = databaseSyncService.getSyncStatus();
      expect(status1.canSync).toBe(true);
      expect(status1.lastBackupTime).toBe('2025-07-17T14:30:45.000Z');
      expect(status1.lastRestoreTime).toBe('2025-07-16T10:15:20.000Z');

      // 2. 测试OSS未配置时的同步状态
      mockOSSService.isConfigured.mockReturnValue(false);

      const status2 = databaseSyncService.getSyncStatus();
      expect(status2.canSync).toBe(false);

      // 3. 测试canSync方法
      mockOSSService.isConfigured.mockReturnValue(true);
      expect(databaseSyncService.canSync()).toBe(true);

      mockOSSService.isConfigured.mockReturnValue(false);
      expect(databaseSyncService.canSync()).toBe(false);
    });

    it('应该处理配置变更场景', async () => {
      // 1. 初始状态：OSS未配置
      mockOSSService.isConfigured.mockReturnValue(false);

      const backupResult1 = await databaseSyncService.backupDatabaseToOSS();
      expect(backupResult1.success).toBe(false);
      expect(backupResult1.message).toBe('未配置OSS存储，无法执行备份操作');

      // 2. 配置OSS后
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.uploadFile.mockResolvedValue({ success: true });

      const mockDatabaseContent = Buffer.from('SQLite format 3\0test content');
      vi.mocked(fs.readFileSync).mockReturnValue(mockDatabaseContent);

      const backupResult2 = await databaseSyncService.backupDatabaseToOSS();
      expect(backupResult2.success).toBe(true);

      // 3. OSS配置失效
      mockOSSService.isConfigured.mockReturnValue(false);

      const listResult = await databaseSyncService.listDatabaseBackups();
      expect(listResult.success).toBe(false);
      expect(listResult.message).toBe('未配置OSS存储，无法获取备份列表');
    });
  });

  describe('异常情况的处理和恢复', () => {
    it('应该处理备份过程中的各种异常', async () => {
      // 1. 数据库文件不存在
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const result1 = await databaseSyncService.backupDatabaseToOSS();
      expect(result1.success).toBe(false);
      expect(result1.message).toBe('数据库文件不存在');

      // 2. 文件读取失败
      vi.mocked(fs.existsSync).mockReturnValue(true);
      vi.mocked(fs.readFileSync).mockImplementation(() => {
        throw new Error('File read error');
      });

      const result2 = await databaseSyncService.backupDatabaseToOSS();
      expect(result2.success).toBe(false);
      expect(result2.message).toContain('备份失败: File read error');

      // 3. 无效的SQLite文件
      vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('Invalid SQLite file'));

      const result3 = await databaseSyncService.backupDatabaseToOSS();
      expect(result3.success).toBe(false);
      expect(result3.message).toContain('备份数据验证失败');
    });

    it('应该处理恢复过程中的各种异常', async () => {
      const backupName = 'test-backup.db';

      // 1. 备份文件不存在
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: false
      });

      const result1 = await databaseSyncService.restoreDatabaseFromOSS(backupName);
      expect(result1.success).toBe(false);
      expect(result1.message).toBe('指定的备份文件不存在');

      // 2. 下载失败
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: false,
        message: 'Download failed'
      });

      const result2 = await databaseSyncService.restoreDatabaseFromOSS(backupName);
      expect(result2.success).toBe(false);
      expect(result2.message).toContain('下载备份文件失败');

      // 3. 无效的备份数据
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: Buffer.from('Invalid backup data')
      });

      const result3 = await databaseSyncService.restoreDatabaseFromOSS(backupName);
      expect(result3.success).toBe(false);
      expect(result3.message).toContain('备份数据验证失败');

      // 4. 数据库恢复失败
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: Buffer.from('SQLite format 3\0valid backup data')
      });
      mockDatabaseManager.restoreFromBackup.mockResolvedValue({
        success: false,
        message: 'Database restore failed'
      });

      const result4 = await databaseSyncService.restoreDatabaseFromOSS(backupName);
      expect(result4.success).toBe(false);
      expect(result4.message).toContain('数据库恢复失败: Database restore failed');
    });

    it('应该处理OSS服务异常', async () => {
      // 1. OSS连接异常
      mockOSSService.listFiles.mockImplementation(() => {
        throw new Error('OSS connection timeout');
      });

      const listResult = await databaseSyncService.listDatabaseBackups();
      expect(listResult.success).toBe(false);
      expect(listResult.message).toContain('获取备份列表失败: OSS connection timeout');

      // 2. OSS文件检查异常
      mockOSSService.fileExists.mockImplementation(() => {
        throw new Error('OSS file check failed');
      });

      const restoreResult = await databaseSyncService.restoreDatabaseFromOSS('test-backup.db');
      expect(restoreResult.success).toBe(false);
      expect(restoreResult.message).toContain('数据库恢复失败: OSS file check failed');

      // 3. OSS上传异常
      const mockDatabaseContent = Buffer.from('SQLite format 3\0test content');
      vi.mocked(fs.readFileSync).mockReturnValue(mockDatabaseContent);
      mockOSSService.uploadFile.mockImplementation(() => {
        throw new Error('OSS upload failed');
      });

      const backupResult = await databaseSyncService.backupDatabaseToOSS();
      expect(backupResult.success).toBe(false);
      expect(backupResult.message).toContain('备份上传失败');
    });
  });

  describe('数据完整性验证', () => {
    it('应该验证备份文件的完整性', async () => {
      const backupName = 'test-backup.db';

      // 1. 有效的SQLite文件
      const validSQLiteData = Buffer.from('SQLite format 3\0valid database content');
      
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: validSQLiteData
      });

      const result1 = await databaseSyncService.validateDatabaseBackup(backupName);
      expect(result1.success).toBe(true);

      // 2. 无效的SQLite文件
      const invalidData = Buffer.from('Invalid database file');
      
      mockOSSService.downloadFile.mockResolvedValue({
        success: true,
        data: invalidData
      });

      const result2 = await databaseSyncService.validateDatabaseBackup(backupName);
      expect(result2.success).toBe(true);
      expect(result2.valid).toBe(false);
      expect(result2.message).toBe('备份文件格式无效，不是有效的SQLite数据库');

      // 3. 文件不存在
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: false
      });

      const result3 = await databaseSyncService.validateDatabaseBackup(backupName);
      expect(result3.success).toBe(true);
      expect(result3.valid).toBe(false);
      expect(result3.message).toBe('备份文件不存在');
    });
  });

  describe('性能和资源管理', () => {
    it('应该正确处理大文件备份', async () => {
      // 1. 模拟大文件（1MB）- 减少内存使用
      const largeFileSize = 1 * 1024 * 1024;
      const largeFileBuffer = Buffer.alloc(largeFileSize);
      largeFileBuffer.write('SQLite format 3\0', 0); // 添加SQLite头

      vi.mocked(fs.readFileSync).mockReturnValue(largeFileBuffer);
      mockOSSService.uploadFile.mockResolvedValue({ success: true });

      // 2. 执行备份
      const startTime = Date.now();
      const backupResult = await databaseSyncService.backupDatabaseToOSS();
      const endTime = Date.now();

      expect(backupResult.success).toBe(true);
      expect(mockOSSService.uploadFile).toHaveBeenCalledWith(
        expect.any(String),
        largeFileBuffer,
        'application/x-sqlite3'
      );

      // 3. 验证性能（应该在合理时间内完成）
      const duration = endTime - startTime;
      expect(duration).toBeLessThan(5000); // 应该在5秒内完成
    });

    it('应该正确处理并发操作', async () => {
      // 1. 准备多个并发操作
      const mockDatabaseContent = Buffer.from('SQLite format 3\0concurrent test');
      vi.mocked(fs.readFileSync).mockReturnValue(mockDatabaseContent);
      
      mockOSSService.uploadFile.mockResolvedValue({ success: true });
      mockOSSService.listFiles.mockResolvedValue({
        success: true,
        files: ['databases/backup-1.db', 'databases/backup-2.db']
      });
      mockOSSService.fileExists.mockResolvedValue({
        success: true,
        exists: true
      });

      // 2. 并发执行多个操作
      const [backupResult, listResult] = await Promise.all([
        databaseSyncService.backupDatabaseToOSS(),
        databaseSyncService.listDatabaseBackups()
      ]);

      // 3. 验证所有操作都成功
      expect(backupResult.success).toBe(true);
      expect(listResult.success).toBe(true);
      expect(listResult.backups).toHaveLength(2);
    });
  });
});