import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';
import * as fs from 'fs';
import * as path from 'path';
import * as os from 'os';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn((name: string) => {
      switch (name) {
        case 'userData':
          return '/tmp/test-userData';
        case 'documents':
          return '/tmp/test-documents';
        default:
          return '/tmp/test';
      }
    }),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { CategoryService } from '../../../electron/services/CategoryService';
import { ImageService } from '../../../electron/services/ImageService';
import { SettingsService } from '../../../electron/services/SettingsService';

describe('Filesystem Integration Tests', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let imageService: ImageService;
  let settingsService: SettingsService;
  let testDb: any;
  let cleanup: (() => void) | null = null;
  let tempTestDir: string;
  let tempStorageDir: string;

  beforeEach(() => {
    // 创建临时测试目录
    tempTestDir = fs.mkdtempSync(path.join(os.tmpdir(), 'filesystem-test-'));
    tempStorageDir = path.join(tempTestDir, 'storage');
    
    // 使用集成测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建真实的DatabaseManager实例
    dbManager = {
      getDatabase: () => testDb,
      testConnection: vi.fn(() => ({ success: true, message: '连接成功' })),
      getStats: vi.fn(),
      close: vi.fn()
    } as any;

    // 创建SettingsService，使用临时存储路径
    settingsService = new SettingsService();
    settingsService.saveSettings({
      storagePath: tempStorageDir,
      usesCategoryFolders: false
    });

    // 创建真实的服务实例
    categoryService = new CategoryService(dbManager);
    imageService = new ImageService(dbManager, settingsService);

    // 临时保留console.log用于调试
    // console.log = vi.fn();
    console.error = vi.fn();
    console.warn = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
    
    // 清理临时文件系统
    if (fs.existsSync(tempTestDir)) {
      fs.rmSync(tempTestDir, { recursive: true, force: true });
    }
  });

  describe('图片上传生命周期测试', () => {
    it('应该完整地处理图片从上传到删除的整个生命周期', async () => {
      // 1. 创建分类
      const category = await categoryService.createCategory({
        name: 'Lifecycle Test Category'
      });

      // 2. 准备测试图片数据
      const testImageBuffer = Buffer.from('fake image data for lifecycle test');
      const filename = 'lifecycle-test.jpg';
      const mimeType = 'image/jpeg';

      // 3. 上传图片
      const uploadedImage = await imageService.uploadImage(
        category.id,
        testImageBuffer,
        filename,
        mimeType
      );

      // 4. 验证图片数据正确性
      expect(uploadedImage.id).toBeDefined();
      expect(uploadedImage.category_id).toBe(category.id);
      expect(uploadedImage.original_filename).toBe(filename);
      expect(uploadedImage.size_bytes).toBe(testImageBuffer.length);

      // 5. 验证文件系统中的文件
      const expectedImagePath = imageService.getImagePath(uploadedImage.stored_filename);
      const expectedThumbnailPath = imageService.getThumbnailPath(
        `${path.parse(uploadedImage.stored_filename).name}_thumb${path.extname(uploadedImage.stored_filename)}`
      );

      expect(fs.existsSync(expectedImagePath)).toBe(true);
      expect(fs.existsSync(expectedThumbnailPath)).toBe(true);

      // 6. 验证文件内容
      const storedImageData = fs.readFileSync(expectedImagePath);
      expect(storedImageData.equals(testImageBuffer)).toBe(true);

      // 7. 验证数据库中的记录
      const retrievedImage = await imageService.getImageById(uploadedImage.id);
      expect(retrievedImage).not.toBeNull();
      expect(retrievedImage!.id).toBe(uploadedImage.id);

      // 8. 删除图片
      await imageService.deleteImage(uploadedImage.id);

      // 9. 验证文件已被删除
      expect(fs.existsSync(expectedImagePath)).toBe(false);
      expect(fs.existsSync(expectedThumbnailPath)).toBe(false);

      // 10. 验证数据库记录已被删除
      const deletedImage = await imageService.getImageById(uploadedImage.id);
      expect(deletedImage).toBeNull();
    });

    it('应该处理上传失败时的文件清理', async () => {
      const category = await categoryService.createCategory({
        name: 'Cleanup Test Category' });

      // Mock数据库插入失败
      const originalPrepare = testDb.prepare;
      testDb.prepare = vi.fn().mockImplementation((sql: string) => {
        if (sql.includes('INSERT INTO images')) {
          throw new Error('Database insert failed');
        }
        return originalPrepare.call(testDb, sql);
      });

      const testImageBuffer = Buffer.from('test cleanup data');
      
      // 尝试上传图片，应该失败
      await expect(
        imageService.uploadImage(category.id, testImageBuffer, 'cleanup-test.jpg', 'image/jpeg')
      ).rejects.toThrow('Database insert failed');

      // 恢复原始方法
      testDb.prepare = originalPrepare;

      // 验证没有遗留文件（因为失败时应该清理）
      if (fs.existsSync(tempStorageDir)) {
        const filesInStorage = fs.readdirSync(tempStorageDir, { recursive: true });
        const imageFiles = filesInStorage.filter(file => 
          typeof file === 'string' && file.endsWith('.jpg')
        );
        expect(imageFiles).toHaveLength(0);
      } else {
        // 如果目录都不存在，说明没有创建任何文件，这也是正确的
        expect(true).toBe(true);
      }
    });

    it('应该处理多个图片的并发上传', async () => {
      const category = await categoryService.createCategory({
        name: 'Concurrent Upload Category' });

      // 准备多个测试图片
      const imagePromises = [];
      for (let i = 1; i <= 5; i++) {
        const buffer = Buffer.from(`concurrent test image ${i}`);
        imagePromises.push(
          imageService.uploadImage(category.id, buffer, `concurrent-${i}.jpg`, 'image/jpeg')
        );
      }

      // 并发上传
      const uploadedImages = await Promise.all(imagePromises);

      // 验证所有图片都成功上传
      expect(uploadedImages).toHaveLength(5);
      uploadedImages.forEach((image, index) => {
        expect(image.id).toBeDefined();
        expect(image.original_filename).toBe(`concurrent-${index + 1}.jpg`);
      });

      // 验证所有文件都存在
      for (const image of uploadedImages) {
        const imagePath = imageService.getImagePath(image.stored_filename);
        const thumbnailPath = imageService.getThumbnailPath(
          `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`
        );
        expect(fs.existsSync(imagePath)).toBe(true);
        expect(fs.existsSync(thumbnailPath)).toBe(true);
      }

      // 验证文件系统结构
      if (fs.existsSync(tempStorageDir)) {
        const allFiles = fs.readdirSync(tempStorageDir, { recursive: true });
        const imageFiles = allFiles.filter(file => 
          typeof file === 'string' && file.endsWith('.jpg') && !file.includes('_thumb')
        );
        const thumbnailFiles = allFiles.filter(file => 
          typeof file === 'string' && file.includes('_thumb.jpg')
        );
        
        expect(imageFiles).toHaveLength(5);
        expect(thumbnailFiles).toHaveLength(5);
      }
    });
  });

  describe('存储路径变更测试', () => {
    it('应该处理从统一存储到分类文件夹的切换', async () => {
      // 1. 在统一存储模式下创建图片
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: false
      });

      const category1 = await categoryService.createCategory({
        name: 'Category 1'
      });
      const category2 = await categoryService.createCategory({
        name: 'Category 2'
      });

      const image1 = await imageService.uploadImage(
        category1.id,
        Buffer.from('image 1 data'),
        'image1.jpg',
        'image/jpeg'
      );
      const image2 = await imageService.uploadImage(
        category2.id,
        Buffer.from('image 2 data'),
        'image2.jpg',
        'image/jpeg'
      );

      // 验证统一存储结构 - 检查文件是否被正确上传
      const uploadedImages = [image1, image2];
      for (const img of uploadedImages) {
        const imagePath = imageService.getImagePath(img.stored_filename);
        const thumbnailPath = imageService.getThumbnailPath(
          `${path.parse(img.stored_filename).name}_thumb${path.extname(img.stored_filename)}`
        );
        expect(fs.existsSync(imagePath)).toBe(true);
        expect(fs.existsSync(thumbnailPath)).toBe(true);
      }

      // 2. 切换到分类文件夹模式
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: true
      });

      // 重新创建ImageService以使用新设置
      imageService = new ImageService(dbManager, settingsService);

      // 3. 执行迁移
      // 写入调试文件
      fs.writeFileSync('/tmp/debug-migration.log', '=== 开始迁移前状态检查 ===\n');
      fs.appendFileSync('/tmp/debug-migration.log', `临时目录: ${tempStorageDir}\n`);
      fs.appendFileSync('/tmp/debug-migration.log', `当前设置: ${JSON.stringify(settingsService.getSettings())}\n`);
      
      const migrationResult = await imageService.migrateStorageLocation(tempStorageDir);
      
      fs.appendFileSync('/tmp/debug-migration.log', '=== 迁移结果 ===\n');
      fs.appendFileSync('/tmp/debug-migration.log', `迁移结果: ${JSON.stringify(migrationResult, null, 2)}\n`);
      
      expect(migrationResult.success).toBe(true);
      if (migrationResult.details) {
        expect(migrationResult.details.success).toBe(2);
      }

      // 4. 验证新的分类文件夹结构
      const category1Dir = path.join(tempStorageDir, 'Category_1');
      const category2Dir = path.join(tempStorageDir, 'Category_2');
      
      fs.appendFileSync('/tmp/debug-migration.log', '=== 验证目录结构 ===\n');
      fs.appendFileSync('/tmp/debug-migration.log', `期望的category1Dir: ${category1Dir}\n`);
      fs.appendFileSync('/tmp/debug-migration.log', `期望的category2Dir: ${category2Dir}\n`);
      fs.appendFileSync('/tmp/debug-migration.log', `category1Dir存在: ${fs.existsSync(category1Dir)}\n`);
      fs.appendFileSync('/tmp/debug-migration.log', `category2Dir存在: ${fs.existsSync(category2Dir)}\n`);
      
      if (fs.existsSync(tempStorageDir)) {
        fs.appendFileSync('/tmp/debug-migration.log', `实际目录内容: ${JSON.stringify(fs.readdirSync(tempStorageDir))}\n`);
      }
      
      expect(fs.existsSync(category1Dir)).toBe(true);
      expect(fs.existsSync(path.join(category1Dir, 'thumbnails'))).toBe(true);
      expect(fs.existsSync(category2Dir)).toBe(true);
      expect(fs.existsSync(path.join(category2Dir, 'thumbnails'))).toBe(true);

      // 5. 验证文件迁移
      expect(fs.existsSync(path.join(category1Dir, image1.stored_filename))).toBe(true);
      expect(fs.existsSync(path.join(category2Dir, image2.stored_filename))).toBe(true);

      // 6. 验证旧文件已经被迁移（现在旧文件在分类文件夹中）
      const oldImage1Path = path.join(tempStorageDir, 'Category_1', image1.stored_filename);
      const oldImage2Path = path.join(tempStorageDir, 'Category_2', image2.stored_filename);
      expect(fs.existsSync(oldImage1Path)).toBe(true);
      expect(fs.existsSync(oldImage2Path)).toBe(true);

      // 7. 验证数据库路径已更新
      const updatedImage1 = await imageService.getImageById(image1.id);
      const updatedImage2 = await imageService.getImageById(image2.id);
      
      expect(updatedImage1!.relative_file_path).toBe('Category_1/' + image1.stored_filename);
      expect(updatedImage2!.relative_file_path).toBe('Category_2/' + image2.stored_filename);
    });

    it('应该处理存储位置的完全迁移', async () => {
      // 创建原始存储位置
      const oldStorageDir = path.join(tempTestDir, 'old-storage');
      const newStorageDir = path.join(tempTestDir, 'new-storage');
      
      settingsService.saveSettings({
        storagePath: oldStorageDir,
        usesCategoryFolders: true
      });

      imageService = new ImageService(dbManager, settingsService);

      // 在原位置创建测试数据
      const category = await categoryService.createCategory({
        name: 'Migration Test' });

      const testImages = [];
      for (let i = 1; i <= 3; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.from(`migration test image ${i}`),
          `migration-${i}.jpg`,
          'image/jpeg'
        );
        testImages.push(image);
      }

      // 验证原位置文件存在
      const oldCategoryDir = path.join(oldStorageDir, 'Migration_Test');
      expect(fs.existsSync(oldCategoryDir)).toBe(true);
      expect(fs.existsSync(path.join(oldCategoryDir, 'thumbnails'))).toBe(true);

      // 执行完全迁移到新位置
      const migrationResult = await imageService.migrateStorageLocation(newStorageDir);
      
      expect(migrationResult.success).toBe(true);
      expect(migrationResult.details.success).toBe(3);

      // 验证新位置文件存在
      const newCategoryDir = path.join(newStorageDir, 'Migration_Test');
      expect(fs.existsSync(newCategoryDir)).toBe(true);
      expect(fs.existsSync(path.join(newCategoryDir, 'thumbnails'))).toBe(true);

      // 验证所有文件都已迁移
      for (const image of testImages) {
        const newImagePath = path.join(newCategoryDir, image.stored_filename);
        const thumbFilename = `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`;
        const newThumbnailPath = path.join(newCategoryDir, 'thumbnails', thumbFilename);
        
        expect(fs.existsSync(newImagePath)).toBe(true);
        expect(fs.existsSync(newThumbnailPath)).toBe(true);
      }

      // 验证原位置文件已被移除 (如果迁移成功)
      if (migrationResult.success) {
        expect(fs.existsSync(oldCategoryDir)).toBe(false);
      }
    });

    it('应该处理迁移过程中的部分失败', async () => {
      const category = await categoryService.createCategory({
        name: 'Partial Fail Test' });

      // 创建一些测试图片
      const image1 = await imageService.uploadImage(
        category.id,
        Buffer.from('partial fail test 1'),
        'partial1.jpg',
        'image/jpeg'
      );

      const image2 = await imageService.uploadImage(
        category.id,
        Buffer.from('partial fail test 2'),
        'partial2.jpg',
        'image/jpeg'
      );

      // 故意删除其中一个原图文件，模拟部分文件丢失
      const image1Path = imageService.getImagePath(image1.stored_filename);
      fs.unlinkSync(image1Path);

      // 执行迁移到新位置
      const newStorageDir = path.join(tempTestDir, 'partial-fail-new');
      const migrationResult = await imageService.migrateStorageLocation(newStorageDir);

      // 验证迁移结果记录了问题
      expect(migrationResult).toBeDefined();
      if (migrationResult.details) {
        // 应该有一些成功和失败的记录
        expect(migrationResult.details.total).toBe(2);
      }

      // 验证成功的文件已迁移
      const newCategoryDir = path.join(newStorageDir, 'Partial_Fail_Test');
      const image2NewPath = path.join(newCategoryDir, image2.stored_filename);
      expect(fs.existsSync(image2NewPath)).toBe(true);
    });
  });

  describe('分类文件夹结构切换测试', () => {
    it('应该正确创建和管理分类特定的目录结构', async () => {
      // 启用分类文件夹模式
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: true
      });

      imageService = new ImageService(dbManager, settingsService);

      // 创建多个分类
      const categories = await Promise.all([
        categoryService.createCategory({ name: 'Birds' }),
        categoryService.createCategory({ name: 'Mammals' }),
        categoryService.createCategory({ name: 'Special/Characters?' })
      ]);

      // 为每个分类上传图片
      const images = [];
      for (let i = 0; i < categories.length; i++) {
        const image = await imageService.uploadImage(
          categories[i].id,
          Buffer.from(`test image for category ${i}`),
          `category-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);
      }

      // 验证图片文件确实被创建和可以访问
      for (let i = 0; i < images.length; i++) {
        const image = images[i];
        expect(imageService.imageExists(image.stored_filename)).toBe(true);
        expect(imageService.thumbnailExists(
          `${path.parse(image.stored_filename).name}_thumb${path.extname(image.stored_filename)}`
        )).toBe(true);
      }

      // 验证路径解析功能能够找到文件
      for (const image of images) {
        const resolvedPath = imageService.getImagePath(image.stored_filename);
        expect(fs.existsSync(resolvedPath)).toBe(true);
      }
    });

    it('应该处理分类名称变更对目录结构的影响', async () => {
      // 启用分类文件夹模式
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: true
      });

      imageService = new ImageService(dbManager, settingsService);

      // 创建分类并上传图片
      const category = await categoryService.createCategory({
        name: 'Original Name' });

      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('category rename test'),
        'rename-test.jpg',
        'image/jpeg'
      );

      // 验证原始目录结构
      const originalDir = path.join(tempStorageDir, 'Original_Name');
      expect(fs.existsSync(path.join(originalDir, image.stored_filename))).toBe(true);

      // 更新分类名称
      await categoryService.updateCategory(category.id, { name: 'New Name' });

      // 上传新图片（应该使用新的目录名）
      const newImage = await imageService.uploadImage(
        category.id,
        Buffer.from('new category name test'),
        'new-name-test.jpg',
        'image/jpeg'
      );

      // 验证新目录结构
      const newDir = path.join(tempStorageDir, 'New_Name');
      expect(fs.existsSync(path.join(newDir, newImage.stored_filename))).toBe(true);

      // 注意：原有文件不会自动迁移，这是预期行为
      // 旧文件仍在原目录，新文件在新目录
      expect(fs.existsSync(path.join(originalDir, image.stored_filename))).toBe(true);
    });
  });

  describe('磁盘空间和错误处理测试', () => {
    it('应该处理磁盘空间不足的情况', async () => {
      // 这个测试模拟真实的磁盘空间不足场景比较困难
      // 在集成测试中，我们重点测试正常流程的完整性
      const category = await categoryService.createCategory({
        name: 'Disk Space Test' });

      // 验证正常情况下图片上传成功
      const testBuffer = Buffer.from('disk space test');
      const image = await imageService.uploadImage(
        category.id, 
        testBuffer, 
        'disk-space-test.jpg', 
        'image/jpeg'
      );

      expect(image.id).toBeDefined();
      expect(image.size_bytes).toBe(testBuffer.length);

      // 验证文件确实存在
      expect(imageService.imageExists(image.stored_filename)).toBe(true);
    });

    it('应该处理文件系统权限问题', async () => {
      // 在集成测试中，我们测试正常的权限场景
      const category = await categoryService.createCategory({
        name: 'Permission Test' });

      const testBuffer = Buffer.from('permission test');
      
      // 验证正常权限下的操作成功
      const image = await imageService.uploadImage(
        category.id, 
        testBuffer, 
        'permission-test.jpg', 
        'image/jpeg'
      );

      expect(image.id).toBeDefined();
      expect(imageService.imageExists(image.stored_filename)).toBe(true);

      // 验证删除操作也能成功
      await imageService.deleteImage(image.id);
      expect(imageService.imageExists(image.stored_filename)).toBe(false);
    });

    it('应该正确检测和报告目录创建问题', async () => {
      // 创建一个无法访问的目录路径
      const inaccessiblePath = path.join(tempTestDir, 'inaccessible');
      fs.mkdirSync(inaccessiblePath);
      
      // 更新设置使用无法访问的路径
      settingsService.saveSettings({
        storagePath: inaccessiblePath,
        usesCategoryFolders: true
      });

      // 模拟权限问题
      fs.chmodSync(inaccessiblePath, 0o444); // 只读权限

      try {
        imageService = new ImageService(dbManager, settingsService);
        
        const category = await categoryService.createCategory(
          TestDataGenerator.createCategory({ name: 'Access Test' })
        );

        // 这应该失败，因为无法在只读目录中创建子目录
        await expect(
          imageService.uploadImage(
            category.id,
            Buffer.from('access test'),
            'access-test.jpg',
            'image/jpeg'
          )
        ).rejects.toThrow();

      } finally {
        // 恢复权限以便清理
        fs.chmodSync(inaccessiblePath, 0o755);
      }
    });
  });

  describe('文件路径解析和兼容性测试', () => {
    it('应该正确解析不同模式下的文件路径', async () => {
      const category = await categoryService.createCategory({
        name: 'Path Resolution Test' });

      // 1. 测试统一存储模式
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: false
      });
      imageService = new ImageService(dbManager, settingsService);

      const unifiedImage = await imageService.uploadImage(
        category.id,
        Buffer.from('unified path test'),
        'unified.jpg',
        'image/jpeg'
      );

      const unifiedPath = imageService.getImagePath(unifiedImage.stored_filename);
      expect(fs.existsSync(unifiedPath)).toBe(true);

      // 2. 测试分类文件夹模式
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: true
      });
      imageService = new ImageService(dbManager, settingsService);

      const categoryImage = await imageService.uploadImage(
        category.id,
        Buffer.from('category path test'),
        'category.jpg',
        'image/jpeg'
      );

      const categoryPath = imageService.getImagePath(categoryImage.stored_filename);
      expect(categoryPath).toBe(
        path.join(tempStorageDir, 'Path_Resolution_Test', categoryImage.stored_filename)
      );
      expect(fs.existsSync(categoryPath)).toBe(true);

      // 3. 验证两种模式下的文件都能被正确访问
      expect(imageService.imageExists(unifiedImage.stored_filename)).toBe(true);
      expect(imageService.imageExists(categoryImage.stored_filename)).toBe(true);
    });

    it('应该处理文件不存在的情况', async () => {
      const nonExistentFilename = 'non-existent-file.jpg';
      
      // 测试图片存在性检查
      expect(imageService.imageExists(nonExistentFilename)).toBe(false);
      expect(imageService.thumbnailExists(nonExistentFilename)).toBe(false);

      // 测试路径解析（应该返回默认路径但文件不存在）
      const imagePath = imageService.getImagePath(nonExistentFilename);
      expect(imagePath).toBeDefined();
      expect(fs.existsSync(imagePath)).toBe(false);
    });

    it('应该正确处理相对路径和绝对路径转换', async () => {
      settingsService.saveSettings({
        storagePath: tempStorageDir,
        usesCategoryFolders: true
      });
      imageService = new ImageService(dbManager, settingsService);

      const category = await categoryService.createCategory({
        name: 'Path Conversion Test' });

      const image = await imageService.uploadImage(
        category.id,
        Buffer.from('path conversion test'),
        'conversion.jpg',
        'image/jpeg'
      );

      // 验证相对路径格式
      expect(image.relative_file_path).toMatch(/Path_Conversion_Test\/.*\.jpg/);
      expect(image.relative_thumbnail_path).toMatch(/Path_Conversion_Test\/thumbnails\/.*_thumb\.jpg/);

      // 验证相对路径使用正斜杠（跨平台兼容）
      expect(image.relative_file_path).not.toContain('\\');
      expect(image.relative_thumbnail_path).not.toContain('\\');

      // 验证绝对路径解析
      const absoluteImagePath = imageService.getImagePath(image.stored_filename);
      expect(path.isAbsolute(absoluteImagePath)).toBe(true);
      expect(fs.existsSync(absoluteImagePath)).toBe(true);
    });
  });

  describe('大量文件处理性能测试', () => {
    it('应该高效处理大量文件的创建和删除', async () => {
      const category = await categoryService.createCategory({
        name: 'Performance Test' });

      const startTime = Date.now();
      
      // 创建20个文件
      const images = [];
      for (let i = 1; i <= 20; i++) {
        const image = await imageService.uploadImage(
          category.id,
          Buffer.from(`performance test image ${i}`),
          `perf-${i}.jpg`,
          'image/jpeg'
        );
        images.push(image);
      }

      const uploadTime = Date.now() - startTime;

      // 验证所有文件都已创建
      expect(images).toHaveLength(20);
      for (const image of images) {
        expect(imageService.imageExists(image.stored_filename)).toBe(true);
      }

      // 删除所有文件
      const deleteStartTime = Date.now();
      for (const image of images) {
        await imageService.deleteImage(image.id);
      }
      const deleteTime = Date.now() - deleteStartTime;

      // 验证所有文件都已删除
      for (const image of images) {
        expect(imageService.imageExists(image.stored_filename)).toBe(false);
      }

      // 性能应该合理（不超过5秒创建，2秒删除）
      expect(uploadTime).toBeLessThan(5000);
      expect(deleteTime).toBeLessThan(2000);

      console.log(`文件创建耗时: ${uploadTime}ms, 删除耗时: ${deleteTime}ms`);
    });

    it('应该正确处理目录遍历和文件统计', async () => {
      // 创建多个分类和文件
      const categories = await Promise.all([
        categoryService.createCategory({ name: 'Stats Test 1' }),
        categoryService.createCategory({ name: 'Stats Test 2' })
      ]);

      // 每个分类创建3个文件
      for (const category of categories) {
        for (let i = 1; i <= 3; i++) {
          await imageService.uploadImage(
            category.id,
            Buffer.from(`stats test ${category.id}-${i}`),
            `stats-${category.id}-${i}.jpg`,
            'image/jpeg'
          );
        }
      }

      // 统计文件系统中的文件
      if (fs.existsSync(tempStorageDir)) {
        const allFiles = fs.readdirSync(tempStorageDir, { recursive: true });
        const imageFiles = allFiles.filter(file => 
          typeof file === 'string' && file.endsWith('.jpg') && !file.includes('_thumb')
        );
        const thumbnailFiles = allFiles.filter(file => 
          typeof file === 'string' && file.includes('_thumb.jpg')
        );

        expect(imageFiles).toHaveLength(6); // 2个分类 × 3个图片
        expect(thumbnailFiles).toHaveLength(6); // 2个分类 × 3个缩略图
      }

      // 验证数据库记录与文件系统一致
      const allImagesInDb = await Promise.all(
        categories.map(cat => imageService.getImagesByCategoryId(cat.id))
      );
      const totalImagesInDb = allImagesInDb.flat().length;
      expect(totalImagesInDb).toBe(6);
    });
  });
});