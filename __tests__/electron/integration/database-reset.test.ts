import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';

// Mock electron模块
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn((name: string) => {
      switch (name) {
        case 'userData':
          return '/tmp/test-userData';
        case 'documents':
          return '/tmp/test-documents';
        default:
          return '/tmp/test';
      }
    }),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';

describe('数据库重置功能集成测试', () => {
  let dbManager: DatabaseManager;
  let testDbManager: TestDatabaseManager;

  beforeEach(() => {
    testDbManager = TestDatabaseManager.getInstance();
    dbManager = new DatabaseManager();
  });

  afterEach(() => {
    if (dbManager) {
      dbManager.close();
    }
    testDbManager.cleanupAll();
  });

  describe('完整的重置流程', () => {
    it('应该完整地重置数据库并恢复默认状态', async () => {
      const db = dbManager.getDatabase();
      
      // 1. 验证初始状态
      const initialStats = dbManager.getStats();
      expect(initialStats.success).toBe(true);
      expect(initialStats.data.categories.count).toBe(3);
      expect(initialStats.data.tags.count).toBe(0);
      expect(initialStats.data.images.count).toBe(0);
      expect(initialStats.data.imageTags.count).toBe(0);
      
      // 2. 添加测试数据
      // 添加标签
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('tag1', '测试标签1', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('tag2', '测试标签2', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      // 添加图片
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('img1', 'magpie', 'test-url-1', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('img2', 'sparrow', 'test-url-2', '2024-01-01T00:00:00Z')
      `).run();
      
      // 添加图片标签关联
      db.prepare(`
        INSERT INTO image_tags (image_id, tag_id)
        VALUES ('img1', 'tag1')
      `).run();
      
      db.prepare(`
        INSERT INTO image_tags (image_id, tag_id)
        VALUES ('img2', 'tag2')
      `).run();
      
      // 3. 验证数据已添加
      const statsWithData = dbManager.getStats();
      expect(statsWithData.data.categories.count).toBe(3);
      expect(statsWithData.data.tags.count).toBe(2);
      expect(statsWithData.data.images.count).toBe(2);
      expect(statsWithData.data.imageTags.count).toBe(2);
      
      // 4. 执行重置
      const resetResult = dbManager.resetDatabase();
      
      // 5. 验证重置结果
      expect(resetResult.success).toBe(true);
      expect(resetResult.message).toBe('数据库重置成功');
      expect(resetResult.timestamp).toBeDefined();
      
      // 6. 验证数据已被清理
      const statsAfterReset = dbManager.getStats();
      expect(statsAfterReset.data.categories.count).toBe(3); // 默认分类
      expect(statsAfterReset.data.tags.count).toBe(0); // 无标签
      expect(statsAfterReset.data.images.count).toBe(0); // 无图片
      expect(statsAfterReset.data.imageTags.count).toBe(0); // 无关联
      
      // 7. 验证默认分类内容
      const categories = db.prepare('SELECT * FROM categories ORDER BY name').all();
      expect(categories.length).toBe(3);
      
      const categoryData = categories.map((cat: any) => ({
        id: cat.id,
        name: cat.name
      }));
      
      // 验证分类数量和内容正确
      expect(categoryData.length).toBe(3);
      
      // 验证所有默认分类都存在
      const categoryIds = categoryData.map(cat => cat.id);
      expect(categoryIds).toContain('turtle-dove');
      expect(categoryIds).toContain('magpie');
      expect(categoryIds).toContain('sparrow');
      
      const categoryNames = categoryData.map(cat => cat.name);
      expect(categoryNames).toContain('斑鸠');
      expect(categoryNames).toContain('喜鹊');
      expect(categoryNames).toContain('麻雀');
      
      // 8. 验证表结构完整性
      const tables = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);
      
      expect(tables).toContain('categories');
      expect(tables).toContain('images');
      expect(tables).toContain('tags');
      expect(tables).toContain('image_tags');
      
      // 9. 验证索引完整性
      const indexes = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='index' AND name NOT LIKE 'sqlite_%'
      `).all().map((row: any) => row.name);
      
      expect(indexes).toContain('idx_images_category');
      expect(indexes).toContain('idx_images_created');
      expect(indexes).toContain('idx_tags_name');
      expect(indexes).toContain('idx_image_tags_image');
      expect(indexes).toContain('idx_image_tags_tag');
    });

    it('应该处理复杂的关联数据重置', async () => {
      const db = dbManager.getDatabase();
      
      // 创建复杂的关联数据
      const tags = [
        { id: 'tag1', name: '小型鸟' },
        { id: 'tag2', name: '城市鸟' },
        { id: 'tag3', name: '森林鸟' }
      ];
      
      const images = [
        { id: 'img1', category_id: 'magpie', title: '喜鹊图片1' },
        { id: 'img2', category_id: 'magpie', title: '喜鹊图片2' },
        { id: 'img3', category_id: 'sparrow', title: '麻雀图片1' },
        { id: 'img4', category_id: 'turtle-dove', title: '斑鸠图片1' }
      ];
      
      const imageTags = [
        { image_id: 'img1', tag_id: 'tag1' },
        { image_id: 'img1', tag_id: 'tag2' },
        { image_id: 'img2', tag_id: 'tag2' },
        { image_id: 'img3', tag_id: 'tag1' },
        { image_id: 'img3', tag_id: 'tag2' },
        { image_id: 'img4', tag_id: 'tag3' }
      ];
      
      // 插入数据
      for (const tag of tags) {
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES (?, ?, '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run(tag.id, tag.name);
      }
      
      for (const image of images) {
        db.prepare(`
          INSERT INTO images (id, category_id, image_url, title, created_at)
          VALUES (?, ?, 'test-url', ?, '2024-01-01T00:00:00Z')
        `).run(image.id, image.category_id, image.title);
      }
      
      for (const imageTag of imageTags) {
        db.prepare(`
          INSERT INTO image_tags (image_id, tag_id)
          VALUES (?, ?)
        `).run(imageTag.image_id, imageTag.tag_id);
      }
      
      // 验证数据插入成功
      const statsBeforeReset = dbManager.getStats();
      expect(statsBeforeReset.data.tags.count).toBe(3);
      expect(statsBeforeReset.data.images.count).toBe(4);
      expect(statsBeforeReset.data.imageTags.count).toBe(6);
      
      // 执行重置
      const resetResult = dbManager.resetDatabase();
      expect(resetResult.success).toBe(true);
      
      // 验证所有关联数据都被清理
      const statsAfterReset = dbManager.getStats();
      expect(statsAfterReset.data.categories.count).toBe(3);
      expect(statsAfterReset.data.tags.count).toBe(0);
      expect(statsAfterReset.data.images.count).toBe(0);
      expect(statsAfterReset.data.imageTags.count).toBe(0);
      
      // 验证可以重新插入数据（表结构正常）
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('new-tag-${Date.now()}', '新标签-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      const newStats = dbManager.getStats();
      expect(newStats.data.tags.count).toBe(1);
    });

    it('应该处理重置过程中的事务回滚', async () => {
      const db = dbManager.getDatabase();
      
      // 添加一些测试数据
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-${Date.now()}', '测试标签-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      db.prepare(`
        INSERT INTO images (id, category_id, image_url, created_at)
        VALUES ('test-img-${Date.now()}', 'magpie', 'test-url-${Date.now()}', '2024-01-01T00:00:00Z')
      `).run();
      
      const statsBeforeReset = dbManager.getStats();
      expect(statsBeforeReset.data.tags.count).toBeGreaterThanOrEqual(1);
      expect(statsBeforeReset.data.images.count).toBeGreaterThanOrEqual(1);
      
      // 执行重置
      const resetResult = dbManager.resetDatabase();
      expect(resetResult.success).toBe(true);
      
      // 验证事务的原子性 - 要么全部成功，要么全部失败
      const statsAfterReset = dbManager.getStats();
      expect(statsAfterReset.data.categories.count).toBe(3); // 默认分类已恢复
      expect(statsAfterReset.data.tags.count).toBe(0); // 所有标签被清理
      expect(statsAfterReset.data.images.count).toBe(0); // 所有图片被清理
      expect(statsAfterReset.data.imageTags.count).toBe(0); // 所有关联被清理
      
      // 验证数据库仍然可以正常工作
      const newCategories = db.prepare('SELECT COUNT(*) as count FROM categories').get() as { count: number };
      expect(newCategories.count).toBe(3);
      
      // 验证sqlite_sequence表处理正确
      const sequenceTableExists = db.prepare(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name='sqlite_sequence'
      `).get();
      
      if (sequenceTableExists) {
        const sequences = db.prepare(`
          SELECT name FROM sqlite_sequence 
          WHERE name IN ('categories', 'images', 'tags', 'image_tags')
        `).all();
        expect(sequences.length).toBe(0); // 序列表应该被清理
      }
      // 如果不存在sqlite_sequence表，这是正常的
    });

    it('应该正确处理重置错误情况', async () => {
      // 创建一个有效的数据库管理器
      const validDbManager = new DatabaseManager();
      
      // 添加一些数据
      const db = validDbManager.getDatabase();
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-${Date.now()}', '测试标签-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      // 验证数据存在
      const statsBeforeClose = validDbManager.getStats();
      expect(statsBeforeClose.data.tags.count).toBeGreaterThanOrEqual(1);
      
      // 关闭数据库连接来模拟错误
      validDbManager.close();
      
      // 尝试重置已关闭的数据库
      const resetResult = validDbManager.resetDatabase();
      
      // 验证错误处理
      expect(resetResult.success).toBe(false);
      expect(resetResult.message).toBe('数据库重置失败');
      expect(resetResult.error).toBeDefined();
      expect(typeof resetResult.error).toBe('string');
    });
  });

  describe('重置后的数据验证', () => {
    it('重置后应该能够正常进行数据库操作', async () => {
      const db = dbManager.getDatabase();
      
      // 添加一些数据
      db.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES ('test-tag-${Date.now()}', '测试标签-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
      `).run();
      
      // 执行重置
      const resetResult = dbManager.resetDatabase();
      expect(resetResult.success).toBe(true);
      
      // 验证可以正常插入新数据
      expect(() => {
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES ('new-tag-${Date.now()}', '新标签-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run();
      }).not.toThrow();
      
      // 验证可以正常查询数据
      const tags = db.prepare('SELECT * FROM tags').all();
      expect(tags.length).toBe(1);
      expect(tags[0].id).toContain('new-tag-');
      expect(tags[0].name).toContain('新标签-');
      
      // 验证可以正常插入关联数据
      expect(() => {
        db.prepare(`
          INSERT INTO images (id, category_id, image_url, created_at)
          VALUES ('new-img-${Date.now()}', 'magpie', 'test-url-${Date.now()}', '2024-01-01T00:00:00Z')
        `).run();
        
        db.prepare(`
          INSERT INTO image_tags (image_id, tag_id)
          VALUES ('new-img-${Date.now()}', 'new-tag-${Date.now()}')
        `).run();
      }).not.toThrow();
      
      // 验证关联数据正确
      const imageTags = db.prepare('SELECT * FROM image_tags').all();
      expect(imageTags.length).toBe(1);
      expect(imageTags[0].image_id).toContain('new-img-');
      expect(imageTags[0].tag_id).toContain('new-tag-');
    });

    it('重置后的默认分类应该包含正确的描述信息', async () => {
      // 执行重置
      const resetResult = dbManager.resetDatabase();
      expect(resetResult.success).toBe(true);
      
      const db = dbManager.getDatabase();
      const categories = db.prepare('SELECT * FROM categories ORDER BY name').all();
      
      expect(categories.length).toBe(3);
      
      // 验证分类详细信息
      const categoryMap = categories.reduce((map: any, cat: any) => {
        map[cat.id] = cat;
        return map;
      }, {});
      
      // 验证各个分类的详细信息
      expect(categoryMap['magpie']).toMatchObject({
        id: 'magpie',
        name: '喜鹊',
        description: '聪明的黑白相间鸟类，善于模仿声音，常见于城市和乡村'
      });
      
      expect(categoryMap['sparrow']).toMatchObject({
        id: 'sparrow',
        name: '麻雀',
        description: '小型褐色鸟类，适应性强，广泛分布于人类居住区'
      });
      
      expect(categoryMap['turtle-dove']).toMatchObject({
        id: 'turtle-dove',
        name: '斑鸠',
        description: '中型鸟类，羽毛有斑点，性情温和，常见于公园和树林'
      });
    });
  });

  describe('性能和稳定性测试', () => {
    it('应该能够处理多次连续重置', async () => {
      const db = dbManager.getDatabase();
      
      // 执行多次重置
      for (let i = 0; i < 5; i++) {
        // 添加一些测试数据
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES ('test-tag-${i}', '测试标签${i}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run();
        
        // 执行重置
        const resetResult = dbManager.resetDatabase();
        expect(resetResult.success).toBe(true);
        
        // 验证每次重置后的状态
        const stats = dbManager.getStats();
        expect(stats.data.categories.count).toBe(3);
        expect(stats.data.tags.count).toBe(0);
        expect(stats.data.images.count).toBe(0);
        expect(stats.data.imageTags.count).toBe(0);
      }
    });

    it('重置操作应该在合理时间内完成', async () => {
      const db = dbManager.getDatabase();
      
      // 添加大量测试数据
      for (let i = 0; i < 100; i++) {
        db.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES ('tag-${i}-${Date.now()}', '标签${i}-${Date.now()}', '2024-01-01T00:00:00Z', '2024-01-01T00:00:00Z')
        `).run();
      }
      
      for (let i = 0; i < 50; i++) {
        db.prepare(`
          INSERT INTO images (id, category_id, image_url, created_at)
          VALUES ('img-${i}-${Date.now()}', 'magpie', 'url-${i}-${Date.now()}', '2024-01-01T00:00:00Z')
        `).run();
      }
      
      // 测量重置时间
      const startTime = Date.now();
      const resetResult = dbManager.resetDatabase();
      const endTime = Date.now();
      
      expect(resetResult.success).toBe(true);
      expect(endTime - startTime).toBeLessThan(5000); // 应该在5秒内完成
      
      // 验证数据被清理
      const stats = dbManager.getStats();
      expect(stats.data.categories.count).toBe(3);
      expect(stats.data.tags.count).toBe(0);
      expect(stats.data.images.count).toBe(0);
    });
  });
});