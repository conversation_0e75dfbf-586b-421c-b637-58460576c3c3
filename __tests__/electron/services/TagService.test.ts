import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { TagService } from '../../../electron/services/TagService';
import type { TagCreate } from '../../../schemas/tag';

describe('TagService', () => {
  let dbManager: DatabaseManager;
  let tagService: TagService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建模拟的DatabaseManager
    dbManager = {
      getDatabase: () => testDb,
      testConnection: vi.fn(),
      getStats: vi.fn(),
      close: vi.fn()
    } as any;

    tagService = new TagService(dbManager);

    // 静默控制台输出
    console.log = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('getAllTags', () => {
    it('应该返回所有标签', async () => {
      // 插入测试数据
      const testTags = TestDataGenerator.createMultipleTags(3);
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);

      testTags.forEach(tag => {
        insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);
      });

      const tags = await tagService.getAllTags();

      expect(tags).toHaveLength(3);
      expect(tags[0]).toHaveProperty('id');
      expect(tags[0]).toHaveProperty('name');
      expect(tags[0]).toHaveProperty('created_at');
      expect(tags[0]).toHaveProperty('updated_at');
    });

    it('应该按创建时间降序排列', async () => {
      const tag1 = TestDataGenerator.createTag({
        name: 'First Tag',
        created_at: '2024-01-01T00:00:00Z'
      });
      const tag2 = TestDataGenerator.createTag({
        name: 'Second Tag',
        created_at: '2024-01-02T00:00:00Z'
      });

      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);

      insertTag.run(tag1.id, tag1.name, tag1.created_at, tag1.updated_at);
      insertTag.run(tag2.id, tag2.name, tag2.created_at, tag2.updated_at);

      const tags = await tagService.getAllTags();

      expect(tags[0].name).toBe('Second Tag');
      expect(tags[1].name).toBe('First Tag');
    });

    it('应该返回空数组当没有标签时', async () => {
      const tags = await tagService.getAllTags();
      expect(tags).toHaveLength(0);
    });
  });

  describe('createTag', () => {
    it('应该成功创建新标签', async () => {
      const tagData: TagCreate = {
        name: 'Test Tag'
      };

      const result = await tagService.createTag(tagData);

      expect(result.id).toBeDefined();
      expect(result.name).toBe(tagData.name);
      expect(result.created_at).toBeDefined();
      expect(result.updated_at).toBeDefined();
    });

    it('应该为新标签生成UUID', async () => {
      const tagData: TagCreate = {
        name: 'Test Tag'
      };

      const result = await tagService.createTag(tagData);

      expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('应该返回已存在的标签而不是创建重复标签', async () => {
      const tagData: TagCreate = {
        name: 'Duplicate Tag'
      };

      const firstResult = await tagService.createTag(tagData);
      const secondResult = await tagService.createTag(tagData);

      expect(firstResult.id).toBe(secondResult.id);
      expect(firstResult.name).toBe(secondResult.name);

      // 验证数据库中只有一个标签
      const allTags = await tagService.getAllTags();
      expect(allTags).toHaveLength(1);
    });

    it('应该不区分大小写检查重复标签', async () => {
      const firstTag: TagCreate = { name: 'TestTag' };
      const secondTag: TagCreate = { name: 'testtag' };

      const firstResult = await tagService.createTag(firstTag);
      const secondResult = await tagService.createTag(secondTag);

      expect(firstResult.id).toBe(secondResult.id);
    });
  });

  describe('updateTag', () => {
    it('应该成功更新标签', async () => {
      // 先创建一个标签
      const originalTag = TestDataGenerator.createTag();
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      insertTag.run(originalTag.id, originalTag.name, originalTag.created_at, originalTag.updated_at);

      // 等待一小段时间确保时间戳不同
      await new Promise(resolve => setTimeout(resolve, 10));

      const updateData: Partial<TagCreate> = {
        name: 'Updated Tag Name'
      };

      const result = await tagService.updateTag(originalTag.id, updateData);

      expect(result.id).toBe(originalTag.id);
      expect(result.name).toBe(updateData.name);
      expect(new Date(result.updated_at).getTime()).toBeGreaterThan(new Date(originalTag.updated_at).getTime());
    });

    it('应该抛出错误当标签不存在时', async () => {
      const updateData: Partial<TagCreate> = {
        name: 'Non-existent Tag'
      };

      await expect(tagService.updateTag('non-existent-id', updateData))
        .rejects.toThrow('标签不存在: non-existent-id');
    });

    it('应该防止重复的标签名称', async () => {
      // 创建两个标签
      const tag1 = TestDataGenerator.createTag({ name: 'Tag One' });
      const tag2 = TestDataGenerator.createTag({ name: 'Tag Two' });

      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      insertTag.run(tag1.id, tag1.name, tag1.created_at, tag1.updated_at);
      insertTag.run(tag2.id, tag2.name, tag2.created_at, tag2.updated_at);

      // 尝试将tag2的名称改为tag1的名称
      const updateData: Partial<TagCreate> = {
        name: 'Tag One'
      };

      await expect(tagService.updateTag(tag2.id, updateData))
        .rejects.toThrow('标签名称已存在: Tag One');
    });

    it('应该允许更新为相同的名称', async () => {
      const tag = TestDataGenerator.createTag({ name: 'Same Name' });
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);

      const updateData: Partial<TagCreate> = {
        name: 'Same Name'
      };

      const result = await tagService.updateTag(tag.id, updateData);
      expect(result.name).toBe(updateData.name);
    });
  });

  describe('deleteTag', () => {
    it('应该成功删除标签', async () => {
      // 先创建一个标签
      const tag = TestDataGenerator.createTag();
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);

      await expect(tagService.deleteTag(tag.id)).resolves.toBeUndefined();

      // 验证标签已被删除
      const result = await tagService.getTagById(tag.id);
      expect(result).toBeNull();
    });

    it('应该抛出错误当标签不存在时', async () => {
      await expect(tagService.deleteTag('non-existent-id'))
        .rejects.toThrow('标签不存在: non-existent-id');
    });

    it('应该级联删除图片标签关联', async () => {
      // 创建分类、图片和标签
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id);
      const tag = TestDataGenerator.createTag();

      // 插入测试数据
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      const insertImageTag = testDb.prepare(`
        INSERT INTO image_tags (image_id, tag_id) VALUES (?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );
      insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);
      insertImageTag.run(image.id, tag.id);

      await tagService.deleteTag(tag.id);

      // 验证图片标签关联也被删除了
      const imageTags = testDb.prepare('SELECT * FROM image_tags WHERE tag_id = ?').all(tag.id);
      expect(imageTags).toHaveLength(0);
    });
  });

  describe('getTagById', () => {
    it('应该返回存在的标签', async () => {
      const tag = TestDataGenerator.createTag();
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);
      insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);

      const result = await tagService.getTagById(tag.id);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(tag.id);
      expect(result!.name).toBe(tag.name);
    });

    it('应该返回null当标签不存在时', async () => {
      const result = await tagService.getTagById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('searchTags', () => {
    beforeEach(async () => {
      // 插入测试标签
      const tags = [
        TestDataGenerator.createTag({ name: 'apple' }),
        TestDataGenerator.createTag({ name: 'application' }),
        TestDataGenerator.createTag({ name: 'banana' }),
        TestDataGenerator.createTag({ name: 'grape' }),
        TestDataGenerator.createTag({ name: 'pineapple' })
      ];

      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);

      tags.forEach(tag => {
        insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);
      });
    });

    it('应该返回精确匹配的标签在前', async () => {
      const results = await tagService.searchTags('apple');

      expect(results.length).toBeGreaterThan(0);
      expect(results[0].name).toBe('apple');
    });

    it('应该返回部分匹配的标签', async () => {
      const results = await tagService.searchTags('app');

      expect(results.length).toBeGreaterThanOrEqual(2);
      const names = results.map(tag => tag.name);
      expect(names).toContain('apple');
      expect(names).toContain('application');
    });

    it('应该不区分大小写搜索', async () => {
      const results = await tagService.searchTags('APPLE');

      expect(results.length).toBeGreaterThan(0);
      expect(results.some(tag => tag.name.toLowerCase().includes('apple'))).toBe(true);
    });

    it('应该返回所有标签当查询为空时', async () => {
      const results = await tagService.searchTags('');
      const allTags = await tagService.getAllTags();

      expect(results).toHaveLength(allTags.length);
    });

    it('应该返回空数组当没有匹配时', async () => {
      const results = await tagService.searchTags('nonexistent');
      expect(results).toHaveLength(0);
    });
  });

  describe('图片标签关联操作', () => {
    let category: any;
    let image: any;
    let tag: any;

    beforeEach(() => {
      category = TestDataGenerator.createCategory();
      image = TestDataGenerator.createImage(category.id);
      tag = TestDataGenerator.createTag();

      // 插入测试数据
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      const insertTag = testDb.prepare(`
        INSERT INTO tags (id, name, created_at, updated_at)
        VALUES (?, ?, ?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );
      insertTag.run(tag.id, tag.name, tag.created_at, tag.updated_at);
    });

    describe('addTagToImage', () => {
      it('应该成功为图片添加标签', async () => {
        await expect(tagService.addTagToImage(image.id, tag.id)).resolves.toBeUndefined();

        // 验证关联已创建
        const imageTags = testDb.prepare('SELECT * FROM image_tags WHERE image_id = ? AND tag_id = ?')
          .all(image.id, tag.id);
        expect(imageTags).toHaveLength(1);
      });

      it('应该防止重复的图片标签关联', async () => {
        await tagService.addTagToImage(image.id, tag.id);
        await tagService.addTagToImage(image.id, tag.id); // 第二次添加

        // 验证只有一个关联
        const imageTags = testDb.prepare('SELECT * FROM image_tags WHERE image_id = ? AND tag_id = ?')
          .all(image.id, tag.id);
        expect(imageTags).toHaveLength(1);
      });

      it('应该抛出错误当图片不存在时', async () => {
        await expect(tagService.addTagToImage('non-existent-image', tag.id))
          .rejects.toThrow('图片不存在: non-existent-image');
      });

      it('应该抛出错误当标签不存在时', async () => {
        await expect(tagService.addTagToImage(image.id, 'non-existent-tag'))
          .rejects.toThrow('标签不存在: non-existent-tag');
      });
    });

    describe('removeTagFromImage', () => {
      beforeEach(async () => {
        // 先添加标签关联
        await tagService.addTagToImage(image.id, tag.id);
      });

      it('应该成功从图片移除标签', async () => {
        await expect(tagService.removeTagFromImage(image.id, tag.id)).resolves.toBeUndefined();

        // 验证关联已删除
        const imageTags = testDb.prepare('SELECT * FROM image_tags WHERE image_id = ? AND tag_id = ?')
          .all(image.id, tag.id);
        expect(imageTags).toHaveLength(0);
      });

      it('应该处理不存在的关联', async () => {
        const anotherTag = TestDataGenerator.createTag();
        const insertTag = testDb.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `);
        insertTag.run(anotherTag.id, anotherTag.name, anotherTag.created_at, anotherTag.updated_at);

        await expect(tagService.removeTagFromImage(image.id, anotherTag.id))
          .resolves.toBeUndefined();
      });
    });

    describe('getTagsForImage', () => {
      it('应该返回图片的所有标签', async () => {
        // 添加多个标签
        const tag2 = TestDataGenerator.createTag();
        const insertTag = testDb.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `);
        insertTag.run(tag2.id, tag2.name, tag2.created_at, tag2.updated_at);

        await tagService.addTagToImage(image.id, tag.id);
        await tagService.addTagToImage(image.id, tag2.id);

        const result = await tagService.getTagsForImage(image.id);

        expect(result).toHaveLength(2);
        const tagIds = result.map(t => t.id);
        expect(tagIds).toContain(tag.id);
        expect(tagIds).toContain(tag2.id);
      });

      it('应该返回空数组当图片没有标签时', async () => {
        const result = await tagService.getTagsForImage(image.id);
        expect(result).toHaveLength(0);
      });
    });

    describe('searchImagesByTags', () => {
      beforeEach(async () => {
        // 为图片添加标签
        await tagService.addTagToImage(image.id, tag.id);
      });

      it('应该根据标签名称搜索图片', async () => {
        const result = await tagService.searchImagesByTags([tag.name]);

        expect(result).toHaveLength(1);
        expect(result[0]).toBe(image.id);
      });

      it('应该不区分大小写搜索', async () => {
        const result = await tagService.searchImagesByTags([tag.name.toUpperCase()]);

        expect(result).toHaveLength(1);
        expect(result[0]).toBe(image.id);
      });

      it('应该返回空数组当没有标签时', async () => {
        const result = await tagService.searchImagesByTags([]);
        expect(result).toHaveLength(0);
      });

      it('应该返回空数组当标签不存在时', async () => {
        const result = await tagService.searchImagesByTags(['nonexistent-tag']);
        expect(result).toHaveLength(0);
      });

      it('应该支持多个标签搜索', async () => {
        const tag2 = TestDataGenerator.createTag();
        const insertTag = testDb.prepare(`
          INSERT INTO tags (id, name, created_at, updated_at)
          VALUES (?, ?, ?, ?)
        `);
        insertTag.run(tag2.id, tag2.name, tag2.created_at, tag2.updated_at);

        await tagService.addTagToImage(image.id, tag2.id);

        const result = await tagService.searchImagesByTags([tag.name, tag2.name]);

        expect(result).toHaveLength(1);
        expect(result[0]).toBe(image.id);
      });
    });
  });
});