import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { OSSService } from '../../../electron/services/OSSService';
import { OSSConfig } from '../../../electron/services/SettingsService';

// Mock AWS SDK
const mockS3Client = {
  send: vi.fn(),
  destroy: vi.fn()
};

const mockUpload = {
  done: vi.fn()
};

vi.mock('@aws-sdk/client-s3', () => ({
  S3Client: vi.fn(() => mockS3Client),
  PutObjectCommand: vi.fn(),
  GetObjectCommand: vi.fn(),
  DeleteObjectCommand: vi.fn(),
  HeadObjectCommand: vi.fn(),
  ListObjectsV2Command: vi.fn()
}));

vi.mock('@aws-sdk/lib-storage', () => ({
  Upload: vi.fn(() => mockUpload)
}));

vi.mock('@aws-sdk/s3-request-presigner', () => ({
  getSignedUrl: vi.fn()
}));

describe('OSSService', () => {
  let ossService: OSSService;
  let mockConfig: OSSConfig;

  beforeEach(() => {
    // Reset all mocks
    vi.clearAllMocks();
    
    // Mock configuration
    mockConfig = {
      endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
      region: 'cn-hangzhou',
      accessKeyId: 'test-access-key',
      secretAccessKey: 'test-secret-key',
      bucket: 'test-bucket',
      pathPrefix: 'pokedex'
    };
    
    ossService = new OSSService(mockConfig);
  });

  afterEach(() => {
    if (ossService) {
      ossService.destroy();
    }
  });

  describe('constructor', () => {
    it('should initialize with config', () => {
      expect(ossService.isConfigured()).toBe(true);
      expect(ossService.getConfig()).toEqual(mockConfig);
    });

    it('should initialize without config', () => {
      const emptyService = new OSSService();
      expect(emptyService.isConfigured()).toBe(false);
      expect(emptyService.getConfig()).toBeNull();
    });
  });

  describe('updateConfig', () => {
    it('should update configuration successfully', () => {
      const newConfig: OSSConfig = {
        ...mockConfig,
        bucket: 'new-bucket'
      };
      
      ossService.updateConfig(newConfig);
      
      expect(ossService.getConfig()).toEqual(newConfig);
      expect(ossService.isConfigured()).toBe(true);
    });
  });

  describe('testConnection', () => {
    it('should test connection successfully', async () => {
      mockS3Client.send.mockResolvedValueOnce({
        Contents: []
      });

      const result = await ossService.testConnection();

      expect(result.success).toBe(true);
      expect(result.message).toBe('连接成功');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle connection failure', async () => {
      const error = new Error('Connection failed');
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.testConnection();

      expect(result.success).toBe(false);
      expect(result.message).toBe('连接失败: Connection failed');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const result = await unconfiguredService.testConnection();

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('uploadFile', () => {
    it('should upload file successfully', async () => {
      const fileBuffer = Buffer.from('test file content');
      const key = 'test-image.jpg';
      const mimeType = 'image/jpeg';
      
      mockUpload.done.mockResolvedValueOnce({});

      const result = await ossService.uploadFile(key, fileBuffer, mimeType);

      expect(result.success).toBe(true);
      expect(result.message).toBe('上传成功');
      expect(result.url).toBe(`${mockConfig.endpoint}/${mockConfig.bucket}/${mockConfig.pathPrefix}/${key}`);
      expect(mockUpload.done).toHaveBeenCalledTimes(1);
    });

    it('should handle upload failure', async () => {
      const fileBuffer = Buffer.from('test file content');
      const key = 'test-image.jpg';
      const mimeType = 'image/jpeg';
      const error = new Error('Upload failed');
      
      mockUpload.done.mockRejectedValueOnce(error);

      const result = await ossService.uploadFile(key, fileBuffer, mimeType);

      expect(result.success).toBe(false);
      expect(result.message).toBe('上传失败: Upload failed');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const fileBuffer = Buffer.from('test file content');
      const key = 'test-image.jpg';
      const mimeType = 'image/jpeg';

      const result = await unconfiguredService.uploadFile(key, fileBuffer, mimeType);

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('downloadFile', () => {
    it('should download file successfully', async () => {
      const key = 'test-image.jpg';
      const mockFileData = Buffer.from('test file content');
      
      // Mock stream data
      const mockStream = {
        async *[Symbol.asyncIterator]() {
          yield mockFileData;
        }
      };
      
      mockS3Client.send.mockResolvedValueOnce({
        Body: mockStream
      });

      const result = await ossService.downloadFile(key);

      expect(result.success).toBe(true);
      expect(result.message).toBe('下载成功');
      expect(result.data).toEqual(mockFileData);
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle download failure', async () => {
      const key = 'test-image.jpg';
      const error = new Error('Download failed');
      
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.downloadFile(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('下载失败: Download failed');
    });

    it('should handle empty file body', async () => {
      const key = 'test-image.jpg';
      
      mockS3Client.send.mockResolvedValueOnce({
        Body: null
      });

      const result = await ossService.downloadFile(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('文件内容为空');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const key = 'test-image.jpg';

      const result = await unconfiguredService.downloadFile(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('deleteFile', () => {
    it('should delete file successfully', async () => {
      const key = 'test-image.jpg';
      
      mockS3Client.send.mockResolvedValueOnce({});

      const result = await ossService.deleteFile(key);

      expect(result.success).toBe(true);
      expect(result.message).toBe('删除成功');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle delete failure', async () => {
      const key = 'test-image.jpg';
      const error = new Error('Delete failed');
      
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.deleteFile(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('删除失败: Delete failed');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const key = 'test-image.jpg';

      const result = await unconfiguredService.deleteFile(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('fileExists', () => {
    it('should check file exists successfully', async () => {
      const key = 'test-image.jpg';
      
      mockS3Client.send.mockResolvedValueOnce({});

      const result = await ossService.fileExists(key);

      expect(result.success).toBe(true);
      expect(result.exists).toBe(true);
      expect(result.message).toBe('文件存在');
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle file not found', async () => {
      const key = 'non-existent.jpg';
      const error = new Error('NotFound');
      error.name = 'NotFound';
      
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.fileExists(key);

      expect(result.success).toBe(true);
      expect(result.exists).toBe(false);
      expect(result.message).toBe('文件不存在');
    });

    it('should handle other errors', async () => {
      const key = 'test-image.jpg';
      const error = new Error('Access denied');
      
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.fileExists(key);

      expect(result.success).toBe(false);
      expect(result.exists).toBe(false);
      expect(result.message).toBe('检查失败: Access denied');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const key = 'test-image.jpg';

      const result = await unconfiguredService.fileExists(key);

      expect(result.success).toBe(false);
      expect(result.exists).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('listFiles', () => {
    it('should list files successfully', async () => {
      const prefix = 'images/';
      const mockFiles = [
        { Key: 'images/test1.jpg' },
        { Key: 'images/test2.png' }
      ];
      
      mockS3Client.send.mockResolvedValueOnce({
        Contents: mockFiles
      });

      const result = await ossService.listFiles(prefix);

      expect(result.success).toBe(true);
      expect(result.message).toBe('列表获取成功');
      expect(result.files).toEqual(['images/test1.jpg', 'images/test2.png']);
      expect(mockS3Client.send).toHaveBeenCalledTimes(1);
    });

    it('should handle empty file list', async () => {
      const prefix = 'images/';
      
      mockS3Client.send.mockResolvedValueOnce({
        Contents: []
      });

      const result = await ossService.listFiles(prefix);

      expect(result.success).toBe(true);
      expect(result.files).toEqual([]);
    });

    it('should handle list failure', async () => {
      const prefix = 'images/';
      const error = new Error('List failed');
      
      mockS3Client.send.mockRejectedValueOnce(error);

      const result = await ossService.listFiles(prefix);

      expect(result.success).toBe(false);
      expect(result.message).toBe('列表获取失败: List failed');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const prefix = 'images/';

      const result = await unconfiguredService.listFiles(prefix);

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('generateSignedUrl', () => {
    it('should generate signed URL successfully', async () => {
      const key = 'test-image.jpg';
      const mockSignedUrl = 'https://signed-url.example.com';
      
      const { getSignedUrl } = await import('@aws-sdk/s3-request-presigner');
      vi.mocked(getSignedUrl).mockResolvedValueOnce(mockSignedUrl);

      const result = await ossService.generateSignedUrl(key);

      expect(result.success).toBe(true);
      expect(result.message).toBe('签名URL生成成功');
      expect(result.url).toBe(mockSignedUrl);
      expect(vi.mocked(getSignedUrl)).toHaveBeenCalledTimes(1);
    });

    it('should handle signed URL generation failure', async () => {
      const key = 'test-image.jpg';
      const error = new Error('Signing failed');
      
      const { getSignedUrl } = await import('@aws-sdk/s3-request-presigner');
      vi.mocked(getSignedUrl).mockRejectedValueOnce(error);

      const result = await ossService.generateSignedUrl(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('签名URL生成失败: Signing failed');
    });

    it('should handle unconfigured service', async () => {
      const unconfiguredService = new OSSService();
      const key = 'test-image.jpg';

      const result = await unconfiguredService.generateSignedUrl(key);

      expect(result.success).toBe(false);
      expect(result.message).toBe('未配置OSS连接');
    });
  });

  describe('destroy', () => {
    it('should destroy client successfully', () => {
      expect(ossService.isConfigured()).toBe(true);
      
      ossService.destroy();
      
      expect(ossService.isConfigured()).toBe(false);
      expect(ossService.getConfig()).toBeNull();
      expect(mockS3Client.destroy).toHaveBeenCalledTimes(1);
    });

    it('should handle destroying unconfigured service', () => {
      const unconfiguredService = new OSSService();
      
      expect(() => unconfiguredService.destroy()).not.toThrow();
    });
  });

  describe('path prefix handling', () => {
    it('should handle config without path prefix', () => {
      const configWithoutPrefix = {
        ...mockConfig,
        pathPrefix: undefined
      };
      
      const serviceWithoutPrefix = new OSSService(configWithoutPrefix);
      expect(serviceWithoutPrefix.getConfig()?.pathPrefix).toBeUndefined();
    });

    it('should construct correct key with path prefix', async () => {
      const key = 'test-image.jpg';
      const fileBuffer = Buffer.from('test content');
      const mimeType = 'image/jpeg';
      
      mockUpload.done.mockResolvedValueOnce({});

      await ossService.uploadFile(key, fileBuffer, mimeType);

      expect(mockUpload.done).toHaveBeenCalledTimes(1);
      // The Upload constructor should have been called with the prefixed key
    });
  });
});