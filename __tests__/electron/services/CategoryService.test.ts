import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { CategoryService } from '../../../electron/services/CategoryService';
import type { CategoryCreate, CategoryUpdate } from '../../../schemas/category';

describe('CategoryService', () => {
  let dbManager: DatabaseManager;
  let categoryService: CategoryService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 使用测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建模拟的DatabaseManager
    dbManager = {
      getDatabase: () => testDb,
      getDatabasePath: vi.fn(() => '/mock/test/database.db'),
      testConnection: vi.fn(),
      getStats: vi.fn(),
      close: vi.fn()
    } as any;

    categoryService = new CategoryService(dbManager);

    // 静默控制台输出
    console.log = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('getCategories', () => {
    it('应该返回分页的分类列表', async () => {
      // 插入测试数据
      const testCategories = TestDataGenerator.createMultipleCategories(5);
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      testCategories.forEach(cat => {
        insertCategory.run(
          cat.id, cat.name, cat.description,
          cat.thumbnail_path, cat.thumbnail_url,
          cat.created_at, cat.updated_at
        );
      });

      const categories = await categoryService.getCategories(0, 3);

      expect(categories).toHaveLength(3);
      expect(categories[0]).toHaveProperty('id');
      expect(categories[0]).toHaveProperty('name');
      expect(categories[0]).toHaveProperty('created_at');
    });

    it('应该支持跳过和限制参数', async () => {
      // 插入测试数据
      const testCategories = TestDataGenerator.createMultipleCategories(10);
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      testCategories.forEach(cat => {
        insertCategory.run(
          cat.id, cat.name, cat.description,
          cat.thumbnail_path, cat.thumbnail_url,
          cat.created_at, cat.updated_at
        );
      });

      const firstPage = await categoryService.getCategories(0, 5);
      const secondPage = await categoryService.getCategories(5, 5);

      expect(firstPage).toHaveLength(5);
      expect(secondPage).toHaveLength(5);
      expect(firstPage[0].id).not.toBe(secondPage[0].id);
    });

    it('应该按创建时间降序排列', async () => {
      // 插入不同时间的测试数据
      const category1 = TestDataGenerator.createCategory({ 
        name: 'First Category',
        created_at: '2024-01-01T00:00:00Z'
      });
      const category2 = TestDataGenerator.createCategory({ 
        name: 'Second Category',
        created_at: '2024-01-02T00:00:00Z'
      });

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);

      insertCategory.run(
        category1.id, category1.name, category1.description,
        category1.thumbnail_path, category1.thumbnail_url,
        category1.created_at, category1.updated_at
      );
      insertCategory.run(
        category2.id, category2.name, category2.description,
        category2.thumbnail_path, category2.thumbnail_url,
        category2.created_at, category2.updated_at
      );

      const categories = await categoryService.getCategories();

      expect(categories[0].name).toBe('Second Category');
      expect(categories[1].name).toBe('First Category');
    });
  });

  describe('createCategory', () => {
    it('应该成功创建新分类', async () => {
      const categoryData: CategoryCreate = {
        name: 'Test Category',
        description: 'Test Description'
      };

      const result = await categoryService.createCategory(categoryData);

      expect(result.id).toBeDefined();
      expect(result.name).toBe(categoryData.name);
      expect(result.description).toBe(categoryData.description);
      expect(result.created_at).toBeDefined();
      expect(result.updated_at).toBeDefined();
      expect(result.thumbnail_path).toBeNull();
      expect(result.thumbnail_url).toBeNull();
    });

    it('应该为新分类生成UUID', async () => {
      const categoryData: CategoryCreate = {
        name: 'Test Category'
      };

      const result = await categoryService.createCategory(categoryData);

      expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('应该处理没有描述的分类', async () => {
      const categoryData: CategoryCreate = {
        name: 'Category Without Description'
      };

      const result = await categoryService.createCategory(categoryData);

      expect(result.name).toBe(categoryData.name);
      expect(result.description).toBeUndefined();
    });
  });

  describe('updateCategory', () => {
    it('应该成功更新存在的分类', async () => {
      // 先创建一个分类
      const originalCategory = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        originalCategory.id, originalCategory.name, originalCategory.description,
        originalCategory.thumbnail_path, originalCategory.thumbnail_url,
        originalCategory.created_at, originalCategory.updated_at
      );

      const updateData: CategoryUpdate = {
        name: 'Updated Name',
        description: 'Updated Description'
      };

      const result = await categoryService.updateCategory(originalCategory.id, updateData);

      expect(result.id).toBe(originalCategory.id);
      expect(result.name).toBe(updateData.name);
      expect(result.description).toBe(updateData.description);
      expect(new Date(result.updated_at).getTime()).toBeGreaterThanOrEqual(new Date(originalCategory.updated_at).getTime());
    });

    it('应该抛出错误当分类不存在时', async () => {
      const updateData: CategoryUpdate = {
        name: 'Non-existent Category'
      };

      await expect(categoryService.updateCategory('non-existent-id', updateData))
        .rejects.toThrow('分类不存在: non-existent-id');
    });

    it('应该支持部分更新', async () => {
      // 先创建一个分类
      const originalCategory = TestDataGenerator.createCategory({
        name: 'Original Name',
        description: 'Original Description'
      });
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        originalCategory.id, originalCategory.name, originalCategory.description,
        originalCategory.thumbnail_path, originalCategory.thumbnail_url,
        originalCategory.created_at, originalCategory.updated_at
      );

      // 只更新名称
      const updateData: CategoryUpdate = {
        name: 'Updated Name Only'
      };

      const result = await categoryService.updateCategory(originalCategory.id, updateData);

      expect(result.name).toBe(updateData.name);
      expect(result.description).toBe(originalCategory.description);
    });

    it('应该抛出错误当没有提供更新字段时', async () => {
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      await expect(categoryService.updateCategory(category.id, {}))
        .rejects.toThrow('没有提供需要更新的字段');
    });
  });

  describe('deleteCategory', () => {
    it('应该成功删除存在的分类', async () => {
      // 先创建一个分类
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const deleteResult = await categoryService.deleteCategory(category.id);
      expect(deleteResult.success).toBe(true);
      expect(deleteResult.message).toContain('删除成功');
      expect(deleteResult.details?.categoryDeleted).toBe(true);

      // 验证分类已被删除
      const result = await categoryService.getCategoryById(category.id);
      expect(result).toBeNull();
    });

    it('应该抛出错误当分类不存在时', async () => {
      const deleteResult = await categoryService.deleteCategory('non-existent-id');
      expect(deleteResult.success).toBe(false);
      expect(deleteResult.message).toContain('分类不存在: non-existent-id');
    });

    it('应该级联删除相关图片', async () => {
      // 创建分类和图片
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id);

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      await categoryService.deleteCategory(category.id);

      // 验证图片也被删除了
      const images = testDb.prepare('SELECT * FROM images WHERE category_id = ?').all(category.id);
      expect(images).toHaveLength(0);
    });
  });

  describe('getCategoryById', () => {
    it('应该返回存在的分类', async () => {
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const result = await categoryService.getCategoryById(category.id);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(category.id);
      expect(result!.name).toBe(category.name);
    });

    it('应该返回null当分类不存在时', async () => {
      const result = await categoryService.getCategoryById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('getCategoryWithImages', () => {
    it('应该返回分类及其图片', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const images = TestDataGenerator.createMultipleImages(category.id, 3);

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      images.forEach(image => {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename,
          image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
          image.mime_type, image.size_bytes, image.description, image.created_at,
          image.updated_at, image.file_metadata, image.exif_info,
          image.image_url, image.thumbnail_url
        );
      });

      const result = await categoryService.getCategoryWithImages(category.id);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(category.id);
      expect(result!.images).toHaveLength(3);
      expect(result!.images![0]).toHaveProperty('id');
      expect(result!.images![0]).toHaveProperty('tags');
      expect(result!.images![0]).toHaveProperty('exif_info');
    });

    it('应该返回null当分类不存在时', async () => {
      const result = await categoryService.getCategoryWithImages('non-existent-id');
      expect(result).toBeNull();
    });

    it('应该正确处理JSON字段', async () => {
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id, {
        file_metadata: JSON.stringify({ width: 1920, height: 1080 }),
        exif_info: JSON.stringify({ camera: 'Test Camera', iso: 100 })
      });

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      const result = await categoryService.getCategoryWithImages(category.id);

      expect(result!.images![0].file_metadata).toEqual({ width: 1920, height: 1080 });
      expect(result!.images![0].exif_info).toEqual({ camera: 'Test Camera', iso: 100 });
    });

    it('应该按创建时间降序排列图片', async () => {
      const category = TestDataGenerator.createCategory();
      const image1 = TestDataGenerator.createImage(category.id, {
        title: 'First Image',
        created_at: '2024-01-01T00:00:00Z'
      });
      const image2 = TestDataGenerator.createImage(category.id, {
        title: 'Second Image',
        created_at: '2024-01-02T00:00:00Z'
      });

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      [image1, image2].forEach(image => {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename,
          image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
          image.mime_type, image.size_bytes, image.description, image.created_at,
          image.updated_at, image.file_metadata, image.exif_info,
          image.image_url, image.thumbnail_url
        );
      });

      const result = await categoryService.getCategoryWithImages(category.id);

      expect(result!.images![0].title).toBe('Second Image');
      expect(result!.images![1].title).toBe('First Image');
    });
  });
});