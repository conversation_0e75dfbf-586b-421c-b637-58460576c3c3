import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { DatabaseSyncService } from '../../../electron/services/DatabaseSyncService';
import { OSSService } from '../../../electron/services/OSSService';
import { SettingsService } from '../../../electron/services/SettingsService';
import { DatabaseManager } from '../../../electron/database';

// Mock dependencies
vi.mock('../../../electron/services/OSSService');
vi.mock('../../../electron/services/SettingsService');
vi.mock('../../../electron/database');

describe('DatabaseSyncService - Backup Management', () => {
  let databaseSyncService: DatabaseSyncService;
  let mockOSSService: vi.Mocked<OSSService>;
  let mockSettingsService: vi.Mocked<SettingsService>;
  let mockDatabaseManager: vi.Mocked<DatabaseManager>;

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Create mock instances
    mockOSSService = {
      isConfigured: vi.fn(),
      fileExists: vi.fn(),
      deleteFile: vi.fn(),
      downloadFile: vi.fn(),
      uploadFile: vi.fn(),
      getConfig: vi.fn(),
      listFiles: vi.fn(),
    } as any;

    mockSettingsService = {
      getSettings: vi.fn(),
      saveSettings: vi.fn(),
    } as any;

    mockDatabaseManager = {
      getDatabasePath: vi.fn(),
      getDatabase: vi.fn(),
      createDatabaseBackup: vi.fn(),
      restoreFromBackup: vi.fn(),
    } as any;

    databaseSyncService = new DatabaseSyncService(
      mockOSSService,
      mockSettingsService,
      mockDatabaseManager
    );
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('deleteDatabaseBackup', () => {
    it('should successfully delete a backup', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockResolvedValue({ 
        success: true, 
        exists: true, 
        message: '文件存在' 
      });
      mockOSSService.deleteFile.mockResolvedValue({ 
        success: true, 
        message: '删除成功' 
      });

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: true,
        message: '备份删除成功'
      });
      expect(mockOSSService.fileExists).toHaveBeenCalledWith(`databases/${backupName}`);
      expect(mockOSSService.deleteFile).toHaveBeenCalledWith(`databases/${backupName}`);
    });

    it('should fail when OSS is not configured', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(false);

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '未配置OSS存储，无法执行删除操作'
      });
      expect(mockOSSService.fileExists).not.toHaveBeenCalled();
      expect(mockOSSService.deleteFile).not.toHaveBeenCalled();
    });

    it('should fail when backup file does not exist', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockResolvedValue({ 
        success: true, 
        exists: false, 
        message: '文件不存在' 
      });

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '指定的备份文件不存在'
      });
      expect(mockOSSService.fileExists).toHaveBeenCalledWith(`databases/${backupName}`);
      expect(mockOSSService.deleteFile).not.toHaveBeenCalled();
    });

    it('should fail when file exists check fails', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockResolvedValue({ 
        success: false, 
        exists: false, 
        message: '检查文件存在性失败' 
      });

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '指定的备份文件不存在'
      });
      expect(mockOSSService.deleteFile).not.toHaveBeenCalled();
    });

    it('should fail when delete operation fails', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockResolvedValue({ 
        success: true, 
        exists: true, 
        message: '文件存在' 
      });
      mockOSSService.deleteFile.mockResolvedValue({ 
        success: false, 
        message: '删除失败：权限不足' 
      });

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '删除失败: 删除失败：权限不足'
      });
      expect(mockOSSService.deleteFile).toHaveBeenCalledWith(`databases/${backupName}`);
    });

    it('should handle exceptions gracefully', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockRejectedValue(new Error('网络错误'));

      // Act
      const result = await databaseSyncService.deleteDatabaseBackup(backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '删除失败: 网络错误'
      });
    });
  });

  describe('renameDatabaseBackup', () => {
    it('should successfully rename a backup', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const mockBackupData = Buffer.from('fake database content');
      
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: false, message: '新文件不存在' });
      mockOSSService.downloadFile.mockResolvedValue({ 
        success: true, 
        data: mockBackupData, 
        message: '下载成功' 
      });
      mockOSSService.uploadFile.mockResolvedValue({ 
        success: true, 
        message: '上传成功' 
      });
      mockOSSService.deleteFile.mockResolvedValue({ 
        success: true, 
        message: '删除成功' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: true,
        message: '备份重命名成功'
      });
      expect(mockOSSService.fileExists).toHaveBeenCalledWith(`databases/${oldName}`);
      expect(mockOSSService.fileExists).toHaveBeenCalledWith(`databases/${newName}`);
      expect(mockOSSService.downloadFile).toHaveBeenCalledWith(`databases/${oldName}`);
      expect(mockOSSService.uploadFile).toHaveBeenCalledWith(`databases/${newName}`, mockBackupData, 'application/x-sqlite3');
      expect(mockOSSService.deleteFile).toHaveBeenCalledWith(`databases/${oldName}`);
    });

    it('should auto-add .db extension to new name', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name'; // No .db extension
      const expectedNewName = 'backup-my-custom-name.db';
      const mockBackupData = Buffer.from('fake database content');
      
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: false, message: '新文件不存在' });
      mockOSSService.downloadFile.mockResolvedValue({ 
        success: true, 
        data: mockBackupData, 
        message: '下载成功' 
      });
      mockOSSService.uploadFile.mockResolvedValue({ 
        success: true, 
        message: '上传成功' 
      });
      mockOSSService.deleteFile.mockResolvedValue({ 
        success: true, 
        message: '删除成功' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result.success).toBe(true);
      expect(mockOSSService.fileExists).toHaveBeenCalledWith(`databases/${expectedNewName}`);
      expect(mockOSSService.uploadFile).toHaveBeenCalledWith(`databases/${expectedNewName}`, mockBackupData, 'application/x-sqlite3');
    });

    it('should fail when OSS is not configured', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      mockOSSService.isConfigured.mockReturnValue(false);

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '未配置OSS存储，无法执行重命名操作'
      });
    });

    it('should fail when new name does not start with backup-', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'my-custom-name.db'; // Does not start with backup-
      mockOSSService.isConfigured.mockReturnValue(true);

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '新文件名必须以 "backup-" 开头'
      });
    });

    it('should fail when original file does not exist', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockResolvedValue({ 
        success: true, 
        exists: false, 
        message: '原文件不存在' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '原备份文件不存在'
      });
    });

    it('should fail when new file already exists', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: true, message: '新文件已存在' });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '新文件名已存在，请选择其他名称'
      });
    });

    it('should fail when download fails', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: false, message: '新文件不存在' });
      mockOSSService.downloadFile.mockResolvedValue({ 
        success: false, 
        message: '下载失败：网络错误' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '下载原文件失败: 下载失败：网络错误'
      });
    });

    it('should fail when upload fails', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const mockBackupData = Buffer.from('fake database content');
      
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: false, message: '新文件不存在' });
      mockOSSService.downloadFile.mockResolvedValue({ 
        success: true, 
        data: mockBackupData, 
        message: '下载成功' 
      });
      mockOSSService.uploadFile.mockResolvedValue({ 
        success: false, 
        message: '上传失败：权限不足' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '上传新文件失败: 上传失败：权限不足'
      });
    });

    it('should succeed even when delete original file fails', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const mockBackupData = Buffer.from('fake database content');
      
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists
        .mockResolvedValueOnce({ success: true, exists: true, message: '原文件存在' })
        .mockResolvedValueOnce({ success: true, exists: false, message: '新文件不存在' });
      mockOSSService.downloadFile.mockResolvedValue({ 
        success: true, 
        data: mockBackupData, 
        message: '下载成功' 
      });
      mockOSSService.uploadFile.mockResolvedValue({ 
        success: true, 
        message: '上传成功' 
      });
      mockOSSService.deleteFile.mockResolvedValue({ 
        success: false, 
        message: '删除失败：权限不足' 
      });

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: true,
        message: '备份重命名成功'
      });
    });

    it('should handle exceptions gracefully', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      mockOSSService.isConfigured.mockReturnValue(true);
      mockOSSService.fileExists.mockRejectedValue(new Error('网络错误'));

      // Act
      const result = await databaseSyncService.renameDatabaseBackup(oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '重命名失败: 网络错误'
      });
    });
  });
});