import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ImageService, DeleteResult, BatchDeleteResult } from '../../../electron/services/ImageService';
import { DatabaseManager } from '../../../electron/database';
import { SettingsService } from '../../../electron/services/SettingsService';
import { TestDataGenerator } from '../helpers/test-data-generator';
import { createTestDatabase, cleanupTestDatabase, mockDatabaseManager } from '../helpers/test-database';
import Database from 'better-sqlite3';
import * as fs from 'fs';
import * as path from 'path';

// Mock Electron modules
vi.mock('electron', async () => {
  const { createElectronMocks } = await import('../helpers/electron-mocks');
  return createElectronMocks();
});

// Mock fs operations for testing
vi.mock('fs');
const mockFs = vi.mocked(fs);

describe('ImageService - 删除功能测试', () => {
  let imageService: ImageService;
  let dbManager: DatabaseManager;
  let settingsService: SettingsService;
  let testDb: Database.Database;
  let tempStorageDir: string;

  beforeEach(async () => {
    // 设置测试数据库
    const testDbSetup = createTestDatabase();
    testDb = testDbSetup.db;
    dbManager = mockDatabaseManager(testDb) as any;
    
    // 设置临时存储目录
    tempStorageDir = path.join(__dirname, '../../../test-temp/delete-test');
    
    // Mock SettingsService
    settingsService = {
      getStoragePath: vi.fn(() => tempStorageDir),
      usesCategoryFolders: vi.fn(() => true),
      getSettings: vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'local'
      })),
      saveSettings: vi.fn(() => true),
      getOSSConfig: vi.fn(() => null), // OSS未配置
      updateOSSConfig: vi.fn(() => true),
      clearOSSConfig: vi.fn(() => true),
      getStorageType: vi.fn(() => 'local')
    } as any;

    // 创建ImageService实例
    imageService = new ImageService(dbManager, settingsService);

    // Mock fs.existsSync 和 fs.unlinkSync
    mockFs.existsSync.mockReturnValue(true);
    mockFs.unlinkSync.mockImplementation(() => {});
    
    console.log('✅ 删除功能测试环境初始化完成');
  });

  afterEach(async () => {
    cleanupTestDatabase();
    vi.clearAllMocks();
  });

  describe('validateDeleteConditions', () => {
    it('应该验证存在的图片', async () => {
      // 创建测试分类和图片
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.validateDeleteConditions(image.id);

      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.image).toBeDefined();
      expect(result.image.id).toBe(image.id);
    });

    it('应该拒绝不存在的图片', async () => {
      const result = await imageService.validateDeleteConditions('non-existent-id');

      expect(result.valid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0]).toContain('数据库中没有找到要删除的图片记录');
      expect(result.image).toBeUndefined();
    });

    it('应该检查本地文件存在性', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      // Mock文件不存在
      mockFs.existsSync.mockReturnValue(false);

      const result = await imageService.validateDeleteConditions(image.id);

      expect(result.valid).toBe(true); // 验证通过，但有警告
      expect(result.warnings.length).toBeGreaterThan(0);
      expect(result.warnings.some(w => w.includes('原图文件不存在'))).toBe(true);
    });
  });

  describe('deleteImage - 本地存储', () => {
    it('应该成功删除本地存储的图片', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(true);
      expect(result.imageId).toBe(image.id);
      expect(result.storageDeleted).toBe(true);
      expect(result.databaseDeleted).toBe(true);
      expect(result.details?.storageType).toBe('local');

      // 验证数据库记录已删除
      const deletedImage = await imageService.getImageById(image.id);
      expect(deletedImage).toBeNull();

      // 验证文件删除被调用
      expect(mockFs.unlinkSync).toHaveBeenCalledTimes(2); // 原图和缩略图
    });

    it('应该处理文件删除失败的情况', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at, 
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      // Mock文件删除失败
      mockFs.unlinkSync.mockImplementation(() => {
        throw new Error('文件删除失败');
      });

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true); // 数据库删除应该成功
      expect(result.error).toContain('文件删除失败');
    });

    it('应该处理不存在的图片', async () => {
      const result = await imageService.deleteImage('non-existent-id');

      expect(result.success).toBe(false);
      expect(result.error).toContain('删除验证失败');
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(false);
    });
  });

  describe('deleteImage - OSS存储', () => {
    beforeEach(() => {
      // Mock OSS存储模式
      settingsService.getSettings = vi.fn(() => ({
        storagePath: tempStorageDir,
        usesCategoryFolders: true,
        storageType: 'oss' as 'oss',
        isFirstTimeSetup: false,
        lastMigrationVersion: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }));
      settingsService.getStorageType = vi.fn(() => 'oss' as 'oss');

      // 重新创建ImageService以使用OSS模式
      imageService = new ImageService(dbManager, settingsService);

      // Mock OSS服务
      const mockOSSService = {
        isConfigured: vi.fn(() => true),
        deleteFile: vi.fn(() => Promise.resolve({ success: true }))
      };

      // 注入mock OSS服务
      (imageService as any).ossService = mockOSSService;
    });

    it('应该成功删除OSS存储的图片', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at,
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(true);
      expect(result.storageDeleted).toBe(true);
      expect(result.databaseDeleted).toBe(true);
      expect(result.details?.storageType).toBe('oss');

      // 验证OSS删除被调用
      const ossService = (imageService as any).ossService;
      expect(ossService.deleteFile).toHaveBeenCalledTimes(2); // 原图和缩略图
    });

    it('应该处理OSS删除失败的情况', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at,
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      // Mock OSS删除失败
      const ossService = (imageService as any).ossService;
      ossService.deleteFile.mockRejectedValue(new Error('OSS删除失败'));

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true); // 数据库删除应该成功
      expect(result.error).toContain('OSS删除失败');
    });

    it('应该处理OSS未配置的情况', async () => {
      // Mock OSS未配置
      const ossService = (imageService as any).ossService;
      ossService.isConfigured.mockReturnValue(false);

      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const image = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
        image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
        image.description, image.created_at, image.updated_at,
        JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
      );

      const result = await imageService.deleteImage(image.id);

      expect(result.success).toBe(false);
      expect(result.storageDeleted).toBe(false);
      expect(result.databaseDeleted).toBe(true);
      expect(result.error).toContain('OSS未配置');
    });
  });

  describe('deleteImages - 批量删除', () => {
    it('应该成功批量删除多张图片', async () => {
      // 创建测试分类
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      // 创建多张测试图片
      const images = [
        TestDataGenerator.createImage(category.id),
        TestDataGenerator.createImage(category.id),
        TestDataGenerator.createImage(category.id)
      ];

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      for (const image of images) {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename, image.stored_filename,
          image.relative_file_path, image.relative_thumbnail_path, image.mime_type, image.size_bytes,
          image.description, image.created_at, image.updated_at,
          JSON.stringify(image.file_metadata), image.image_url, image.thumbnail_url
        );
      }

      const imageIds = images.map(img => img.id);
      const result = await imageService.deleteImages(imageIds);

      expect(result.totalCount).toBe(3);
      expect(result.successCount).toBe(3);
      expect(result.failedCount).toBe(0);
      expect(result.results).toHaveLength(3);

      // 验证所有图片都被删除
      for (const imageId of imageIds) {
        const deletedImage = await imageService.getImageById(imageId);
        expect(deletedImage).toBeNull();
      }
    });

    it('应该处理空数组', async () => {
      const result = await imageService.deleteImages([]);

      expect(result.totalCount).toBe(0);
      expect(result.successCount).toBe(0);
      expect(result.failedCount).toBe(0);
      expect(result.results).toHaveLength(0);
    });

    it('应该处理部分图片不存在的情况', async () => {
      // 创建一张存在的图片
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const existingImage = TestDataGenerator.createImage(category.id);
      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename,
          relative_file_path, relative_thumbnail_path, mime_type, size_bytes,
          description, created_at, updated_at, file_metadata, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        existingImage.id, existingImage.category_id, existingImage.title, existingImage.original_filename, existingImage.stored_filename,
        existingImage.relative_file_path, existingImage.relative_thumbnail_path, existingImage.mime_type, existingImage.size_bytes,
        existingImage.description, existingImage.created_at, existingImage.updated_at,
        JSON.stringify(existingImage.file_metadata), existingImage.image_url, existingImage.thumbnail_url
      );

      const imageIds = [existingImage.id, 'non-existent-1', 'non-existent-2'];
      const result = await imageService.deleteImages(imageIds);

      expect(result.totalCount).toBe(3);
      expect(result.successCount).toBe(1);
      expect(result.failedCount).toBe(2);
      expect(result.errors.length).toBeGreaterThan(0);

      // 验证存在的图片被删除
      const deletedImage = await imageService.getImageById(existingImage.id);
      expect(deletedImage).toBeNull();
    });
  });
});
