import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { TestDatabaseManager } from '../helpers/test-database';
import { TestDataGenerator } from '../helpers/test-data-generator';
import * as fs from 'fs';
import * as path from 'path';

// Mock electron模块 - 必须在导入前
vi.mock('electron', () => ({
  app: {
    getPath: vi.fn(() => '/tmp/test-userData'),
    quit: vi.fn(),
    exit: vi.fn(),
    isReady: vi.fn(() => true),
    whenReady: vi.fn(() => Promise.resolve()),
    on: vi.fn(),
    once: vi.fn(),
    emit: vi.fn()
  }
}));

// Mock fs模块
vi.mock('fs', () => ({
  existsSync: vi.fn(),
  mkdirSync: vi.fn(),
  writeFileSync: vi.fn(),
  unlinkSync: vi.fn(),
  readFileSync: vi.fn(),
  statSync: vi.fn(() => ({ size: 1024000 })),
  copyFileSync: vi.fn(),
  promises: {
    writeFile: vi.fn(),
    unlink: vi.fn(),
    stat: vi.fn(() => Promise.resolve({ size: 1024000 })),
    copyFile: vi.fn()
  }
}));

import { DatabaseManager } from '../../../electron/database/index';
import { ImageService } from '../../../electron/services/ImageService';
import { SettingsService } from '../../../electron/services/SettingsService';
import type { ImageUpdate } from '../../../schemas/image';

describe('ImageService', () => {
  let dbManager: DatabaseManager;
  let imageService: ImageService;
  let settingsService: SettingsService;
  let testDb: any;
  let cleanup: (() => void) | null = null;

  beforeEach(() => {
    // 重置所有mocks
    vi.clearAllMocks();

    // 设置fs mocks的默认行为
    vi.mocked(fs.existsSync).mockReturnValue(true);
    vi.mocked(fs.mkdirSync).mockImplementation(() => {});
    vi.mocked(fs.writeFileSync).mockImplementation(() => {});
    vi.mocked(fs.unlinkSync).mockImplementation(() => {});
    vi.mocked(fs.readFileSync).mockReturnValue(Buffer.from('test image data'));

    // 使用测试数据库
    const testDbManager = TestDatabaseManager.getInstance();
    const testDatabase = testDbManager.createTestDatabase();
    testDb = testDatabase.db;
    cleanup = testDatabase.cleanup;

    // 创建模拟的DatabaseManager
    dbManager = {
      getDatabase: () => testDb,
      testConnection: vi.fn(),
      getStats: vi.fn(),
      close: vi.fn()
    } as any;

    // 创建模拟的SettingsService
    settingsService = {
      getStoragePath: vi.fn(() => '/tmp/test-storage'),
      usesCategoryFolders: vi.fn(() => false),
      getSettings: vi.fn(),
      saveSettings: vi.fn(),
      getOSSConfig: vi.fn(() => undefined),
      getStorageType: vi.fn(() => 'local')
    } as any;

    imageService = new ImageService(dbManager, settingsService);

    // 静默控制台输出
    console.log = vi.fn();
    console.warn = vi.fn();
    console.error = vi.fn();
  });

  afterEach(() => {
    if (cleanup) {
      cleanup();
      cleanup = null;
    }
  });

  describe('构造函数和初始化', () => {
    it('应该成功创建ImageService实例', () => {
      expect(imageService).toBeInstanceOf(ImageService);
    });


    it('应该使用分类文件夹结构', () => {
      const service = new ImageService(dbManager, settingsService);
      expect(service).toBeInstanceOf(ImageService);
    });

    it('应该在没有SettingsService时使用默认配置', () => {
      const service = new ImageService(dbManager);
      expect(service).toBeInstanceOf(ImageService);
    });
  });

  describe('getImagesByCategoryId', () => {
    it('应该返回指定分类的图片列表', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const images = TestDataGenerator.createMultipleImages(category.id, 3);
      const tags = TestDataGenerator.createMultipleTags(2);

      // 插入测试数据
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      images.forEach(image => {
        insertImage.run(
          image.id, image.category_id, image.title, image.original_filename,
          image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
          image.mime_type, image.size_bytes, image.description, image.created_at,
          image.updated_at, image.file_metadata, image.exif_info,
          image.image_url, image.thumbnail_url
        );
      });

      const result = await imageService.getImagesByCategoryId(category.id);

      expect(result).toHaveLength(3);
      expect(result[0]).toHaveProperty('id');
      expect(result[0]).toHaveProperty('category_id', category.id);
      expect(result[0]).toHaveProperty('tags');
    });

    it('应该返回空数组当分类不存在时', async () => {
      const result = await imageService.getImagesByCategoryId('non-existent-category');
      expect(result).toHaveLength(0);
    });

    it('应该正确解析JSON字段', async () => {
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id, {
        file_metadata: JSON.stringify({ width: 1920, height: 1080 }),
        exif_info: JSON.stringify({ camera: 'Test Camera', iso: 100 })
      });

      // 插入测试数据
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      const result = await imageService.getImagesByCategoryId(category.id);

      expect(result[0].file_metadata).toEqual({ width: 1920, height: 1080 });
      expect(result[0].exif_info).toEqual({ camera: 'Test Camera', iso: 100 });
    });
  });

  describe('getImageById', () => {
    it('应该返回指定ID的图片', async () => {
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id);

      // 插入测试数据
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      const result = await imageService.getImageById(image.id);

      expect(result).not.toBeNull();
      expect(result!.id).toBe(image.id);
      expect(result!.category_id).toBe(category.id);
    });

    it('应该返回null当图片不存在时', async () => {
      const result = await imageService.getImageById('non-existent-id');
      expect(result).toBeNull();
    });
  });

  describe('uploadImage', () => {
    it('应该成功上传图片', async () => {
      // 创建分类
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const fileBuffer = Buffer.from('test image data');
      const originalFilename = 'test-image.jpg';
      const mimeType = 'image/jpeg';

      const result = await imageService.uploadImage(category.id, fileBuffer, originalFilename, mimeType);

      expect(result).toHaveProperty('id');
      expect(result.category_id).toBe(category.id);
      expect(result.original_filename).toBe(originalFilename);
      expect(result.mime_type).toBe(mimeType);
      expect(result.size_bytes).toBe(fileBuffer.length);
    });

    it('应该为上传的图片生成UUID', async () => {
      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const fileBuffer = Buffer.from('test image data');
      const result = await imageService.uploadImage(category.id, fileBuffer, 'test.jpg', 'image/jpeg');

      expect(result.id).toMatch(/^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i);
    });

    it('应该创建必需的目录', async () => {
      vi.mocked(fs.existsSync).mockReturnValue(false);

      const category = TestDataGenerator.createCategory();
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const fileBuffer = Buffer.from('test image data');
      await imageService.uploadImage(category.id, fileBuffer, 'test.jpg', 'image/jpeg');

      expect(fs.mkdirSync).toHaveBeenCalled();
    });

    it('应该在上传时设置为分类缩略图', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      
      // 插入分类到数据库
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const fileBuffer = Buffer.from('test image data');
      
      // 上传图片并设置为分类缩略图
      const uploadedImage = await imageService.uploadImage(category.id, fileBuffer, 'test.jpg', 'image/jpeg', true);
      
      // 验证图片上传成功
      expect(uploadedImage.id).toBeDefined();
      expect(uploadedImage.category_id).toBe(category.id);
      
      // 验证分类缩略图已设置
      const updatedCategory = testDb.prepare('SELECT * FROM categories WHERE id = ?').get(category.id) as any;
      expect(updatedCategory.thumbnail_path).toBe(uploadedImage.relative_thumbnail_path);
      expect(updatedCategory.thumbnail_url).toBe(uploadedImage.thumbnail_url);
    });

    it('应该在上传时不设置为分类缩略图（默认情况）', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      
      // 插入分类到数据库
      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const fileBuffer = Buffer.from('test image data');
      
      // 上传图片，不设置为分类缩略图
      const uploadedImage = await imageService.uploadImage(category.id, fileBuffer, 'test.jpg', 'image/jpeg', false);
      
      // 验证图片上传成功
      expect(uploadedImage.id).toBeDefined();
      expect(uploadedImage.category_id).toBe(category.id);
      
      // 验证分类缩略图未被更改
      const updatedCategory = testDb.prepare('SELECT * FROM categories WHERE id = ?').get(category.id) as any;
      expect(updatedCategory.thumbnail_path).toBe(category.thumbnail_path);
      expect(updatedCategory.thumbnail_url).toBe(category.thumbnail_url);
    });
  });

  describe('updateImage', () => {
    it('应该成功更新图片信息', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id);

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      const updateData: ImageUpdate = {
        title: 'Updated Title',
        description: 'Updated Description'
      };

      const result = await imageService.updateImage(image.id, updateData);

      expect(result.id).toBe(image.id);
      expect(result.title).toBe(updateData.title);
      expect(result.description).toBe(updateData.description);
    });

    it('应该抛出错误当图片不存在时', async () => {
      const updateData: ImageUpdate = {
        title: 'Non-existent Image'
      };

      await expect(imageService.updateImage('non-existent-id', updateData))
        .rejects.toThrow();
    });
  });

  describe('deleteImage', () => {
    it('应该成功删除图片和文件', async () => {
      // 创建测试数据
      const category = TestDataGenerator.createCategory();
      const image = TestDataGenerator.createImage(category.id);

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      const insertImage = testDb.prepare(`
        INSERT INTO images (id, category_id, title, original_filename, stored_filename, relative_file_path, 
                           relative_thumbnail_path, mime_type, size_bytes, description, created_at, updated_at,
                           file_metadata, exif_info, image_url, thumbnail_url)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);
      insertImage.run(
        image.id, image.category_id, image.title, image.original_filename,
        image.stored_filename, image.relative_file_path, image.relative_thumbnail_path,
        image.mime_type, image.size_bytes, image.description, image.created_at,
        image.updated_at, image.file_metadata, image.exif_info,
        image.image_url, image.thumbnail_url
      );

      const deleteResult = await imageService.deleteImage(image.id);
      expect(deleteResult.success).toBe(true);
      expect(deleteResult.imageId).toBe(image.id);
      expect(deleteResult.storageDeleted).toBe(true);
      expect(deleteResult.databaseDeleted).toBe(true);

      // 验证图片已被删除
      const result = await imageService.getImageById(image.id);
      expect(result).toBeNull();
    });

    it('应该抛出错误当图片不存在时', async () => {
      const deleteResult = await imageService.deleteImage('non-existent-id');
      expect(deleteResult.success).toBe(false);
      expect(deleteResult.imageId).toBe('non-existent-id');
      expect(deleteResult.storageDeleted).toBe(false);
      expect(deleteResult.databaseDeleted).toBe(false);
      expect(deleteResult.error).toContain('数据库中没有找到要删除的图片记录');
    });
  });

  describe('文件夹名称处理', () => {
    it('应该正确清理文件夹名称', () => {
      // 通过创建一个具有特殊字符的分类来测试sanitizeFolderName
      const category = TestDataGenerator.createCategory({
        name: 'Test/Category<>:"\\|?*  With   Spaces'
      });

      const insertCategory = testDb.prepare(`
        INSERT INTO categories (id, name, description, thumbnail_path, thumbnail_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `);
      insertCategory.run(
        category.id, category.name, category.description,
        category.thumbnail_path, category.thumbnail_url,
        category.created_at, category.updated_at
      );

      // 通过尝试上传图片来触发getCategoryName的调用
      vi.mocked(settingsService.usesCategoryFolders).mockReturnValue(true);
      const service = new ImageService(dbManager, settingsService);

      const fileBuffer = Buffer.from('test');
      // 这个操作会内部调用getCategoryName，进而调用sanitizeFolderName
      expect(() => service.uploadImage(category.id, fileBuffer, 'test.jpg', 'image/jpeg'))
        .not.toThrow();
    });

    it('应该处理不存在的分类', async () => {
      vi.mocked(settingsService.usesCategoryFolders).mockReturnValue(true);
      const service = new ImageService(dbManager, settingsService);

      const fileBuffer = Buffer.from('test');
      // 使用不存在的分类ID - 这会导致外键约束错误
      await expect(service.uploadImage('non-existent-category', fileBuffer, 'test.jpg', 'image/jpeg'))
        .rejects.toThrow(); // 应该抛出外键约束错误
    });
  });
});