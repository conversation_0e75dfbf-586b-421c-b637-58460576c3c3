import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { ipcMain } from 'electron';
import { DatabaseSyncService } from '../../../electron/services/DatabaseSyncService';

// Mock electron
vi.mock('electron', () => ({
  ipcMain: {
    handle: vi.fn(),
    removeHandler: vi.fn(),
  },
  app: {
    getPath: vi.fn(() => '/mock/path'),
  },
}));

// Mock DatabaseSyncService
vi.mock('../../../electron/services/DatabaseSyncService');

describe('IPC Handlers - Database Backup Management', () => {
  let mockDatabaseSyncService: vi.Mocked<DatabaseSyncService>;
  let ipcHandlers: { [key: string]: Function };

  beforeEach(() => {
    vi.clearAllMocks();
    
    // Mock DatabaseSyncService
    mockDatabaseSyncService = {
      deleteDatabaseBackup: vi.fn(),
      renameDatabaseBackup: vi.fn(),
    } as any;

    // Capture IPC handlers
    ipcHandlers = {};
    (ipcMain.handle as any).mockImplementation((channel: string, handler: Function) => {
      ipcHandlers[channel] = handler;
    });

    // Setup the global services mock
    (global as any).databaseSyncService = mockDatabaseSyncService;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('delete-database-backup handler', () => {
    it('should handle successful backup deletion', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      const expectedResult = {
        success: true,
        message: '备份删除成功'
      };
      
      mockDatabaseSyncService.deleteDatabaseBackup.mockResolvedValue(expectedResult);

      // Simulate handler registration
      const handler = (_, backupName: string) => mockDatabaseSyncService.deleteDatabaseBackup(backupName);
      ipcHandlers['delete-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['delete-database-backup'](null, backupName);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockDatabaseSyncService.deleteDatabaseBackup).toHaveBeenCalledWith(backupName);
    });

    it('should handle backup deletion failure', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      const expectedResult = {
        success: false,
        message: '删除失败: 文件不存在'
      };
      
      mockDatabaseSyncService.deleteDatabaseBackup.mockResolvedValue(expectedResult);

      // Simulate handler registration
      const handler = (_, backupName: string) => mockDatabaseSyncService.deleteDatabaseBackup(backupName);
      ipcHandlers['delete-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['delete-database-backup'](null, backupName);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockDatabaseSyncService.deleteDatabaseBackup).toHaveBeenCalledWith(backupName);
    });

    it('should handle exceptions in delete handler', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      const error = new Error('Network error');
      
      mockDatabaseSyncService.deleteDatabaseBackup.mockRejectedValue(error);

      // Simulate handler registration
      const handler = async (_, backupName: string) => {
        try {
          return await mockDatabaseSyncService.deleteDatabaseBackup(backupName);
        } catch (error) {
          return {
            success: false,
            message: `删除失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };
      ipcHandlers['delete-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['delete-database-backup'](null, backupName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '删除失败: Network error'
      });
      expect(mockDatabaseSyncService.deleteDatabaseBackup).toHaveBeenCalledWith(backupName);
    });
  });

  describe('rename-database-backup handler', () => {
    it('should handle successful backup rename', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const expectedResult = {
        success: true,
        message: '备份重命名成功'
      };
      
      mockDatabaseSyncService.renameDatabaseBackup.mockResolvedValue(expectedResult);

      // Simulate handler registration
      const handler = (_, oldName: string, newName: string) => 
        mockDatabaseSyncService.renameDatabaseBackup(oldName, newName);
      ipcHandlers['rename-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['rename-database-backup'](null, oldName, newName);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockDatabaseSyncService.renameDatabaseBackup).toHaveBeenCalledWith(oldName, newName);
    });

    it('should handle backup rename failure', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const expectedResult = {
        success: false,
        message: '重命名失败: 新文件名已存在'
      };
      
      mockDatabaseSyncService.renameDatabaseBackup.mockResolvedValue(expectedResult);

      // Simulate handler registration
      const handler = (_, oldName: string, newName: string) => 
        mockDatabaseSyncService.renameDatabaseBackup(oldName, newName);
      ipcHandlers['rename-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['rename-database-backup'](null, oldName, newName);

      // Assert
      expect(result).toEqual(expectedResult);
      expect(mockDatabaseSyncService.renameDatabaseBackup).toHaveBeenCalledWith(oldName, newName);
    });

    it('should handle exceptions in rename handler', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'backup-my-custom-name.db';
      const error = new Error('Network error');
      
      mockDatabaseSyncService.renameDatabaseBackup.mockRejectedValue(error);

      // Simulate handler registration
      const handler = async (_, oldName: string, newName: string) => {
        try {
          return await mockDatabaseSyncService.renameDatabaseBackup(oldName, newName);
        } catch (error) {
          return {
            success: false,
            message: `重命名失败: ${error instanceof Error ? error.message : String(error)}`
          };
        }
      };
      ipcHandlers['rename-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['rename-database-backup'](null, oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '重命名失败: Network error'
      });
      expect(mockDatabaseSyncService.renameDatabaseBackup).toHaveBeenCalledWith(oldName, newName);
    });

    it('should handle invalid parameters', async () => {
      // Arrange
      const oldName = '';
      const newName = '';
      
      // Simulate handler registration
      const handler = async (_, oldName: string, newName: string) => {
        if (!oldName || !newName) {
          return {
            success: false,
            message: '参数不能为空'
          };
        }
        return await mockDatabaseSyncService.renameDatabaseBackup(oldName, newName);
      };
      ipcHandlers['rename-database-backup'] = handler;

      // Act
      const result = await ipcHandlers['rename-database-backup'](null, oldName, newName);

      // Assert
      expect(result).toEqual({
        success: false,
        message: '参数不能为空'
      });
      expect(mockDatabaseSyncService.renameDatabaseBackup).not.toHaveBeenCalled();
    });
  });
});