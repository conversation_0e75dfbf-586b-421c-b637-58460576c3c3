import { describe, it, expect, beforeEach, vi } from 'vitest';

// Mock electron模块
const mockIpcRenderer = {
  invoke: vi.fn(),
  on: vi.fn(),
  removeAllListeners: vi.fn()
};

const mockContextBridge = {
  exposeInMainWorld: vi.fn()
};

// Mock process globals
const originalProcess = global.process;

beforeEach(() => {
  // 重置process.platform
  global.process = {
    ...originalProcess,
    platform: 'linux',
    env: {
      NODE_ENV: 'test'
    }
  } as any;
});

vi.mock('electron', () => ({
  contextBridge: mockContextBridge,
  ipcRenderer: mockIpcRenderer
}));

describe('Preload Script API Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础功能测试', () => {
    it('应该正确模拟contextBridge.exposeInMainWorld调用', () => {
      // 验证mock设置正确
      expect(mockContextBridge.exposeInMainWorld).toBeDefined();
      expect(mockIpcRenderer.invoke).toBeDefined();
    });

    it('应该能够创建electronAPI对象结构', () => {
      // 模拟preload脚本的核心功能
      const electronAPI = {
        isElectron: true,
        platform: global.process.platform,
        
        // 数据库方法
        testDatabase: () => mockIpcRenderer.invoke('test-database'),
        getDatabaseStats: () => mockIpcRenderer.invoke('get-database-stats'),
        
        // 分类方法  
        getCategories: (skip?: number, limit?: number) => 
          mockIpcRenderer.invoke('get-categories', skip, limit),
        createCategory: (categoryData: any) => 
          mockIpcRenderer.invoke('create-category', categoryData),
        
        // 图片方法
        uploadImage: (categoryId: string, fileBuffer: any, originalFilename: string, mimeType: string) => 
          mockIpcRenderer.invoke('upload-image', categoryId, fileBuffer, originalFilename, mimeType),
        
        // 标签方法
        getAllTags: () => mockIpcRenderer.invoke('get-all-tags'),
        searchTags: (query: string) => mockIpcRenderer.invoke('search-tags', query),
        
        // 设置方法
        getStorageSettings: () => mockIpcRenderer.invoke('get-storage-settings'),
        
        // 事件监听
        onMenuAction: (callback: (action: string, data?: any) => void) => {
          mockIpcRenderer.on('menu-action', (_, action, data) => callback(action, data));
        },
        
        removeAllListeners: (channel: string) => {
          mockIpcRenderer.removeAllListeners(channel);
        }
      };

      // 模拟contextBridge调用
      mockContextBridge.exposeInMainWorld('electronAPI', electronAPI);

      expect(mockContextBridge.exposeInMainWorld).toHaveBeenCalledWith(
        'electronAPI',
        expect.objectContaining({
          isElectron: true,
          platform: 'linux'
        })
      );
    });
  });

  describe('IPC调用验证', () => {
    let electronAPI: any;

    beforeEach(() => {
      electronAPI = {
        // 数据库方法
        testDatabase: () => mockIpcRenderer.invoke('test-database'),
        getDatabaseStats: () => mockIpcRenderer.invoke('get-database-stats'),
        
        // 分类方法
        getCategories: (skip?: number, limit?: number) => 
          mockIpcRenderer.invoke('get-categories', skip, limit),
        createCategory: (categoryData: any) => 
          mockIpcRenderer.invoke('create-category', categoryData),
        updateCategory: (categoryId: string, categoryData: any) => 
          mockIpcRenderer.invoke('update-category', categoryId, categoryData),
        deleteCategory: (categoryId: string) => 
          mockIpcRenderer.invoke('delete-category', categoryId),
        getCategoryById: (categoryId: string) => 
          mockIpcRenderer.invoke('get-category-by-id', categoryId),
        getCategoryWithImages: (categoryId: string) => 
          mockIpcRenderer.invoke('get-category-with-images', categoryId),
        
        // 图片方法
        uploadImage: (categoryId: string, fileBuffer: any, originalFilename: string, mimeType: string) => 
          mockIpcRenderer.invoke('upload-image', categoryId, fileBuffer, originalFilename, mimeType),
        getImageById: (imageId: string) => 
          mockIpcRenderer.invoke('get-image-by-id', imageId),
        updateImage: (imageId: string, imageData: any) => 
          mockIpcRenderer.invoke('update-image', imageId, imageData),
        deleteImage: (imageId: string) => 
          mockIpcRenderer.invoke('delete-image', imageId),
        getImagePath: (filename: string) => 
          mockIpcRenderer.invoke('get-image-path', filename),
        getThumbnailPath: (filename: string) => 
          mockIpcRenderer.invoke('get-thumbnail-path', filename),
        
        // 标签方法
        getAllTags: () => mockIpcRenderer.invoke('get-all-tags'),
        createTag: (tagData: any) => mockIpcRenderer.invoke('create-tag', tagData),
        updateTag: (tagId: string, tagData: any) => 
          mockIpcRenderer.invoke('update-tag', tagId, tagData),
        deleteTag: (tagId: string) => mockIpcRenderer.invoke('delete-tag', tagId),
        getTagById: (tagId: string) => mockIpcRenderer.invoke('get-tag-by-id', tagId),
        searchTags: (query: string) => mockIpcRenderer.invoke('search-tags', query),
        addTagToImage: (imageId: string, tagId: string) => 
          mockIpcRenderer.invoke('add-tag-to-image', imageId, tagId),
        removeTagFromImage: (imageId: string, tagId: string) => 
          mockIpcRenderer.invoke('remove-tag-from-image', imageId, tagId),
        getTagsForImage: (imageId: string) => 
          mockIpcRenderer.invoke('get-tags-for-image', imageId),
        searchImagesByTags: (tagNames: string[]) => 
          mockIpcRenderer.invoke('search-images-by-tags', tagNames),

        // 设置方法
        getStorageSettings: () => mockIpcRenderer.invoke('get-storage-settings'),
        updateStorageSettings: (settings: any) => 
          mockIpcRenderer.invoke('update-storage-settings', settings),
        selectDirectory: () => mockIpcRenderer.invoke('select-directory'),
        migrateStorageLocation: (newPath: string) => 
          mockIpcRenderer.invoke('migrate-storage-location', newPath),

        // 事件监听方法
        onMenuAction: (callback: (action: string, data?: any) => void) => {
          mockIpcRenderer.on('menu-action', (_, action, data) => callback(action, data));
        },
        onAppReady: (callback: () => void) => {
          mockIpcRenderer.on('app-ready', callback);
        },
        removeAllListeners: (channel: string) => {
          mockIpcRenderer.removeAllListeners(channel);
        }
      };
    });

    it('数据库方法应该调用正确的IPC通道', () => {
      electronAPI.testDatabase();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('test-database');

      electronAPI.getDatabaseStats();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-database-stats');
    });

    it('分类方法应该传递正确的参数', () => {
      electronAPI.getCategories(10, 20);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-categories', 10, 20);

      const categoryData = { name: 'Test Category' };
      electronAPI.createCategory(categoryData);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('create-category', categoryData);

      electronAPI.updateCategory('cat-id', { name: 'Updated' });
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('update-category', 'cat-id', { name: 'Updated' });

      electronAPI.deleteCategory('cat-id');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('delete-category', 'cat-id');
    });

    it('图片方法应该传递正确的参数', () => {
      const buffer = Buffer.from('test data');
      electronAPI.uploadImage('cat-id', buffer, 'test.jpg', 'image/jpeg');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith(
        'upload-image', 'cat-id', buffer, 'test.jpg', 'image/jpeg'
      );

      electronAPI.getImageById('img-id');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-image-by-id', 'img-id');

      electronAPI.getImagePath('test.jpg');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-image-path', 'test.jpg');
    });

    it('标签方法应该传递正确的参数', () => {
      electronAPI.getAllTags();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-all-tags');

      electronAPI.searchTags('query');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('search-tags', 'query');

      electronAPI.addTagToImage('img-id', 'tag-id');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('add-tag-to-image', 'img-id', 'tag-id');

      electronAPI.searchImagesByTags(['tag1', 'tag2']);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('search-images-by-tags', ['tag1', 'tag2']);
    });

    it('设置方法应该调用正确的IPC通道', () => {
      electronAPI.getStorageSettings();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-storage-settings');

      const settings = { storagePath: '/new/path' };
      electronAPI.updateStorageSettings(settings);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('update-storage-settings', settings);

      electronAPI.selectDirectory();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('select-directory');
    });

    it('事件监听应该正确设置', () => {
      const callback = vi.fn();
      electronAPI.onMenuAction(callback);
      expect(mockIpcRenderer.on).toHaveBeenCalledWith('menu-action', expect.any(Function));

      // 测试回调包装
      const wrappedCallback = mockIpcRenderer.on.mock.calls[0][1];
      wrappedCallback(null, 'test-action', { data: 'test' });
      expect(callback).toHaveBeenCalledWith('test-action', { data: 'test' });

      electronAPI.onAppReady(callback);
      expect(mockIpcRenderer.on).toHaveBeenCalledWith('app-ready', callback);

      electronAPI.removeAllListeners('test-channel');
      expect(mockIpcRenderer.removeAllListeners).toHaveBeenCalledWith('test-channel');
    });
  });

  describe('参数处理测试', () => {
    let electronAPI: any;

    beforeEach(() => {
      electronAPI = {
        createCategory: (categoryData: any) => 
          mockIpcRenderer.invoke('create-category', categoryData),
        getCategories: (skip?: number, limit?: number) => 
          mockIpcRenderer.invoke('get-categories', skip, limit),
        searchImagesByTags: (tagNames: string[]) => 
          mockIpcRenderer.invoke('search-images-by-tags', tagNames),
        uploadImage: (categoryId: string, fileBuffer: any, originalFilename: string, mimeType: string) => 
          mockIpcRenderer.invoke('upload-image', categoryId, fileBuffer, originalFilename, mimeType)
      };
    });

    it('应该正确处理可选参数', () => {
      electronAPI.getCategories();
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-categories', undefined, undefined);

      electronAPI.getCategories(10);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-categories', 10, undefined);

      electronAPI.getCategories(0, 50);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('get-categories', 0, 50);
    });

    it('应该正确处理null和undefined', () => {
      electronAPI.createCategory(null);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('create-category', null);

      electronAPI.createCategory(undefined);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('create-category', undefined);
    });

    it('应该正确处理复杂对象', () => {
      const complexData = {
        name: 'Test Category',
        description: 'Description',
        metadata: {
          author: 'user',
          tags: ['tag1', 'tag2']
        }
      };

      electronAPI.createCategory(complexData);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('create-category', complexData);
    });

    it('应该正确处理数组参数', () => {
      const tagNames = ['tag1', 'tag2', 'tag3'];
      electronAPI.searchImagesByTags(tagNames);
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('search-images-by-tags', tagNames);
    });

    it('应该正确处理Buffer对象', () => {
      const buffer = Buffer.from('fake image data');
      electronAPI.uploadImage('cat-id', buffer, 'test.jpg', 'image/jpeg');
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith(
        'upload-image', 'cat-id', buffer, 'test.jpg', 'image/jpeg'
      );
    });
  });

  describe('类型和结构验证', () => {
    it('应该验证API结构完整性', () => {
      const electronAPI = {
        isElectron: true,
        platform: global.process.platform,
        
        // 数据库
        testDatabase: vi.fn(),
        getDatabaseStats: vi.fn(),
        
        // 分类
        getCategories: vi.fn(),
        createCategory: vi.fn(),
        updateCategory: vi.fn(),
        deleteCategory: vi.fn(),
        getCategoryById: vi.fn(),
        getCategoryWithImages: vi.fn(),
        
        // 图片
        uploadImage: vi.fn(),
        getImageById: vi.fn(),
        updateImage: vi.fn(),
        deleteImage: vi.fn(),
        getImagePath: vi.fn(),
        getThumbnailPath: vi.fn(),
        
        // 标签
        getAllTags: vi.fn(),
        createTag: vi.fn(),
        updateTag: vi.fn(),
        deleteTag: vi.fn(),
        getTagById: vi.fn(),
        searchTags: vi.fn(),
        addTagToImage: vi.fn(),
        removeTagFromImage: vi.fn(),
        getTagsForImage: vi.fn(),
        searchImagesByTags: vi.fn(),

        // 设置
        getStorageSettings: vi.fn(),
        updateStorageSettings: vi.fn(),
        selectDirectory: vi.fn(),
        migrateStorageLocation: vi.fn(),

        // 事件
        onMenuAction: vi.fn(),
        onAppReady: vi.fn(),
        removeAllListeners: vi.fn()
      };

      // 验证基础属性
      expect(electronAPI.isElectron).toBe(true);
      expect(electronAPI.platform).toBe('linux');

      // 验证所有方法都是函数
      const methodNames = [
        'testDatabase', 'getDatabaseStats',
        'getCategories', 'createCategory', 'updateCategory', 'deleteCategory',
        'getCategoryById', 'getCategoryWithImages',
        'uploadImage', 'getImageById', 'updateImage', 'deleteImage',
        'getImagePath', 'getThumbnailPath',
        'getAllTags', 'createTag', 'updateTag', 'deleteTag',
        'getTagById', 'searchTags', 'addTagToImage', 'removeTagFromImage',
        'getTagsForImage', 'searchImagesByTags',
        'getStorageSettings', 'updateStorageSettings', 'selectDirectory',
        'migrateStorageLocation', 'onMenuAction', 'onAppReady', 'removeAllListeners'
      ];

      methodNames.forEach(methodName => {
        expect(electronAPI[methodName]).toBeTypeOf('function');
      });
    });

    it('应该验证平台信息正确', () => {
      // 测试不同平台
      global.process.platform = 'darwin';
      
      const electronAPI = {
        isElectron: true,
        platform: global.process.platform
      };

      expect(electronAPI.platform).toBe('darwin');

      // 恢复
      global.process.platform = 'linux';
    });
  });

  describe('错误处理测试', () => {
    let electronAPI: any;

    beforeEach(() => {
      electronAPI = {
        testDatabase: () => mockIpcRenderer.invoke('test-database'),
        createCategory: (categoryData: any) => 
          mockIpcRenderer.invoke('create-category', categoryData)
      };
    });

    it('应该处理IPC调用失败', async () => {
      const error = new Error('IPC error');
      mockIpcRenderer.invoke.mockRejectedValueOnce(error);

      await expect(electronAPI.testDatabase()).rejects.toThrow('IPC error');
    });

    it('应该正确传递异常', async () => {
      const customError = new Error('Custom error');
      mockIpcRenderer.invoke.mockRejectedValueOnce(customError);

      try {
        await electronAPI.createCategory({ name: 'Test' });
      } catch (err) {
        expect(err).toBe(customError);
      }
    });
  });
});