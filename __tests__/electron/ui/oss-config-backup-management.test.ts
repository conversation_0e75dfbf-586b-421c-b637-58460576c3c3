import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { JSD<PERSON> } from 'jsdom';
import fs from 'fs';
import path from 'path';

// Mock electron's ipcRenderer
const mockIpcRenderer = {
  invoke: vi.fn(),
  on: vi.fn(),
  removeAllListeners: vi.fn(),
};

// Mock electron in global context
(global as any).require = vi.fn((module: string) => {
  if (module === 'electron') {
    return { ipcRenderer: mockIpcRenderer };
  }
  return {};
});

describe('OSS Config Window - Backup Management UI', () => {
  let dom: JSDOM;
  let document: Document;
  let window: Window;

  beforeEach(() => {
    // Read the actual HTML file
    const htmlPath = path.join(__dirname, '../../../electron/oss-config-window.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf-8');
    
    // Create DOM
    dom = new JSDOM(htmlContent, {
      runScripts: 'dangerously',
      resources: 'usable',
    });

    document = dom.window.document;
    window = dom.window;

    // Mock the global objects
    (window as any).ipcRenderer = mockIpcRenderer;
    (window as any).confirm = vi.fn();
    (window as any).prompt = vi.fn();
    (window as any).alert = vi.fn();

    // Reset all mocks
    vi.clearAllMocks();
  });

  afterEach(() => {
    dom.window.close();
  });

  describe('deleteBackup function', () => {
    it('should show confirmation dialog and delete backup on confirm', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      (window as any).confirm.mockReturnValue(true);
      mockIpcRenderer.invoke.mockResolvedValue({
        success: true,
        message: '备份删除成功'
      });

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the deleteBackup function directly on the window object
      (window as any).deleteBackup = async (backupName: string) => {
                const confirmed = (window as any).confirm(`确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`);
        
        if (!confirmed) {
          return;
        }
        
        try {
          (window as any).showStatus('正在删除备份...', 'info');
          
          const result = await mockIpcRenderer.invoke('delete-database-backup', backupName);
          
          if (result.success) {
            (window as any).showStatus('备份删除成功！', 'success');
            (window as any).loadBackupsList();
          } else {
            (window as any).showStatus(`删除失败: ${result.message}`, 'error');
          }
        } catch (error: any) {
          (window as any).showStatus(`删除失败: ${error.message}`, 'error');
        }
      };

      // Act
      await (window as any).deleteBackup(backupName);

      // Assert
      expect((window as any).confirm).toHaveBeenCalledWith(
        `确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`
      );
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('delete-database-backup', backupName);
      expect((window as any).showStatus).toHaveBeenCalledWith('正在删除备份...', 'info');
      expect((window as any).showStatus).toHaveBeenCalledWith('备份删除成功！', 'success');
      expect((window as any).loadBackupsList).toHaveBeenCalled();
    });

    it('should not delete backup when user cancels confirmation', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      (window as any).confirm.mockReturnValue(false);

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the deleteBackup function
      (window as any).deleteBackup = async (backupName: string) => {
        const confirmed = (window as any).confirm(`确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`);
        
        if (!confirmed) {
          return;
        }
        
        // This should not be reached
        (window as any).showStatus('正在删除备份...', 'info');
      };

      // Act
      await (window as any).deleteBackup(backupName);

      // Assert
      expect((window as any).confirm).toHaveBeenCalledWith(
        `确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`
      );
      expect(mockIpcRenderer.invoke).not.toHaveBeenCalled();
      expect((window as any).showStatus).not.toHaveBeenCalled();
      expect((window as any).loadBackupsList).not.toHaveBeenCalled();
    });

    it('should handle delete failure gracefully', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      (window as any).confirm.mockReturnValue(true);
      mockIpcRenderer.invoke.mockResolvedValue({
        success: false,
        message: '文件不存在'
      });

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the deleteBackup function directly on the window object
      (window as any).deleteBackup = async (backupName: string) => {
                const confirmed = (window as any).confirm(`确定要删除备份 "${backupName}" 吗？\n\n此操作不可撤销。`);
        
        if (!confirmed) {
          return;
        }
        
        try {
          (window as any).showStatus('正在删除备份...', 'info');
          
          const result = await mockIpcRenderer.invoke('delete-database-backup', backupName);
          
          if (result.success) {
            (window as any).showStatus('备份删除成功！', 'success');
            (window as any).loadBackupsList();
          } else {
            (window as any).showStatus(`删除失败: ${result.message}`, 'error');
          }
        } catch (error: any) {
          (window as any).showStatus(`删除失败: ${error.message}`, 'error');
        }
      };

      // Act
      await (window as any).deleteBackup(backupName);

      // Assert
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith('delete-database-backup', backupName);
      expect((window as any).showStatus).toHaveBeenCalledWith('删除失败: 文件不存在', 'error');
      expect((window as any).loadBackupsList).not.toHaveBeenCalled();
    });
  });

  describe('renameBackup function', () => {
    it('should rename backup successfully', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'my-custom-name';
      const expectedNewName = 'backup-my-custom-name.db';
      
      (window as any).prompt.mockReturnValue(newName);
      mockIpcRenderer.invoke.mockResolvedValue({
        success: true,
        message: '备份重命名成功'
      });

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the renameBackup function directly on the window object
      (window as any).renameBackup = async (backupName: string) => {
                const newName = (window as any).prompt(`请输入新的备份名称：`, backupName.replace('.db', ''));
        
        if (!newName || newName.trim() === '') {
          return;
        }
        
        let newBackupName = newName.trim();
        
        // 确保文件名以 backup- 开头
        if (!newBackupName.startsWith('backup-')) {
          newBackupName = 'backup-' + newBackupName;
        }
        
        // 确保文件名以 .db 结尾
        if (!newBackupName.endsWith('.db')) {
          newBackupName += '.db';
        }
        
        if (newBackupName === backupName) {
          (window as any).showStatus('新名称与原名称相同', 'info');
          return;
        }
        
        try {
          (window as any).showStatus('正在重命名备份...', 'info');
          
          const result = await mockIpcRenderer.invoke('rename-database-backup', backupName, newBackupName);
          
          if (result.success) {
            (window as any).showStatus('备份重命名成功！', 'success');
            (window as any).loadBackupsList();
          } else {
            (window as any).showStatus(`重命名失败: ${result.message}`, 'error');
          }
        } catch (error: any) {
          (window as any).showStatus(`重命名失败: ${error.message}`, 'error');
        }
      };

      // Act
      await (window as any).renameBackup(oldName);

      // Assert
      expect((window as any).prompt).toHaveBeenCalledWith(
        '请输入新的备份名称：',
        'backup-2025-01-01-12-00-00'
      );
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith(
        'rename-database-backup',
        oldName,
        expectedNewName
      );
      expect((window as any).showStatus).toHaveBeenCalledWith('正在重命名备份...', 'info');
      expect((window as any).showStatus).toHaveBeenCalledWith('备份重命名成功！', 'success');
      expect((window as any).loadBackupsList).toHaveBeenCalled();
    });

    it('should not rename when user cancels prompt', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      (window as any).prompt.mockReturnValue(null); // User cancelled

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the renameBackup function
      (window as any).renameBackup = async (backupName: string) => {
        const newName = (window as any).prompt(`请输入新的备份名称：`, backupName.replace('.db', ''));
        
        if (!newName || newName.trim() === '') {
          return;
        }
        
        // This should not be reached
        (window as any).showStatus('正在重命名备份...', 'info');
      };

      // Act
      await (window as any).renameBackup(backupName);

      // Assert
      expect((window as any).prompt).toHaveBeenCalledWith(
        '请输入新的备份名称：',
        'backup-2025-01-01-12-00-00'
      );
      expect(mockIpcRenderer.invoke).not.toHaveBeenCalled();
      expect((window as any).showStatus).not.toHaveBeenCalled();
      expect((window as any).loadBackupsList).not.toHaveBeenCalled();
    });

    it('should handle same name scenario', async () => {
      // Arrange
      const backupName = 'backup-2025-01-01-12-00-00.db';
      const newName = '2025-01-01-12-00-00'; // Will become the same after processing
      
      (window as any).prompt.mockReturnValue(newName);

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the renameBackup function
      (window as any).renameBackup = async (backupName: string) => {
        const newName = (window as any).prompt(`请输入新的备份名称：`, backupName.replace('.db', ''));
        
        if (!newName || newName.trim() === '') {
          return;
        }
        
        let newBackupName = newName.trim();
        
        // 确保文件名以 backup- 开头
        if (!newBackupName.startsWith('backup-')) {
          newBackupName = 'backup-' + newBackupName;
        }
        
        // 确保文件名以 .db 结尾
        if (!newBackupName.endsWith('.db')) {
          newBackupName += '.db';
        }
        
        if (newBackupName === backupName) {
          (window as any).showStatus('新名称与原名称相同', 'info');
          return;
        }
      };

      // Act
      await (window as any).renameBackup(backupName);

      // Assert
      expect((window as any).prompt).toHaveBeenCalledWith(
        '请输入新的备份名称：',
        'backup-2025-01-01-12-00-00'
      );
      expect(mockIpcRenderer.invoke).not.toHaveBeenCalled();
      expect((window as any).showStatus).toHaveBeenCalledWith('新名称与原名称相同', 'info');
      expect((window as any).loadBackupsList).not.toHaveBeenCalled();
    });

    it('should handle rename failure gracefully', async () => {
      // Arrange
      const oldName = 'backup-2025-01-01-12-00-00.db';
      const newName = 'my-custom-name';
      const expectedNewName = 'backup-my-custom-name.db';
      
      (window as any).prompt.mockReturnValue(newName);
      mockIpcRenderer.invoke.mockResolvedValue({
        success: false,
        message: '新文件名已存在'
      });

      // Mock functions
      (window as any).loadBackupsList = vi.fn();
      (window as any).showStatus = vi.fn();

      // Create the renameBackup function directly on the window object
      (window as any).renameBackup = async (backupName: string) => {
                const newName = (window as any).prompt(`请输入新的备份名称：`, backupName.replace('.db', ''));
        
        if (!newName || newName.trim() === '') {
          return;
        }
        
        let newBackupName = newName.trim();
        
        // 确保文件名以 backup- 开头
        if (!newBackupName.startsWith('backup-')) {
          newBackupName = 'backup-' + newBackupName;
        }
        
        // 确保文件名以 .db 结尾
        if (!newBackupName.endsWith('.db')) {
          newBackupName += '.db';
        }
        
        if (newBackupName === backupName) {
          (window as any).showStatus('新名称与原名称相同', 'info');
          return;
        }
        
        try {
          (window as any).showStatus('正在重命名备份...', 'info');
          
          const result = await mockIpcRenderer.invoke('rename-database-backup', backupName, newBackupName);
          
          if (result.success) {
            (window as any).showStatus('备份重命名成功！', 'success');
            (window as any).loadBackupsList();
          } else {
            (window as any).showStatus(`重命名失败: ${result.message}`, 'error');
          }
        } catch (error: any) {
          (window as any).showStatus(`重命名失败: ${error.message}`, 'error');
        }
      };

      // Act
      await (window as any).renameBackup(oldName);

      // Assert
      expect(mockIpcRenderer.invoke).toHaveBeenCalledWith(
        'rename-database-backup',
        oldName,
        expectedNewName
      );
      expect((window as any).showStatus).toHaveBeenCalledWith('重命名失败: 新文件名已存在', 'error');
      expect((window as any).loadBackupsList).not.toHaveBeenCalled();
    });
  });
});