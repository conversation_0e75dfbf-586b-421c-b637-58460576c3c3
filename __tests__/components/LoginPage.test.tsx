/**
 * LoginPage组件测试套件
 * 测试登录页面的渲染和交互功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import LoginPage from '../../components/LoginPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';
import * as apiService from '../../services/api';
import type { ApiError, TokenResponse } from '../../types';

// Mock API服务
vi.mock('../../services/api', () => ({
  sendVerificationCode: vi.fn(),
  verifyCodeAndGetToken: vi.fn(),
}));

// Mock constants
vi.mock('../../constants', () => ({
  IS_ELECTRON: false, // 默认为Web模式
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    form: ({ children, ...props }: any) => <form {...props}>{children}</form>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    input: ({ children, ...props }: any) => <input {...props}>{children}</input>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
    h2: ({ children, ...props }: any) => <h2 {...props}>{children}</h2>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    label: ({ children, ...props }: any) => <label {...props}>{children}</label>,
  },
}));

// Mock animations utils
vi.mock('../../utils/animations', () => ({
  fadeInVariants: {},
  staggerContainerVariants: {},
  staggerItemVariants: {},
  getAnimationConfig: vi.fn(() => ({})),
}));

// Mock子组件
vi.mock('../../components/ErrorDisplay', () => ({
  default: ({ error }: { error: any }) => (
    <div data-testid="error-display">{error.message || error}</div>
  ),
}));

vi.mock('../../components/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

// Mock react-router-dom
const mockNavigate = vi.fn();
vi.mock('react-router-dom', async () => {
  const actual = await vi.importActual('react-router-dom');
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

// 测试包装组件
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MemoryRouter>
    <AuthProvider>
      <ThemeProvider>
        {children}
      </ThemeProvider>
    </AuthProvider>
  </MemoryRouter>
);

describe('LoginPage组件测试套件', () => {
  const mockSendVerificationCode = vi.mocked(apiService.sendVerificationCode);
  const mockVerifyCodeAndGetToken = vi.mocked(apiService.verifyCodeAndGetToken);

  beforeEach(() => {
    vi.clearAllMocks();
    // 重置IS_ELECTRON为false（Web模式）
    vi.doMock('../../constants', () => ({
      IS_ELECTRON: false,
    }));
  });

  describe('Web模式基础渲染测试', () => {
    it('应该正确渲染登录表单', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      expect(screen.getByText('Login')).toBeInTheDocument();
      expect(screen.getByLabelText(/email/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /get verification code/i })).toBeInTheDocument();
    });

    it('应该显示邮箱输入框', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('placeholder', expect.stringContaining('email'));
    });

    it('应该在未发送验证码时隐藏验证码输入框', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      expect(screen.queryByLabelText(/verification code/i)).not.toBeInTheDocument();
      expect(screen.queryByRole('button', { name: /verify and login/i })).not.toBeInTheDocument();
    });
  });

  describe('邮箱验证和验证码发送测试', () => {
    it('应该验证邮箱格式', async () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      // 测试空邮箱
      fireEvent.click(getCodeButton);
      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Please enter your email address.');
      });

      // 测试无效邮箱格式
      fireEvent.change(emailInput, { target: { value: 'invalid-email' } });
      fireEvent.click(getCodeButton);
      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Please enter a valid email address.');
      });
    });

    it('应该成功发送验证码', async () => {
      mockSendVerificationCode.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(getCodeButton);

      await waitFor(() => {
        expect(mockSendVerificationCode).toHaveBeenCalledWith('<EMAIL>');
      });

      // 应该显示成功消息和验证码输入框
      expect(screen.getByText(/verification code sent/i)).toBeInTheDocument();
      expect(screen.getByLabelText(/verification code/i)).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /verify and login/i })).toBeInTheDocument();
    });

    it('应该处理发送验证码失败', async () => {
      const mockError: ApiError = {
        type: 'validation',
        message: 'Email not found',
        timestamp: new Date().toISOString(),
      };

      mockSendVerificationCode.mockRejectedValue(mockError);

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(getCodeButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Email not found');
      });
    });

    it('应该在发送验证码时显示加载状态', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockSendVerificationCode.mockReturnValue(promise);

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(getCodeButton);

      // 应该显示加载状态
      expect(getCodeButton).toBeDisabled();
      expect(getCodeButton).toHaveTextContent(/sending/i);

      // 解析Promise
      resolvePromise!({ success: true });
      await waitFor(() => {
        expect(getCodeButton).not.toBeDisabled();
      });
    });
  });

  describe('验证码验证和登录测试', () => {
    beforeEach(async () => {
      mockSendVerificationCode.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.click(getCodeButton);

      await waitFor(() => {
        expect(screen.getByLabelText(/verification code/i)).toBeInTheDocument();
      });
    });

    it('应该验证验证码格式', async () => {
      const verifyButton = screen.getByRole('button', { name: /verify and login/i });

      // 测试空验证码
      fireEvent.click(verifyButton);
      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Please enter the verification code.');
      });

      // 测试短验证码
      const codeInput = screen.getByLabelText(/verification code/i);
      fireEvent.change(codeInput, { target: { value: '123' } });
      fireEvent.click(verifyButton);
      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Verification code must be 6 digits.');
      });
    });

    it('应该成功验证并登录', async () => {
      const mockTokenResponse: TokenResponse = {
        access_token: 'test-token',
        token_type: 'bearer',
        expires_in: 3600,
        refresh_token: 'refresh-token',
      };

      mockVerifyCodeAndGetToken.mockResolvedValue(mockTokenResponse);

      const codeInput = screen.getByLabelText(/verification code/i);
      const verifyButton = screen.getByRole('button', { name: /verify and login/i });

      fireEvent.change(codeInput, { target: { value: '123456' } });
      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(mockVerifyCodeAndGetToken).toHaveBeenCalledWith('<EMAIL>', '123456');
      });

      // 应该导航到首页
      expect(mockNavigate).toHaveBeenCalledWith('/');
    });

    it('应该处理验证失败', async () => {
      const mockError: ApiError = {
        type: 'validation',
        message: 'Invalid verification code',
        timestamp: new Date().toISOString(),
      };

      mockVerifyCodeAndGetToken.mockRejectedValue(mockError);

      const codeInput = screen.getByLabelText(/verification code/i);
      const verifyButton = screen.getByRole('button', { name: /verify and login/i });

      fireEvent.change(codeInput, { target: { value: '123456' } });
      fireEvent.click(verifyButton);

      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toHaveTextContent('Invalid verification code');
      });
    });

    it('应该在验证时显示加载状态', async () => {
      let resolvePromise: (value: any) => void;
      const promise = new Promise((resolve) => {
        resolvePromise = resolve;
      });
      mockVerifyCodeAndGetToken.mockReturnValue(promise);

      const codeInput = screen.getByLabelText(/verification code/i);
      const verifyButton = screen.getByRole('button', { name: /verify and login/i });

      fireEvent.change(codeInput, { target: { value: '123456' } });
      fireEvent.click(verifyButton);

      // 应该显示加载状态
      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();

      // 解析Promise
      resolvePromise!({ access_token: 'test-token' });
      await waitFor(() => {
        expect(screen.queryByTestId('loading-spinner')).not.toBeInTheDocument();
      });
    });
  });

  describe('Electron模式测试', () => {
    beforeEach(() => {
      // 设置为Electron模式
      vi.doMock('../../constants', () => ({
        IS_ELECTRON: true,
      }));
    });

    it('应该在Electron模式下直接导航到首页', async () => {
      // 重新导入以获取更新的IS_ELECTRON值
      const { LoginPage: ElectronLoginPage } = await import('../../components/LoginPage');
      
      render(
        <TestWrapper>
          <ElectronLoginPage />
        </TestWrapper>
      );

      // 应该直接导航到首页
      expect(mockNavigate).toHaveBeenCalledWith('/', { replace: true });
    });
  });

  describe('已认证用户重定向测试', () => {
    it('应该在已认证时重定向到首页', () => {
      // 这个测试需要模拟已认证状态，但由于AuthProvider的复杂性，
      // 我们主要测试useEffect中的逻辑
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      // 在Web模式下，如果用户已认证，应该重定向
      // 这个测试可能需要更复杂的mock设置
      expect(screen.getByText('Login')).toBeInTheDocument();
    });
  });

  describe('表单交互测试', () => {
    it('应该支持Enter键提交表单', async () => {
      mockSendVerificationCode.mockResolvedValue({ success: true });

      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: '<EMAIL>' } });
      fireEvent.keyDown(emailInput, { key: 'Enter', code: 'Enter' });

      await waitFor(() => {
        expect(mockSendVerificationCode).toHaveBeenCalledWith('<EMAIL>');
      });
    });

    it('应该清除错误消息当用户开始输入', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });
      
      // 触发错误
      fireEvent.click(getCodeButton);
      expect(screen.getByTestId('error-display')).toBeInTheDocument();

      // 开始输入应该清除错误
      const emailInput = screen.getByLabelText(/email/i);
      fireEvent.change(emailInput, { target: { value: 't' } });
      
      expect(screen.queryByTestId('error-display')).not.toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    it('应该有正确的表单标签和ARIA属性', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      expect(emailInput).toHaveAttribute('type', 'email');
      expect(emailInput).toHaveAttribute('required');

      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });
      expect(getCodeButton).toHaveAttribute('type', 'submit');
    });

    it('应该支持键盘导航', () => {
      render(
        <TestWrapper>
          <LoginPage />
        </TestWrapper>
      );

      const emailInput = screen.getByLabelText(/email/i);
      const getCodeButton = screen.getByRole('button', { name: /get verification code/i });

      // 测试tab顺序
      emailInput.focus();
      expect(document.activeElement).toBe(emailInput);

      fireEvent.keyDown(emailInput, { key: 'Tab' });
      // 下一个焦点应该是按钮（在实际DOM中）
      expect(getCodeButton).toBeInTheDocument();
    });
  });
});
