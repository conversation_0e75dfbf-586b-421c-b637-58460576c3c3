/**
 * CategoryDetail组件测试套件
 * 测试分类详情页面的渲染和交互功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import CategoryDetail from '../../components/CategoryDetail';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';
import * as apiService from '../../services/api';
import type { CategoryReadWithImages, ImageRead, ApiError } from '../../types';

// Mock API服务
vi.mock('../../services/api', () => ({
  getCategoryWithImages: vi.fn(),
  updateCategory: vi.fn(),
  deleteCategory: vi.fn(),
  uploadImage: vi.fn(),
}));

// Mock constants
vi.mock('../../constants', () => ({
  IMAGE_BASE_URL: 'http://localhost:8000',
  IS_ELECTRON: false,
}));

// Mock子组件
vi.mock('../../components/LoadingSpinner', () => ({
  default: () => <div data-testid="loading-spinner">Loading...</div>,
}));

vi.mock('../../components/ErrorDisplay', () => ({
  default: ({ error }: { error: any }) => (
    <div data-testid="error-display">{error.message || error}</div>
  ),
}));

vi.mock('../../components/ImageCard', () => ({
  default: ({ image, onClick }: { image: ImageRead; onClick: () => void }) => (
    <div data-testid={`image-card-${image.id}`} onClick={onClick}>
      {image.title}
    </div>
  ),
}));

vi.mock('../../components/ImageCardSkeleton', () => ({
  default: () => <div data-testid="image-card-skeleton">Loading image...</div>,
}));

vi.mock('../../components/Modal', () => ({
  default: ({ isOpen, onClose, children }: any) =>
    isOpen ? (
      <div data-testid="modal">
        <button data-testid="modal-close" onClick={onClose}>
          Close
        </button>
        {children}
      </div>
    ) : null,
}));

vi.mock('../../components/CategoryForm', () => ({
  default: ({ onSubmit, onCancel, initialData }: any) => (
    <div data-testid="category-form">
      <input
        data-testid="category-name-input"
        defaultValue={initialData?.name || ''}
        onChange={(e) => {
          // Mock form submission
          if (e.target.value === 'submit') {
            onSubmit({ name: 'Updated Category', description: 'Updated Description' });
          }
        }}
      />
      <button data-testid="form-submit" onClick={() => onSubmit({ name: 'Updated Category' })}>
        Submit
      </button>
      <button data-testid="form-cancel" onClick={onCancel}>
        Cancel
      </button>
    </div>
  ),
}));

vi.mock('../../components/ImageUploadForm', () => ({
  default: ({ onSubmit, onCancel }: any) => (
    <div data-testid="image-upload-form">
      <button
        data-testid="upload-submit"
        onClick={() => onSubmit({ file: new File(['test'], 'test.jpg'), title: 'Test Image' })}
      >
        Upload
      </button>
      <button data-testid="upload-cancel" onClick={onCancel}>
        Cancel
      </button>
    </div>
  ),
}));

vi.mock('../../components/AlertDialog', () => ({
  default: ({ isOpen, onConfirm, onCancel }: any) =>
    isOpen ? (
      <div data-testid="alert-dialog">
        <button data-testid="confirm-delete" onClick={onConfirm}>
          Confirm
        </button>
        <button data-testid="cancel-delete" onClick={onCancel}>
          Cancel
        </button>
      </div>
    ) : null,
}));

vi.mock('../../components/icons', () => ({
  EditIcon: () => <div data-testid="edit-icon">Edit</div>,
  TrashIcon: () => <div data-testid="trash-icon">Delete</div>,
  UploadIcon: () => <div data-testid="upload-icon">Upload</div>,
  ImagePlaceholderIcon: () => <div data-testid="image-placeholder-icon">Placeholder</div>,
}));

// 测试包装组件
const TestWrapper: React.FC<{ children: React.ReactNode; initialPath?: string }> = ({
  children,
  initialPath = '/categories/1',
}) => (
  <MemoryRouter initialEntries={[initialPath]}>
    <AuthProvider>
      <ThemeProvider>
        <Routes>
          <Route path="/categories/:categoryId" element={children} />
        </Routes>
      </ThemeProvider>
    </AuthProvider>
  </MemoryRouter>
);

describe('CategoryDetail组件测试套件', () => {
  const mockGetCategoryWithImages = vi.mocked(apiService.getCategoryWithImages);
  const mockUpdateCategory = vi.mocked(apiService.updateCategory);
  const mockDeleteCategory = vi.mocked(apiService.deleteCategory);
  const mockUploadImage = vi.mocked(apiService.uploadImage);

  const mockImages: ImageRead[] = [
    {
      id: '1',
      category_id: '1',
      title: 'Image 1',
      original_filename: 'image1.jpg',
      stored_filename: 'stored1.jpg',
      relative_file_path: '/images/image1.jpg',
      relative_thumbnail_path: '/thumbnails/image1.jpg',
      mime_type: 'image/jpeg',
      size_bytes: 1024000,
      description: 'Test image 1',
      tags: [],
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
      file_metadata: {},
      exif_info: null,
      image_url: '/images/image1.jpg',
      thumbnail_url: '/thumbnails/image1.jpg',
      set_as_category_thumbnail: false,
    },
    {
      id: '2',
      category_id: '1',
      title: 'Image 2',
      original_filename: 'image2.jpg',
      stored_filename: 'stored2.jpg',
      relative_file_path: '/images/image2.jpg',
      relative_thumbnail_path: '/thumbnails/image2.jpg',
      mime_type: 'image/jpeg',
      size_bytes: 2048000,
      description: 'Test image 2',
      tags: [],
      created_at: '2025-01-02T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
      file_metadata: {},
      exif_info: null,
      image_url: '/images/image2.jpg',
      thumbnail_url: '/thumbnails/image2.jpg',
      set_as_category_thumbnail: false,
    },
  ];

  const mockCategory: CategoryReadWithImages = {
    id: '1',
    name: 'Test Category',
    description: 'Test Description',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    thumbnail_path: '/thumbnails/category1.jpg',
    thumbnail_url: 'http://localhost:8000/thumbnails/category1.jpg',
    images: mockImages,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础渲染测试', () => {
    it('应该显示加载状态', () => {
      mockGetCategoryWithImages.mockImplementation(() => new Promise(() => {})); // 永不resolve

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('应该成功加载并显示分类详情', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      expect(screen.getByText('Test Description')).toBeInTheDocument();
      expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('image-card-2')).toBeInTheDocument();
    });

    it('应该显示错误状态', async () => {
      const mockError: ApiError = {
        type: 'network',
        message: 'Failed to load category',
        timestamp: new Date().toISOString(),
      };

      mockGetCategoryWithImages.mockRejectedValue(mockError);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toBeInTheDocument();
      });

      expect(screen.getByText('Failed to load category')).toBeInTheDocument();
    });
  });

  describe('认证状态测试', () => {
    it('应该在已认证时显示编辑和删除按钮', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      expect(screen.getByTestId('edit-icon')).toBeInTheDocument();
      expect(screen.getByTestId('trash-icon')).toBeInTheDocument();
      expect(screen.getByTestId('upload-icon')).toBeInTheDocument();
    });
  });

  describe('分类编辑测试', () => {
    it('应该打开编辑模态框', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('edit-icon'));

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(screen.getByTestId('category-form')).toBeInTheDocument();
    });

    it('应该成功更新分类', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);
      const updatedCategory = { ...mockCategory, name: 'Updated Category' };
      mockUpdateCategory.mockResolvedValue(updatedCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('edit-icon'));
      fireEvent.click(screen.getByTestId('form-submit'));

      await waitFor(() => {
        expect(mockUpdateCategory).toHaveBeenCalledWith('1', { name: 'Updated Category' });
      });
    });

    it('应该关闭编辑模态框', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('edit-icon'));
      expect(screen.getByTestId('modal')).toBeInTheDocument();

      fireEvent.click(screen.getByTestId('form-cancel'));
      expect(screen.queryByTestId('modal')).not.toBeInTheDocument();
    });
  });

  describe('分类删除测试', () => {
    it('应该打开删除确认对话框', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('trash-icon'));

      expect(screen.getByTestId('alert-dialog')).toBeInTheDocument();
    });

    it('应该成功删除分类', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);
      mockDeleteCategory.mockResolvedValue(undefined);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('trash-icon'));
      fireEvent.click(screen.getByTestId('confirm-delete'));

      await waitFor(() => {
        expect(mockDeleteCategory).toHaveBeenCalledWith('1');
      });
    });

    it('应该取消删除操作', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('trash-icon'));
      expect(screen.getByTestId('alert-dialog')).toBeInTheDocument();

      fireEvent.click(screen.getByTestId('cancel-delete'));
      expect(screen.queryByTestId('alert-dialog')).not.toBeInTheDocument();
    });
  });

  describe('图片上传测试', () => {
    it('应该打开图片上传模态框', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('upload-icon'));

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(screen.getByTestId('image-upload-form')).toBeInTheDocument();
    });

    it('应该成功上传图片', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);
      const newImage = { ...mockImages[0], id: '3', title: 'New Image' };
      mockUploadImage.mockResolvedValue(newImage);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('upload-icon'));
      fireEvent.click(screen.getByTestId('upload-submit'));

      await waitFor(() => {
        expect(mockUploadImage).toHaveBeenCalled();
      });
    });
  });

  describe('图片查看测试', () => {
    it('应该点击图片卡片打开详情模态框', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper>
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('image-card-1'));

      // 这里应该打开图片详情模态框，但由于我们mock了组件，只能验证点击事件
      expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
    });
  });

  describe('URL参数处理测试', () => {
    it('应该根据URL参数打开指定图片', async () => {
      mockGetCategoryWithImages.mockResolvedValue(mockCategory);

      render(
        <TestWrapper initialPath="/categories/1?imageId=1">
          <CategoryDetail />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Test Category')).toBeInTheDocument();
      });

      // 验证API被调用
      expect(mockGetCategoryWithImages).toHaveBeenCalledWith('1');
    });
  });
});
