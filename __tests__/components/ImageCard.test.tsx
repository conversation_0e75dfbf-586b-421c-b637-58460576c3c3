/**
 * ImageCard组件测试套件
 * 测试图片卡片组件的渲染和交互功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import ImageCard from '../../components/ImageCard';
import { ThemeProvider } from '../../contexts/ThemeContext';
import type { ImageRead } from '../../types';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    img: ({ children, ...props }: any) => <img {...props}>{children}</img>,
  },
}));

// Mock animations utils
vi.mock('../../utils/animations', () => ({
  cardVariants: {},
  imageVariants: {},
  getAnimationConfig: vi.fn(() => ({})),
}));

// Mock constants
vi.mock('../../constants', () => ({
  IMAGE_BASE_URL: 'http://localhost:8000',
}));

// Mock icons
vi.mock('../../components/icons', () => ({
  ImagePlaceholderIcon: ({ className }: { className?: string }) => (
    <div data-testid="image-placeholder-icon" className={className}>
      Placeholder Icon
    </div>
  ),
}));

// 测试包装组件
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <ThemeProvider>
    {children}
  </ThemeProvider>
);

describe('ImageCard组件测试套件', () => {
  const mockOnClick = vi.fn();

  const mockImage: ImageRead = {
    id: '1',
    category_id: '1',
    title: 'Test Image',
    original_filename: 'test.jpg',
    stored_filename: 'stored-test.jpg',
    relative_file_path: '/images/test.jpg',
    relative_thumbnail_path: '/thumbnails/test.jpg',
    mime_type: 'image/jpeg',
    size_bytes: 1024000,
    description: 'Test image description',
    tags: [],
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    file_metadata: {},
    exif_info: null,
    image_url: 'http://localhost:8000/images/test.jpg',
    thumbnail_url: 'http://localhost:8000/thumbnails/test.jpg',
    set_as_category_thumbnail: false,
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础渲染测试', () => {
    it('应该正确渲染图片卡片', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Test Image')).toBeInTheDocument();
      expect(screen.getByText('Test image description')).toBeInTheDocument();
    });

    it('应该显示缩略图', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/test.jpg');
      expect(image).toHaveAttribute('alt', 'Test Image');
    });

    it('应该在没有缩略图时使用原图', () => {
      const imageWithoutThumbnail = {
        ...mockImage,
        thumbnail_url: null,
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithoutThumbnail} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/images/test.jpg');
    });

    it('应该在没有图片时显示占位符', () => {
      const imageWithoutUrls = {
        ...mockImage,
        thumbnail_url: null,
        image_url: null,
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithoutUrls} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });
  });

  describe('交互功能测试', () => {
    it('应该在点击时调用onClick回调', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      fireEvent.click(card);

      expect(mockOnClick).toHaveBeenCalledTimes(1);
    });

    it('应该支持键盘导航', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      
      // 测试Enter键
      fireEvent.keyDown(card, { key: 'Enter' });
      expect(mockOnClick).toHaveBeenCalledTimes(1);

      // 测试空格键
      fireEvent.keyDown(card, { key: ' ' });
      expect(mockOnClick).toHaveBeenCalledTimes(2);

      // 测试其他键不应该触发
      fireEvent.keyDown(card, { key: 'Escape' });
      expect(mockOnClick).toHaveBeenCalledTimes(2);
    });

    it('应该有正确的ARIA标签', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('aria-label', 'View details for Test Image');
      expect(card).toHaveAttribute('tabIndex', '0');
    });
  });

  describe('图片加载错误处理', () => {
    it('应该在图片加载失败时显示占位符', async () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      
      // 模拟图片加载错误
      fireEvent.error(image);

      await waitFor(() => {
        expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      });
    });

    it('应该在图片变化时重置错误状态', async () => {
      const { rerender } = render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      fireEvent.error(image);

      await waitFor(() => {
        expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      });

      // 更改图片
      const newImage = {
        ...mockImage,
        id: '2',
        thumbnail_url: 'http://localhost:8000/thumbnails/new.jpg',
      };

      rerender(
        <TestWrapper>
          <ImageCard image={newImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      // 应该重新显示图片
      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('forceSquare属性测试', () => {
    it('应该默认使用正方形布局', () => {
      const { container } = render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const imageContainer = container.querySelector('.aspect-square');
      expect(imageContainer).toBeInTheDocument();
    });

    it('应该在forceSquare=false时使用自适应布局', () => {
      const { container } = render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} forceSquare={false} />
        </TestWrapper>
      );

      const imageContainer = container.querySelector('.aspect-square');
      expect(imageContainer).not.toBeInTheDocument();
    });
  });

  describe('URL转换测试', () => {
    it('应该正确转换绝对URL为相对URL', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/test.jpg');
    });

    it('应该保持相对URL不变', () => {
      const imageWithRelativeUrl = {
        ...mockImage,
        thumbnail_url: '/thumbnails/relative.jpg',
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithRelativeUrl} onClick={mockOnClick} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/relative.jpg');
    });

    it('应该处理空的URL', () => {
      const imageWithEmptyUrl = {
        ...mockImage,
        thumbnail_url: '',
        image_url: '',
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithEmptyUrl} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
    });
  });

  describe('样式和动画测试', () => {
    it('应该应用正确的CSS类', () => {
      const { container } = render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = container.firstChild as HTMLElement;
      expect(card).toHaveClass('cursor-pointer');
      expect(card).toHaveClass('group');
      expect(card).toHaveClass('flex');
      expect(card).toHaveClass('flex-col');
    });

    it('应该在hover时应用变换效果', () => {
      const { container } = render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const imageElement = container.querySelector('img');
      expect(imageElement).toHaveClass('group-hover:scale-105');
    });
  });

  describe('边界情况测试', () => {
    it('应该处理没有标题的图片', () => {
      const imageWithoutTitle = {
        ...mockImage,
        title: null,
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithoutTitle} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('aria-label', 'View details for image');
    });

    it('应该处理没有描述的图片', () => {
      const imageWithoutDescription = {
        ...mockImage,
        description: null,
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithoutDescription} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Test Image')).toBeInTheDocument();
      expect(screen.queryByText('Test image description')).not.toBeInTheDocument();
    });

    it('应该处理长标题和描述', () => {
      const imageWithLongContent = {
        ...mockImage,
        title: 'This is a very long image title that might overflow the container',
        description: 'This is a very long image description that might also overflow the container and should be handled gracefully',
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithLongContent} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByText(imageWithLongContent.title)).toBeInTheDocument();
      expect(screen.getByText(imageWithLongContent.description)).toBeInTheDocument();
    });

    it('应该处理特殊字符', () => {
      const imageWithSpecialChars = {
        ...mockImage,
        title: 'Image & <Special> "Characters"',
        description: 'Description with <HTML> & special chars',
      };

      render(
        <TestWrapper>
          <ImageCard image={imageWithSpecialChars} onClick={mockOnClick} />
        </TestWrapper>
      );

      expect(screen.getByText('Image & <Special> "Characters"')).toBeInTheDocument();
      expect(screen.getByText('Description with <HTML> & special chars')).toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    it('应该有正确的role和tabIndex', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('tabIndex', '0');
    });

    it('应该支持屏幕阅读器', () => {
      render(
        <TestWrapper>
          <ImageCard image={mockImage} onClick={mockOnClick} />
        </TestWrapper>
      );

      const card = screen.getByRole('button');
      expect(card).toHaveAttribute('aria-label', 'View details for Test Image');

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', 'Test Image');
    });
  });
});
