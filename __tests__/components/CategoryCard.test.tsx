/**
 * CategoryCard组件测试套件
 * 测试分类卡片组件的渲染和交互功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import CategoryCard from '../../components/CategoryCard';
import { ThemeProvider } from '../../contexts/ThemeContext';
import type { CategoryRead } from '../../types';

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    img: ({ children, ...props }: any) => <img {...props}>{children}</img>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
    h3: ({ children, ...props }: any) => <h3 {...props}>{children}</h3>,
  },
}));

// Mock animations utils
vi.mock('../../utils/animations', () => ({
  cardVariants: {},
  imageVariants: {},
  getAnimationConfig: vi.fn(() => ({})),
}));

// Mock constants
vi.mock('../../constants', () => ({
  IMAGE_BASE_URL: 'http://localhost:8000',
}));

// Mock icons
vi.mock('../../components/icons', () => ({
  ImagePlaceholderIcon: ({ className }: { className?: string }) => (
    <div data-testid="image-placeholder-icon" className={className}>
      Placeholder Icon
    </div>
  ),
}));

// 测试包装组件
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <MemoryRouter>
    <ThemeProvider>
      {children}
    </ThemeProvider>
  </MemoryRouter>
);

describe('CategoryCard组件测试套件', () => {
  const mockCategory: CategoryRead = {
    id: '1',
    name: 'Test Category',
    description: 'Test Description',
    created_at: '2025-01-01T00:00:00Z',
    updated_at: '2025-01-01T00:00:00Z',
    thumbnail_path: '/thumbnails/test.jpg',
    thumbnail_url: 'http://localhost:8000/thumbnails/test.jpg',
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('基础渲染测试', () => {
    it('应该正确渲染分类卡片', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      // 默认模式下不显示文本，只显示图片
      const link = screen.getByRole('link');
      expect(link).toBeInTheDocument();
      expect(link).toHaveAttribute('href', '/categories/1');

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', 'Test Category');
    });

    it('应该渲染为链接到分类详情页', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/categories/1');
    });

    it('应该显示分类缩略图', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/test.jpg');
      expect(image).toHaveAttribute('alt', 'Test Category');
    });

    it('应该在没有缩略图时显示占位符图标', () => {
      const categoryWithoutThumbnail = {
        ...mockCategory,
        thumbnail_url: null,
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithoutThumbnail} />
        </TestWrapper>
      );

      expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      expect(screen.queryByRole('img')).not.toBeInTheDocument();
    });
  });

  describe('紧凑模式测试', () => {
    it('应该在紧凑模式下正确渲染', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} showCompactDetails={true} />
        </TestWrapper>
      );

      // 紧凑模式下显示分类名称，但不显示描述
      expect(screen.getByText('Test Category')).toBeInTheDocument();
      expect(screen.queryByText('Test Description')).not.toBeInTheDocument();
    });

    it('应该在紧凑模式下应用不同的样式', () => {
      const { container } = render(
        <TestWrapper>
          <CategoryCard category={mockCategory} showCompactDetails={true} />
        </TestWrapper>
      );

      // 紧凑模式下不应该有aspect-square类
      const cardElement = container.querySelector('a');
      expect(cardElement?.className).not.toContain('aspect-square');
    });
  });

  describe('图片加载错误处理', () => {
    it('应该在图片加载失败时显示占位符', async () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      
      // 模拟图片加载错误
      fireEvent.error(image);

      await waitFor(() => {
        expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      });
    });

    it('应该在分类变化时重置错误状态', async () => {
      const { rerender } = render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      fireEvent.error(image);

      await waitFor(() => {
        expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
      });

      // 更改分类
      const newCategory = {
        ...mockCategory,
        id: '2',
        thumbnail_url: 'http://localhost:8000/thumbnails/new.jpg',
      };

      rerender(
        <TestWrapper>
          <CategoryCard category={newCategory} />
        </TestWrapper>
      );

      // 应该重新显示图片
      expect(screen.getByRole('img')).toBeInTheDocument();
    });
  });

  describe('URL转换测试', () => {
    it('应该正确转换绝对URL为相对URL', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/test.jpg');
    });

    it('应该保持相对URL不变', () => {
      const categoryWithRelativeUrl = {
        ...mockCategory,
        thumbnail_url: '/thumbnails/relative.jpg',
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithRelativeUrl} />
        </TestWrapper>
      );

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('src', '/thumbnails/relative.jpg');
    });

    it('应该处理空的thumbnail_url', () => {
      const categoryWithEmptyUrl = {
        ...mockCategory,
        thumbnail_url: '',
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithEmptyUrl} />
        </TestWrapper>
      );

      expect(screen.getByTestId('image-placeholder-icon')).toBeInTheDocument();
    });
  });

  describe('动画和样式测试', () => {
    it('应该应用正确的CSS类', () => {
      const { container } = render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      // 检查外层容器的类
      const outerDiv = container.firstChild as HTMLElement;
      expect(outerDiv).toHaveClass('block');
      expect(outerDiv).toHaveClass('group');
      expect(outerDiv).toHaveClass('aspect-square');

      // 检查链接元素的类
      const linkElement = container.querySelector('a');
      expect(linkElement).toHaveClass('block');
      expect(linkElement).toHaveClass('w-full');
      expect(linkElement).toHaveClass('h-full');
    });

    it('应该在hover时应用变换效果', () => {
      const { container } = render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const imageElement = container.querySelector('img');
      expect(imageElement).toHaveClass('group-hover:scale-105');
    });
  });

  describe('显示索引测试', () => {
    it('应该支持显示索引属性', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} displayIndex={1} showCompactDetails={true} />
        </TestWrapper>
      );

      // 在紧凑模式下应该显示索引号
      expect(screen.getByText('No. 001')).toBeInTheDocument();
      expect(screen.getByText('Test Category')).toBeInTheDocument();
    });
  });

  describe('边界情况测试', () => {
    it('应该处理长分类名称', () => {
      const categoryWithLongName = {
        ...mockCategory,
        name: 'This is a very long category name that might overflow the container',
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithLongName} showCompactDetails={true} />
        </TestWrapper>
      );

      expect(screen.getByText(categoryWithLongName.name)).toBeInTheDocument();
    });

    it('应该处理空描述', () => {
      const categoryWithoutDescription = {
        ...mockCategory,
        description: null,
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithoutDescription} showCompactDetails={true} />
        </TestWrapper>
      );

      // CategoryCard不显示描述，只显示名称
      expect(screen.getByText('Test Category')).toBeInTheDocument();
      expect(screen.queryByText('Test Description')).not.toBeInTheDocument();
    });

    it('应该处理特殊字符', () => {
      const categoryWithSpecialChars = {
        ...mockCategory,
        name: 'Category & <Special> "Characters"',
        description: 'Description with <HTML> & special chars',
      };

      render(
        <TestWrapper>
          <CategoryCard category={categoryWithSpecialChars} showCompactDetails={true} />
        </TestWrapper>
      );

      expect(screen.getByText('Category & <Special> "Characters"')).toBeInTheDocument();
      // CategoryCard不显示描述
      expect(screen.queryByText('Description with <HTML> & special chars')).not.toBeInTheDocument();
    });
  });

  describe('可访问性测试', () => {
    it('应该有正确的ARIA属性', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const link = screen.getByRole('link');
      expect(link).toHaveAttribute('href', '/categories/1');

      const image = screen.getByRole('img');
      expect(image).toHaveAttribute('alt', 'Test Category');
    });

    it('应该支持键盘导航', () => {
      render(
        <TestWrapper>
          <CategoryCard category={mockCategory} />
        </TestWrapper>
      );

      const link = screen.getByRole('link');
      expect(link).toBeVisible();
      
      // 链接应该可以获得焦点
      link.focus();
      expect(document.activeElement).toBe(link);
    });
  });
});
