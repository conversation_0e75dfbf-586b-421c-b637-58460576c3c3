/**
 * TagPage组件测试套件
 * 测试标签页面的渲染和交互功能
 */

import React from 'react';
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { MemoryRouter, Route, Routes } from 'react-router-dom';
import TagPage from '../../components/TagPage';
import { ThemeProvider } from '../../contexts/ThemeContext';
import { AuthProvider } from '../../contexts/AuthContext';
import * as apiService from '../../services/api';
import type { ImageRead, ApiError } from '../../types';

// Mock API服务
vi.mock('../../services/api', () => ({
  searchImagesByTag: vi.fn(),
}));

// Mock framer-motion
vi.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    h1: ({ children, ...props }: any) => <h1 {...props}>{children}</h1>,
  },
}));

// Mock react-masonry-css
vi.mock('react-masonry-css', () => ({
  default: ({ children, className }: any) => (
    <div className={className} data-testid="masonry-container">
      {children}
    </div>
  ),
}));

// Mock子组件
vi.mock('../../components/ErrorDisplay', () => ({
  default: ({ error }: { error: any }) => (
    <div data-testid="error-display">{error.message || error}</div>
  ),
}));

vi.mock('../../components/ImageCard', () => ({
  default: ({ image, onClick }: { image: ImageRead; onClick: () => void }) => (
    <div data-testid={`image-card-${image.id}`} onClick={onClick}>
      {image.title}
    </div>
  ),
}));

vi.mock('../../components/ImageCardSkeleton', () => ({
  default: () => <div data-testid="image-card-skeleton">Loading image...</div>,
}));

vi.mock('../../components/ImageDetailModal', () => ({
  default: ({ isOpen, onClose, image }: any) =>
    isOpen ? (
      <div data-testid="image-detail-modal">
        <button data-testid="modal-close" onClick={onClose}>
          Close
        </button>
        <div data-testid="modal-image-title">{image?.title}</div>
      </div>
    ) : null,
}));

vi.mock('../../components/icons', () => ({
  PhotoIcon: () => <div data-testid="photo-icon">Photo Icon</div>,
}));

// Mock sessionStorage
const mockSessionStorage = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn(),
};

Object.defineProperty(window, 'sessionStorage', {
  value: mockSessionStorage,
});

// Mock window.scrollTo
Object.defineProperty(window, 'scrollTo', {
  value: vi.fn(),
});

// 测试包装组件
const TestWrapper: React.FC<{ children: React.ReactNode; initialPath?: string }> = ({
  children,
  initialPath = '/tags/nature',
}) => (
  <MemoryRouter initialEntries={[initialPath]}>
    <AuthProvider>
      <ThemeProvider>
        <Routes>
          <Route path="/tags/:tagName" element={children} />
        </Routes>
      </ThemeProvider>
    </AuthProvider>
  </MemoryRouter>
);

describe('TagPage组件测试套件', () => {
  const mockSearchImagesByTag = vi.mocked(apiService.searchImagesByTag);

  const mockImages: ImageRead[] = [
    {
      id: '1',
      category_id: '1',
      title: 'Nature Image 1',
      original_filename: 'nature1.jpg',
      stored_filename: 'stored1.jpg',
      relative_file_path: '/images/nature1.jpg',
      relative_thumbnail_path: '/thumbnails/nature1.jpg',
      mime_type: 'image/jpeg',
      size_bytes: 1024000,
      description: 'Beautiful nature scene',
      tags: [{ id: '1', name: 'nature', created_at: '2025-01-01T00:00:00Z', updated_at: '2025-01-01T00:00:00Z' }],
      created_at: '2025-01-01T00:00:00Z',
      updated_at: '2025-01-01T00:00:00Z',
      file_metadata: {},
      exif_info: null,
      image_url: '/images/nature1.jpg',
      thumbnail_url: '/thumbnails/nature1.jpg',
      set_as_category_thumbnail: false,
    },
    {
      id: '2',
      category_id: '1',
      title: 'Nature Image 2',
      original_filename: 'nature2.jpg',
      stored_filename: 'stored2.jpg',
      relative_file_path: '/images/nature2.jpg',
      relative_thumbnail_path: '/thumbnails/nature2.jpg',
      mime_type: 'image/jpeg',
      size_bytes: 2048000,
      description: 'Another nature scene',
      tags: [{ id: '1', name: 'nature', created_at: '2025-01-01T00:00:00Z', updated_at: '2025-01-01T00:00:00Z' }],
      created_at: '2025-01-02T00:00:00Z',
      updated_at: '2025-01-02T00:00:00Z',
      file_metadata: {},
      exif_info: null,
      image_url: '/images/nature2.jpg',
      thumbnail_url: '/thumbnails/nature2.jpg',
      set_as_category_thumbnail: false,
    },
  ];

  beforeEach(() => {
    vi.clearAllMocks();
    mockSessionStorage.getItem.mockReturnValue(null);
  });

  describe('基础渲染测试', () => {
    it('应该显示加载状态', () => {
      mockSearchImagesByTag.mockImplementation(() => new Promise(() => {})); // 永不resolve

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      expect(screen.getAllByTestId('image-card-skeleton')).toHaveLength(20); // ITEMS_PER_PAGE
    });

    it('应该成功加载并显示标签图片', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Images tagged with "nature"')).toBeInTheDocument();
      });

      expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      expect(screen.getByTestId('image-card-2')).toBeInTheDocument();
      expect(screen.getByText('2 images found')).toBeInTheDocument();
    });

    it('应该显示错误状态', async () => {
      const mockError: ApiError = {
        type: 'network',
        message: 'Failed to load images',
        timestamp: new Date().toISOString(),
      };

      mockSearchImagesByTag.mockRejectedValue(mockError);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toBeInTheDocument();
      });

      expect(screen.getByText('Failed to load images')).toBeInTheDocument();
    });

    it('应该显示空状态', async () => {
      mockSearchImagesByTag.mockResolvedValue([]);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Images tagged with "nature"')).toBeInTheDocument();
      });

      expect(screen.getByTestId('photo-icon')).toBeInTheDocument();
      expect(screen.getByText('No images found for this tag')).toBeInTheDocument();
    });
  });

  describe('标签名称解码测试', () => {
    it('应该正确解码URL编码的标签名', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper initialPath="/tags/%E8%87%AA%E7%84%B6"> {/* "自然" 的URL编码 */}
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Images tagged with "自然"')).toBeInTheDocument();
      });

      expect(mockSearchImagesByTag).toHaveBeenCalledWith('自然');
    });

    it('应该处理解码失败的情况', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      // 模拟解码失败
      const originalDecodeURIComponent = global.decodeURIComponent;
      global.decodeURIComponent = vi.fn().mockImplementation(() => {
        throw new Error('Decode failed');
      });

      render(
        <TestWrapper initialPath="/tags/invalid%encoding">
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Images tagged with "invalid%encoding"')).toBeInTheDocument();
      });

      // 恢复原函数
      global.decodeURIComponent = originalDecodeURIComponent;
    });
  });

  describe('图片交互测试', () => {
    it('应该点击图片打开详情模态框', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('image-card-1'));

      expect(screen.getByTestId('image-detail-modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal-image-title')).toHaveTextContent('Nature Image 1');
    });

    it('应该关闭图片详情模态框', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      });

      fireEvent.click(screen.getByTestId('image-card-1'));
      expect(screen.getByTestId('image-detail-modal')).toBeInTheDocument();

      fireEvent.click(screen.getByTestId('modal-close'));
      expect(screen.queryByTestId('image-detail-modal')).not.toBeInTheDocument();
    });
  });

  describe('分页和加载更多测试', () => {
    it('应该支持加载更多图片', async () => {
      // 创建足够多的图片来测试分页
      const manyImages = Array.from({ length: 25 }, (_, i) => ({
        ...mockImages[0],
        id: `${i + 1}`,
        title: `Image ${i + 1}`,
      }));

      mockSearchImagesByTag.mockResolvedValue(manyImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('25 images found')).toBeInTheDocument();
      });

      // 初始应该只显示20个图片
      expect(screen.getAllByTestId(/^image-card-/)).toHaveLength(20);

      // 查找并点击"Load More"按钮
      const loadMoreButton = screen.getByRole('button', { name: /load more/i });
      fireEvent.click(loadMoreButton);

      // 现在应该显示所有25个图片
      expect(screen.getAllByTestId(/^image-card-/)).toHaveLength(25);
    });

    it('应该在显示所有图片时隐藏加载更多按钮', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages); // 只有2个图片

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('2 images found')).toBeInTheDocument();
      });

      // 应该没有"Load More"按钮
      expect(screen.queryByRole('button', { name: /load more/i })).not.toBeInTheDocument();
    });
  });

  describe('会话状态管理测试', () => {
    it('应该保存滚动位置到sessionStorage', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      });

      // 模拟滚动
      fireEvent.scroll(window, { target: { scrollY: 500 } });

      // 应该保存滚动位置
      expect(mockSessionStorage.setItem).toHaveBeenCalledWith(
        'tagPageScrollPos_nature',
        '500'
      );
    });

    it('应该从sessionStorage恢复滚动位置', async () => {
      mockSessionStorage.getItem.mockImplementation((key) => {
        if (key === 'tagPageScrollPos_nature') return '300';
        if (key === 'tagPageItemCount_nature') return '40';
        return null;
      });

      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('image-card-1')).toBeInTheDocument();
      });

      // 应该恢复滚动位置
      expect(window.scrollTo).toHaveBeenCalledWith(0, 300);
    });

    it('应该保存和恢复显示数量', async () => {
      mockSessionStorage.getItem.mockImplementation((key) => {
        if (key === 'tagPageItemCount_nature') return '40';
        return null;
      });

      const manyImages = Array.from({ length: 50 }, (_, i) => ({
        ...mockImages[0],
        id: `${i + 1}`,
        title: `Image ${i + 1}`,
      }));

      mockSearchImagesByTag.mockResolvedValue(manyImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('50 images found')).toBeInTheDocument();
      });

      // 应该显示40个图片（从sessionStorage恢复）
      expect(screen.getAllByTestId(/^image-card-/)).toHaveLength(40);
    });
  });

  describe('Masonry布局测试', () => {
    it('应该使用Masonry组件进行布局', async () => {
      mockSearchImagesByTag.mockResolvedValue(mockImages);

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('masonry-container')).toBeInTheDocument();
      });
    });
  });

  describe('边界情况测试', () => {
    it('应该处理空的标签名', async () => {
      mockSearchImagesByTag.mockResolvedValue([]);

      render(
        <TestWrapper initialPath="/tags/">
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByText('Images tagged with ""')).toBeInTheDocument();
      });
    });

    it('应该处理API调用失败', async () => {
      mockSearchImagesByTag.mockRejectedValue(new Error('Network error'));

      render(
        <TestWrapper>
          <TagPage />
        </TestWrapper>
      );

      await waitFor(() => {
        expect(screen.getByTestId('error-display')).toBeInTheDocument();
      });
    });

    it('应该处理sessionStorage访问失败', async () => {
      mockSessionStorage.getItem.mockImplementation(() => {
        throw new Error('SessionStorage not available');
      });

      mockSearchImagesByTag.mockResolvedValue(mockImages);

      expect(() => {
        render(
          <TestWrapper>
            <TagPage />
          </TestWrapper>
        );
      }).not.toThrow();
    });
  });
});
