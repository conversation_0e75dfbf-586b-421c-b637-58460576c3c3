import React, { useState, useEffect, useCallback } from 'react';
import { HashRouter, Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import CategoryList from './components/CategoryList';
import CategoryDetail from './components/CategoryDetail';
import AnalyticsPage from './components/AnalyticsPage';
import LoginPage from './components/LoginPage';
import TagPage from './components/TagPage';
import SplashScreen from './components/SplashScreen';
import ShortcutsModal from './components/ShortcutsModal';
import WelcomeGuide from './components/WelcomeGuide';

import { CategoryProvider } from './contexts/CategoryContext';
import { AuthProvider } from './contexts/AuthContext';
import { useSplashAnimation, LoadingTask } from './hooks/useSplashAnimation';
import { IS_ELECTRON } from './constants';

const App: React.FC = () => {
  const [isAppReady, setIsAppReady] = useState(false);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const [showShortcuts, setShowShortcuts] = useState(false);
  const [showWelcomeGuide, setShowWelcomeGuide] = useState(false);

  // 检查是否是首次使用
  useEffect(() => {
    if (IS_ELECTRON && isAppReady) {
      const hasSeenGuide = localStorage.getItem('hasSeenWelcomeGuide');
      if (!hasSeenGuide) {
        setShowWelcomeGuide(true);
      }
    }
  }, [isAppReady]);

  // 处理菜单事件
  useEffect(() => {
    if (IS_ELECTRON && window.electronAPI?.onMenuAction) {
      const handleMenuAction = (action: string, data?: any) => {
        console.log('Menu action received:', action, data);
        
        switch (action) {
          case 'show-shortcuts':
            setShowShortcuts(true);
            break;
          case 'new-category':
            // 这里可以触发新建分类的模态框
            // 目前先跳转到主页
            window.location.hash = '/';
            break;
          case 'import-images':
            // 处理图片导入
            if (data && data.length > 0) {
              console.log('Import images:', data);
              // 这里可以实现批量导入逻辑
            }
            break;
          case 'search':
            // 触发搜索功能
            console.log('Trigger search');
            break;
          default:
            console.log('Unknown menu action:', action);
        }
      };

      window.electronAPI.onMenuAction(handleMenuAction);

      return () => {
        window.electronAPI.removeAllListeners?.('menu-action');
      };
    }
  }, []);

  // 监听应用就绪事件
  useEffect(() => {
    if (IS_ELECTRON && window.electronAPI?.onAppReady) {
      const handleAppReady = () => {
        console.log('App ready event received from main process');
      };

      window.electronAPI.onAppReady(handleAppReady);

      return () => {
        window.electronAPI.removeAllListeners?.('app-ready');
      };
    }
  }, []);

  // 关闭欢迎指南
  const handleCloseWelcomeGuide = () => {
    setShowWelcomeGuide(false);
    localStorage.setItem('hasSeenWelcomeGuide', 'true');
  };

  // 模拟数据加载函数
  const loadPokemonData = async (): Promise<void> => {
    // 模拟API调用 - 加载宝可梦基础数据
    await new Promise(resolve => setTimeout(resolve, 800 + Math.random() * 400));
    console.log('Pokemon data loaded');
  };

  const loadUserSettings = async (): Promise<void> => {
    // 模拟用户设置加载
    await new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 200));
    console.log('User settings loaded');
  };

  const initializeCache = async (): Promise<void> => {
    // 模拟缓存初始化
    await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 100));
    console.log('Cache initialized');
  };

  const preloadCriticalAssets = async (): Promise<void> => {
    // 模拟关键资源预加载
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 300));
    console.log('Critical assets preloaded');
  };

  // 定义加载任务
  const loadingTasks: LoadingTask[] = [
    {
      id: 'pokemon-data',
      name: '加载宝可梦数据',
      loader: loadPokemonData,
      weight: 0.4 // 40%权重
    },
    {
      id: 'user-settings',
      name: '加载用户设置',
      loader: loadUserSettings,
      weight: 0.2 // 20%权重
    },
    {
      id: 'cache-init',
      name: '初始化缓存',
      loader: initializeCache,
      weight: 0.2 // 20%权重
    },
    {
      id: 'critical-assets',
      name: '预加载关键资源',
      loader: preloadCriticalAssets,
      weight: 0.2 // 20%权重
    }
  ];

  // 配置开屏动画 - 启用动态加载模式
  const splashAnimation = useSplashAnimation({
    enableDynamicLoading: true, // 启用动态加载
    loadingTasks,              // 加载任务列表
    minDuration: 1500,         // 最少显示1.5秒
    maxTimeout: 8000,          // 最多显示8秒
    animationWeight: 0.3,      // 动画占30%
    dataWeight: 0.7,           // 数据加载占70%
    autoHide: true,
    onPhaseChange: (phase) => {
      console.log('Splash animation phase:', phase);
    },
    onProgressChange: (progress) => {
      console.log('Loading progress:', progress.toFixed(1) + '%');
    },
    onComplete: () => {
      console.log('Splash animation completed');
      setIsAppReady(true);
      
      // 检查是否需要显示欢迎引导
      const hasSeenWelcomeGuide = localStorage.getItem('hasSeenWelcomeGuide');
      if (!hasSeenWelcomeGuide) {
        setShowWelcomeGuide(true);
      }
    },
    onError: (error) => {
      console.error('Splash animation error:', error);
      // 出错时也要显示主应用
      setIsAppReady(true);
    }
  });

  // 添加超时保护机制，防止首屏动画卡死
  useEffect(() => {
    const maxWaitTime = 10000; // 最多等待10秒
    const timeoutId = setTimeout(() => {
      if (!isAppReady) {
        console.warn('Splash animation timeout, forcing app to show');
        setIsAppReady(true);
      }
    }, maxWaitTime);

    return () => clearTimeout(timeoutId);
  }, [isAppReady]);

  // 不再需要单独的数据预加载逻辑，已集成到splash动画中

  // 手动跳过开屏动画的处理函数
  const handleSkipSplash = useCallback(() => {
    splashAnimation.hide();
    setIsAppReady(true);
  }, [splashAnimation]);

  // 如果开屏动画还在显示，渲染开屏动画
  if (!splashAnimation.isHidden && !isAppReady) {
    return (
      <SplashScreen
        onAnimationComplete={handleSkipSplash}
        progress={splashAnimation.progress}
        phase={splashAnimation.phase}
        config={{
          title: 'Pokedex',
          description: `加载中... ${splashAnimation.progress.toFixed(0)}%`,
          duration: 0, // 动态模式下不使用固定时长
          showTime: 0  // 动态模式下不使用固定显示时间
        }}
        loadingTasks={loadingTasks}
        loadingStatus={splashAnimation.loadingStatus}
      />
    );
  }

  // 主应用渲染
  return (
    <>
      <HashRouter>
        <AuthProvider>
          <CategoryProvider>
            <Layout>
              <Routes>
                <Route path='/' element={<CategoryList />} />
                <Route path='/categories' element={<Navigate to='/' replace />} />
                <Route path='/categories/:categoryId' element={<CategoryDetail />} />
                <Route path='/tags/:tagName' element={<TagPage />} />
                <Route path='/species' element={<AnalyticsPage />} />
                <Route path='/login' element={<LoginPage />} />
                <Route path='*' element={<Navigate to='/' replace />} />
              </Routes>
            </Layout>
          </CategoryProvider>
        </AuthProvider>
      </HashRouter>

      {/* Electron专用模态框 */}
      {IS_ELECTRON && (
        <>
          <ShortcutsModal
            isOpen={showShortcuts}
            onClose={() => setShowShortcuts(false)}
          />
          <WelcomeGuide
            isOpen={showWelcomeGuide}
            onClose={handleCloseWelcomeGuide}
          />
        </>
      )}
    </>
  );
};

export default App;
