# 图片删除功能完善与测试覆盖计划

## 目标概述
完善图片删除功能，确保删除图片时能正确删除本地或OSS上的文件，并提供100%的测试覆盖率。

## 当前状况分析
根据代码审查，`electron/services/ImageService.ts`中的`deleteImage()`方法已经实现了基本的删除逻辑：
- ✅ 支持本地存储文件删除
- ✅ 支持OSS存储文件删除  
- ✅ 包含错误处理机制
- ✅ 数据库记录删除

## 需要完善的问题
1. **错误处理不够完善**：OSS删除失败时只记录日志，没有用户反馈
2. **批量删除缺失**：没有批量删除图片的功能
3. **删除确认机制**：缺少删除前的确认对话框
4. **测试覆盖不足**：删除功能的测试用例不完整
5. **日志记录不详细**：删除操作的审计日志不够详细

## 详细任务清单

### 📋 **阶段一：核心删除逻辑完善**

#### [ ] 1. 增强ImageService删除错误处理
**文件**: `electron/services/ImageService.ts`
**目标**: 完善deleteImage方法的错误处理和用户反馈
- 改进OSS删除失败的错误处理
- 添加详细的删除操作日志
- 实现删除操作的回滚机制（数据库操作失败时）
- 返回详细的删除结果状态

#### [ ] 2. 实现批量删除功能
**文件**: `electron/services/ImageService.ts`
**目标**: 添加批量删除图片的功能
- 实现`deleteImages(imageIds: number[])`方法
- 支持本地和OSS存储的批量删除
- 添加批量操作的事务处理
- 提供批量删除的进度反馈

#### [ ] 3. 添加删除前验证机制
**文件**: `electron/services/ImageService.ts`
**目标**: 增加删除前的数据验证
- 验证图片是否存在于数据库
- 检查图片文件是否存在于存储中
- 验证用户权限（如果有权限系统）
- 记录删除操作的审计日志

### 📋 **阶段二：IPC接口扩展**

#### [ ] 4. 扩展IPC处理器支持批量删除
**文件**: `electron/main.ts`
**目标**: 添加批量删除的IPC接口
- 添加`delete-images`IPC处理器
- 实现删除进度的实时反馈
- 添加删除确认对话框的IPC接口
- 统一错误处理和用户通知

#### [ ] 5. 更新预加载脚本API
**文件**: `electron/preload.ts`
**目标**: 扩展前端可用的删除API
- 添加批量删除API接口
- 添加删除确认对话框API
- 更新TypeScript类型定义
- 确保API的类型安全

### 📋 **阶段三：实际应用场景集成**

#### [ ] 6. 完善分类删除时的图片批量删除
**文件**: `electron/services/CategoryService.ts`
**目标**: 删除分类时同步删除该分类下的所有图片文件
- 在`deleteCategory()`方法中集成图片批量删除逻辑
- 确保删除分类前先删除所有关联图片的存储文件
- 添加分类删除的事务处理（图片删除失败时回滚）
- 提供删除进度反馈和错误处理

#### [ ] 7. 优化单个图片删除的用户体验
**文件**: `services/api.ts`
**目标**: 改进单个图片删除的用户反馈
- 优化`deleteImage()`的错误消息显示
- 添加删除成功的用户反馈
- 确保删除操作的加载状态显示
- 统一错误处理格式

#### [ ] 8. 更新相关IPC接口
**文件**: `electron/main.ts`
**目标**: 确保分类删除的IPC接口支持图片文件删除
- 检查`delete-category`IPC处理器是否正确调用图片删除
- 添加删除进度的实时反馈机制
- 统一分类和图片删除的错误处理
- 确保操作的原子性

### 📋 **阶段四：全面测试覆盖**

#### [ ] 8. 创建删除功能单元测试
**文件**: `__tests__/electron/services/ImageService.delete.test.ts`
**目标**: 为删除功能创建全面的单元测试
- 测试单个图片删除（本地存储）
- 测试单个图片删除（OSS存储）
- 测试批量删除功能
- 测试删除失败的错误处理
- 测试数据库事务回滚

#### [ ] 9. 创建删除功能集成测试
**文件**: `__tests__/electron/integration/delete-workflow.test.ts`
**目标**: 测试完整的删除工作流程
- 测试从上传到删除的完整流程
- 测试存储类型切换时的删除行为
- 测试并发删除操作
- 测试删除操作的数据一致性

#### [ ] 10. 创建删除功能性能测试
**文件**: `__tests__/electron/performance/delete-performance.test.ts`
**目标**: 确保删除操作的性能表现
- 测试大量图片的批量删除性能
- 测试OSS删除操作的超时处理
- 测试内存使用情况
- 建立性能基准线

#### [ ] 11. 创建边界情况测试
**文件**: `__tests__/electron/edge-cases/delete-edge-cases.test.ts`
**目标**: 测试删除功能的边界情况
- 测试删除不存在的图片
- 测试网络断开时的OSS删除
- 测试磁盘空间不足时的删除
- 测试并发删除同一图片的情况

### 📋 **阶段五：文档和优化**

#### [ ] 12. 更新API文档
**文件**: `CLAUDE.md`
**目标**: 更新文档反映新的删除功能
- 记录新增的删除API
- 更新架构说明
- 添加删除功能的使用指南
- 更新测试覆盖率信息

## 技术实现细节

### 删除操作流程设计
```typescript
// 单个删除流程
1. 验证图片存在性
2. 检查存储类型
3. 删除存储文件（本地/OSS）
4. 删除数据库记录
5. 返回操作结果

// 批量删除流程
1. 验证所有图片存在性
2. 按存储类型分组
3. 并行删除存储文件
4. 事务删除数据库记录
5. 返回批量操作结果
```

### 错误处理策略
```typescript
interface DeleteResult {
  success: boolean;
  imageId: number;
  error?: string;
  storageDeleted: boolean;
  databaseDeleted: boolean;
}

interface BatchDeleteResult {
  totalCount: number;
  successCount: number;
  failedCount: number;
  results: DeleteResult[];
}
```

### 测试覆盖目标
- **单元测试覆盖率**: 100%
- **集成测试覆盖率**: 95%
- **边界情况覆盖**: 90%
- **性能测试**: 关键操作基准

## 验收标准

1. ✅ 单个图片删除功能完善，支持本地和OSS存储
2. ✅ 批量删除功能实现，支持进度反馈
3. ✅ 删除确认机制和用户体验优化
4. ✅ 完整的错误处理和回滚机制
5. ✅ 100%的测试覆盖率
6. ✅ 性能测试通过，满足基准要求
7. ✅ 文档更新完整

## 实施约束

- **保持API兼容性**: 现有删除API接口不能破坏性变更
- **性能要求**: 单个删除操作 < 2秒，批量删除 < 10秒
- **错误恢复**: 所有删除操作支持回滚和重试
- **用户体验**: 提供清晰的进度反馈和错误信息

---

**开发负责人**: Claude Code Assistant  
**计划创建时间**: 2025-01-27  
**预计完成时间**: 待定  
**完成进度**: 0% (0/12 任务完成)
