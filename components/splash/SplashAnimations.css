/* ==============================================
   开屏动画统一样式文件 (SplashAnimations.css)
   包含所有CSS keyframes动画和响应式媒体查询适配
   ============================================== */

/* ==================== 核心动画定义 ==================== */

/* 网格移动动画 */
@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(30px, 30px);
  }
}

/* 360度旋转动画 */
@keyframes rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 光晕脉冲动画 */
@keyframes glowPulse {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.1);
  }
}

/* Logo浮动动画 */
@keyframes logoFloat {
  0%, 100% {
    transform: translateY(0) scale(1);
  }
  50% {
    transform: translateY(-8px) scale(1.02);
  }
}

/* 标题滑入动画 */
@keyframes titleSlide {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 线条生长动画 */
@keyframes lineGrow {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 3rem;
    opacity: 1;
  }
}

/* 副标题渐现动画 */
@keyframes subtitleFade {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 进度条移动动画 */
@keyframes progressMove {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(400%);
  }
}

/* 进度条光效动画 */
@keyframes progressGlow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(153, 153, 153, 0.3);
  }
  50% {
    box-shadow: 0 0 15px rgba(153, 153, 153, 0.6);
  }
}

/* 淡入动画 */
@keyframes splashFadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* 淡出动画 */
@keyframes splashFadeOut {
  0% {
    opacity: 1;
  }
  100% {
    opacity: 0;
  }
}

/* ==================== 开屏动画基础样式 ==================== */

.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: #0a0a0a;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  overflow: hidden;
  animation: splashFadeIn 0.3s ease-in-out;
}

.splash-screen.fade-out {
  animation: splashFadeOut 0.5s ease-in-out forwards;
}

/* 背景层样式 */
.geometric-background,
.grid-background,
.rotating-light {
  pointer-events: none;
}

.geometric-background {
  z-index: 1;
}

.grid-background {
  z-index: 2;
}

.rotating-light {
  z-index: 3;
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
}

/* 内容层样式 */
.splash-content {
  text-align: center;
  z-index: 10;
  position: relative;
}

/* Logo相关样式 */
.logo-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 2rem;
}

.logo-glow {
  will-change: transform, opacity;
  transform: translateZ(0);
}

.logo {
  will-change: transform;
  transform: translateZ(0);
}

/* 标题相关样式 */
.app-title {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.app-title-heading,
.app-title-description {
  will-change: transform, opacity;
}

.title-line {
  will-change: width, opacity;
}

/* 进度条相关样式 */
.progress-area {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  margin-top: 1.5rem;
  justify-content: center;
}

.progress-fill {
  will-change: transform, width;
  transform: translateZ(0);
}

/* ==================== 响应式设计 ==================== */

/* 中等屏幕适配 (640px以下) */
@media (max-width: 640px) {
  .splash-content h1,
  .app-title-heading {
    font-size: 1.875rem !important;
  }
  
  .splash-content p,
  .app-title-description {
    font-size: 0.875rem !important;
  }
  
  .progress-bar {
    width: 10rem !important;
  }
}

/* 小屏幕适配 (480px以下) */
@media (max-width: 480px) {
  .splash-content h1,
  .app-title-heading {
    font-size: 1.5rem !important;
  }
  
  .splash-content,
  .app-title {
    gap: 0.75rem !important;
  }
  
  .progress-area {
    gap: 1rem !important;
  }
  
  .logo-container .logo {
    width: 58px !important; /* 72px * 0.8 */
    height: 58px !important;
  }
  
  .logo-container .logo-glow {
    width: 102px !important; /* 128px * 0.8 */
    height: 102px !important;
  }
}

/* 超小屏幕适配 (360px以下) */
@media (max-width: 360px) {
  .splash-content h1,
  .app-title-heading {
    font-size: 1.25rem !important;
  }
  
  .progress-bar {
    width: 8rem !important;
  }
  
  .logo-container .logo {
    width: 50px !important;
    height: 50px !important;
  }
  
  .logo-container .logo svg {
    width: 22px !important;
    height: 22px !important;
  }
}

/* ==================== 可访问性支持 ==================== */

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .geometric-background,
  .grid-background,
  .rotating-light,
  .logo-glow,
  .logo,
  .app-title-heading,
  .title-line,
  .app-title-description,
  .progress-fill {
    animation: none !important;
    opacity: 1 !important;
    transform: none !important;
  }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
  .splash-screen {
    background: #000000;
  }
  
  .progress-bar {
    border: 1px solid #666;
  }
  
  .progress-fill {
    background: linear-gradient(90deg, 
      transparent, 
      #ffffff, 
      #ffffff, 
      #ffffff, 
      transparent
    ) !important;
  }
  
  .logo {
    border: 2px solid #ffffff !important;
  }
  
  .app-title-heading {
    color: #ffffff !important;
  }
  
  .app-title-description {
    color: #cccccc !important;
  }
}

/* 暗色模式特定调整 */
@media (prefers-color-scheme: dark) {
  .splash-screen {
    background: #000000;
  }
}

/* ==================== 性能优化 ==================== */

/* GPU加速优化 */
.splash-screen,
.geometric-background,
.grid-background,
.rotating-light,
.logo-glow,
.logo,
.progress-fill {
  transform: translateZ(0);
}

/* 防止动画卡顿 */
.rotating-light,
.logo,
.logo-glow {
  backface-visibility: hidden;
}

/* ==================== 打印样式 ==================== */

@media print {
  .splash-screen {
    display: none !important;
  }
} 